from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QTextEdit, QPushButton,
                             QListWidget, QListWidgetItem, QMessageBox,
                             QComboBox, QGroupBox)
from PyQt6.QtCore import Qt

class MetadataEditor(QDialog):
    """محرر البيانات الوصفية للمستند"""

    def __init__(self, parent, database, document):
        """تهيئة محرر البيانات الوصفية"""
        super().__init__(parent)

        self.db = database
        self.document = document
        self.custom_metadata = {}

        self.init_ui()
        self.load_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل المستند")
        self.setMinimumSize(600, 500)

        layout = QVBoxLayout(self)

        # البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)

        self.title_input = QLineEdit()
        basic_layout.addRow("العنوان:", self.title_input)

        self.description_input = QTextEdit()
        basic_layout.addRow("الوصف:", self.description_input)

        self.keywords_input = QLineEdit()
        basic_layout.addRow("الكلمات المفتاحية:", self.keywords_input)

        layout.addWidget(basic_group)

        # التصنيفات
        categories_group = QGroupBox("التصنيفات")
        categories_layout = QVBoxLayout(categories_group)

        self.categories_list = QListWidget()
        categories_layout.addWidget(self.categories_list)

        categories_buttons_layout = QHBoxLayout()

        self.add_category_btn = QPushButton("إضافة")
        self.add_category_btn.clicked.connect(self.add_category)
        categories_buttons_layout.addWidget(self.add_category_btn)

        self.remove_category_btn = QPushButton("إزالة")
        self.remove_category_btn.clicked.connect(self.remove_category)
        categories_buttons_layout.addWidget(self.remove_category_btn)

        categories_layout.addLayout(categories_buttons_layout)

        layout.addWidget(categories_group)

        # البيانات الوصفية المخصصة
        metadata_group = QGroupBox("البيانات الوصفية المخصصة")
        metadata_layout = QVBoxLayout(metadata_group)

        self.metadata_form = QFormLayout()
        metadata_layout.addLayout(self.metadata_form)

        # إضافة حقل جديد
        add_field_layout = QHBoxLayout()

        self.field_name_input = QLineEdit()
        self.field_name_input.setPlaceholderText("اسم الحقل")
        add_field_layout.addWidget(self.field_name_input)

        self.field_value_input = QLineEdit()
        self.field_value_input.setPlaceholderText("قيمة الحقل")
        add_field_layout.addWidget(self.field_value_input)

        self.add_field_btn = QPushButton("إضافة حقل")
        self.add_field_btn.clicked.connect(self.add_metadata_field)
        add_field_layout.addWidget(self.add_field_btn)

        metadata_layout.addLayout(add_field_layout)

        layout.addWidget(metadata_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_changes)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

    def load_data(self):
        """تحميل بيانات المستند"""
        # البيانات الأساسية
        self.title_input.setText(self.document['title'])
        self.description_input.setText(self.document['description'] or "")
        self.keywords_input.setText(self.document['keywords'] or "")

        # التصنيفات
        self.load_categories()

        # البيانات الوصفية المخصصة
        self.load_metadata()

    def load_categories(self):
        """تحميل تصنيفات المستند"""
        self.categories_list.clear()

        document_categories = self.db.get_document_categories(self.document['id'])

        for category in document_categories:
            item = QListWidgetItem(category['name'])
            item.setData(Qt.ItemDataRole.UserRole, category['id'])
            self.categories_list.addItem(item)

    def load_metadata(self):
        """تحميل البيانات الوصفية المخصصة"""
        # مسح الحقول السابقة
        while self.metadata_form.count():
            item = self.metadata_form.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # الحصول على البيانات الوصفية
        self.custom_metadata = self.db.get_custom_metadata(self.document['id'])

        # عرض البيانات الوصفية
        for field_name, field_value in self.custom_metadata.items():
            field_layout = QHBoxLayout()

            value_input = QLineEdit(field_value)
            value_input.setObjectName(f"metadata_{field_name}")

            field_layout.addWidget(value_input)

            delete_btn = QPushButton("حذف")
            delete_btn.setProperty("field_name", field_name)
            delete_btn.clicked.connect(self.delete_metadata_field)

            field_layout.addWidget(delete_btn)

            self.metadata_form.addRow(f"{field_name}:", field_layout)

    def add_category(self):
        """إضافة تصنيف للمستند"""
        # الحصول على التصنيفات المتاحة
        document_categories = self.db.get_document_categories(self.document['id'])
        document_category_ids = [cat['id'] for cat in document_categories]

        # استخدام get_all_categories للحصول على جميع التصنيفات (الرئيسية والفرعية)
        all_categories = self.db.get_all_categories()
        available_categories = [cat for cat in all_categories if cat['id'] not in document_category_ids]

        if not available_categories:
            QMessageBox.information(self, "معلومات", "تم تعيين جميع التصنيفات المتاحة بالفعل")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة تصنيف")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        label = QLabel("اختر التصنيف:")
        layout.addWidget(label)

        category_combo = QComboBox()

        # إضافة التصنيفات مع عرض التسلسل الهرمي
        for category in available_categories:
            display_name = self._format_category_name(category)
            category_combo.addItem(display_name, category['id'])

        layout.addWidget(category_combo)

        buttons_layout = QHBoxLayout()
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        add_btn = QPushButton("إضافة")
        add_btn.clicked.connect(dialog.accept)
        buttons_layout.addWidget(add_btn)

        layout.addLayout(buttons_layout)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            category_id = category_combo.currentData()
            if category_id:
                self.db.assign_category_to_document(self.document['id'], category_id)
                self.load_categories()

    def _format_category_name(self, category):
        """تنسيق اسم التصنيف لعرض التسلسل الهرمي"""
        if category.get('parent_name'):
            return f"{category['parent_name']} ← {category['name']}"
        else:
            return category['name']

    def remove_category(self):
        """إزالة تصنيف من المستند"""
        selected_items = self.categories_list.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        category_id = item.data(Qt.ItemDataRole.UserRole)

        # حذف العلاقة بين المستند والتصنيف
        self.db.cursor.execute(
            "DELETE FROM document_categories WHERE document_id = ? AND category_id = ?",
            (self.document['id'], category_id)
        )
        self.db.conn.commit()

        self.load_categories()

    def add_metadata_field(self):
        """إضافة حقل بيانات وصفية جديد"""
        field_name = self.field_name_input.text().strip()
        field_value = self.field_value_input.text().strip()

        if not field_name:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال اسم الحقل")
            return

        # التحقق من عدم وجود الحقل مسبقاً
        if field_name in self.custom_metadata:
            QMessageBox.warning(self, "تحذير", "هذا الحقل موجود بالفعل")
            return

        # إضافة الحقل إلى قاعدة البيانات
        self.db.add_custom_metadata(self.document['id'], field_name, field_value)

        # تحديث العرض
        self.custom_metadata[field_name] = field_value
        self.load_metadata()

        # مسح حقول الإدخال
        self.field_name_input.clear()
        self.field_value_input.clear()

    def delete_metadata_field(self):
        """حذف حقل بيانات وصفية"""
        sender = self.sender()
        field_name = sender.property("field_name")

        if not field_name:
            return

        # حذف الحقل من قاعدة البيانات
        self.db.cursor.execute(
            "DELETE FROM custom_metadata WHERE document_id = ? AND field_name = ?",
            (self.document['id'], field_name)
        )
        self.db.conn.commit()

        # تحديث العرض
        if field_name in self.custom_metadata:
            del self.custom_metadata[field_name]

        self.load_metadata()

    def save_changes(self):
        """حفظ التغييرات"""
        title = self.title_input.text().strip()
        if not title:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال عنوان للمستند")
            return

        description = self.description_input.toPlainText().strip()
        keywords = self.keywords_input.text().strip()

        # تحديث البيانات الأساسية
        self.db.update_document(
            self.document['id'],
            title=title,
            description=description,
            keywords=keywords
        )

        # تحديث البيانات الوصفية المخصصة
        for i in range(self.metadata_form.rowCount()):
            label_item = self.metadata_form.itemAt(i, QFormLayout.ItemRole.LabelRole)
            if not label_item or not label_item.widget():
                continue

            field_name = label_item.widget().text().rstrip(":")

            field_layout = self.metadata_form.itemAt(i, QFormLayout.ItemRole.FieldRole)
            if not field_layout:
                continue

            value_input = field_layout.layout().itemAt(0).widget()
            if not value_input:
                continue

            field_value = value_input.text()

            # تحديث قيمة الحقل في قاعدة البيانات
            self.db.cursor.execute(
                "UPDATE custom_metadata SET field_value = ? WHERE document_id = ? AND field_name = ?",
                (field_value, self.document['id'], field_name)
            )

        self.db.conn.commit()

        # تحديث المستند في الواجهة الرئيسية
        self.document['title'] = title
        self.document['description'] = description
        self.document['keywords'] = keywords

        QMessageBox.information(self, "نجاح", "تم حفظ التغييرات بنجاح")
        self.accept()
