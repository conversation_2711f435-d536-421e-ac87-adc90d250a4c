#!/usr/bin/env python
# أداة بسيطة لتوليد مفاتيح الترخيص

import sys
import os
import random
import argparse

# إضافة المسار الحالي إلى مسار البحث عن المكتبات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.license_manager import LicenseManager

def generate_keys(count, output_file=None):
    """توليد عدد محدد من مفاتيح الترخيص"""
    license_manager = LicenseManager()
    keys = []
    
    for i in range(count):
        # توليد مفتاح باستخدام قيمة عشوائية
        seed = random.randint(1000, 9999)
        key = license_manager.generate_key(seed)
        keys.append(key)
        
    # طباعة المفاتيح
    print(f"تم إنشاء {len(keys)} مفتاح ترخيص:")
    for i, key in enumerate(keys, 1):
        valid = license_manager.validate_key(key)
        status = "صالح" if valid else "غير صالح"
        print(f"{i}. {key} [{status}]")
    
    # حفظ المفاتيح في ملف إذا تم تحديد ذلك
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            for key in keys:
                f.write(f"{key}\n")
        print(f"\nتم حفظ المفاتيح في الملف: {output_file}")
    
    return keys

def validate_key(key):
    """التحقق من صحة مفتاح الترخيص"""
    license_manager = LicenseManager()
    is_valid = license_manager.validate_key(key)
    
    if is_valid:
        print(f"المفتاح: {key}")
        print("حالة المفتاح: [صالح]")
    else:
        print(f"المفتاح: {key}")
        print("حالة المفتاح: [غير صالح]")
    
    return is_valid

def main():
    parser = argparse.ArgumentParser(description="أداة إنشاء والتحقق من مفاتيح الترخيص")
    
    subparsers = parser.add_subparsers(dest="command", help="الأمر المراد تنفيذه")
    
    # أمر توليد المفاتيح
    generate_parser = subparsers.add_parser("generate", help="توليد مفاتيح الترخيص")
    generate_parser.add_argument("-c", "--count", type=int, default=1, help="عدد المفاتيح المراد توليدها (الافتراضي: 1)")
    generate_parser.add_argument("-o", "--output", help="ملف لحفظ المفاتيح المولدة")
    
    # أمر التحقق من صحة المفتاح
    validate_parser = subparsers.add_parser("validate", help="التحقق من صحة مفتاح الترخيص")
    validate_parser.add_argument("key", help="مفتاح الترخيص للتحقق منه")
    
    args = parser.parse_args()
    
    if args.command == "generate":
        generate_keys(args.count, args.output)
    elif args.command == "validate":
        validate_key(args.key)
    else:
        # إذا لم يتم تحديد أي أمر، اعرض المساعدة
        parser.print_help()

if __name__ == "__main__":
    main()
