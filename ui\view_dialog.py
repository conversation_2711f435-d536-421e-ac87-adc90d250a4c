from PyQt6.QtWidgets import (Q<PERSON><PERSON>og, QVBoxLayout, QHBox<PERSON>ayout, QTreeWidget, QTreeWidgetItem,
                           QLabel, QPushButton, QGridLayout, QListView, QListWidget, QListWidgetItem,
                           QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QComboBox, QLineEdit)
from PyQt6.QtGui import QIcon, QPixmap, QFont, QDrag
from PyQt6.QtCore import Qt, QSize, QUrl
# استيراد QMimeData من PyQt6.QtCore بدلاً من PyQt6.QtGui
from PyQt6.QtCore import QMimeData
import os
from datetime import datetime

class ViewDialog(QDialog):
    def __init__(self, parent=None, show_all_files_option=False):
        super().__init__(parent)
        self.parent = parent
        self.setWindowTitle("عرض المجلدات")
        self.setMinimumSize(900, 700)

        # تخزين معلومات الملفات
        self.file_info_cache = {}
        self.current_category_id = None
        self.show_all_files_option = show_all_files_option
        self.search_text = ""  # إضافة متغير لتخزين نص البحث

        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout(self)

        # القسم العلوي: اختيار المجلد والعرض
        top_layout = QHBoxLayout()

        # قسم اختيار المجلد
        folder_layout = QVBoxLayout()
        folder_label = QLabel("اختر مجلد التصنيف:")
        folder_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        folder_layout.addWidget(folder_label)

        # إنشاء شجرة التصنيفات
        self.categories_tree = QTreeWidget()
        self.categories_tree.setHeaderLabels(["الاسم"])
        self.categories_tree.setHeaderHidden(True)
        self.categories_tree.setAnimated(True)
        self.categories_tree.setIconSize(QSize(24, 24))
        self.categories_tree.setFont(QFont("Arial", 11))
        self.categories_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 30px;
            }
            QTreeWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #e8eaf6;
            }
        """)

        # نسخ التصنيفات من الشجرة الرئيسية
        for i in range(self.parent.categories_tree.topLevelItemCount()):
            top_item = self.parent.categories_tree.topLevelItem(i)
            new_item = QTreeWidgetItem([top_item.text(0)])
            new_item.setData(0, Qt.ItemDataRole.UserRole, top_item.data(0, Qt.ItemDataRole.UserRole))
            self.categories_tree.addTopLevelItem(new_item)

            # نسخ العناصر الفرعية إذا كانت موجودة
            self._copy_tree_items(top_item, new_item)

        # توسيع الشجرة
        self.categories_tree.expandAll()
        folder_layout.addWidget(self.categories_tree)

        # زر عرض محتويات المجلد
        show_folder_btn = QPushButton("عرض محتويات المجلد")
        show_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #303f9f;
            }
        """)
        folder_layout.addWidget(show_folder_btn)

        # إضافة زر عرض جميع الملفات إذا كان الخيار مفعلاً
        if self.show_all_files_option:
            show_all_files_btn = QPushButton("عرض جميع الملفات")
            show_all_files_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #388e3c;
                }
            """)
            folder_layout.addWidget(show_all_files_btn)
            show_all_files_btn.clicked.connect(self.show_all_files)

        top_layout.addLayout(folder_layout, 1)

        # قسم خيارات العرض
        view_options_layout = QVBoxLayout()
        view_options_label = QLabel("خيارات العرض:")
        view_options_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        view_options_layout.addWidget(view_options_label)

        # أزرار خيارات العرض
        view_buttons_layout = QGridLayout()

        # زر أيقونات كبيرة
        large_icons_btn = QPushButton("أيقونات كبيرة")
        large_icons_btn.setCheckable(True)
        large_icons_btn.setChecked(True)  # افتراضي
        view_buttons_layout.addWidget(large_icons_btn, 0, 0)

        # زر أيقونات متوسطة
        medium_icons_btn = QPushButton("أيقونات متوسطة")
        medium_icons_btn.setCheckable(True)
        view_buttons_layout.addWidget(medium_icons_btn, 0, 1)

        # زر أيقونات صغيرة
        small_icons_btn = QPushButton("أيقونات صغيرة")
        small_icons_btn.setCheckable(True)
        view_buttons_layout.addWidget(small_icons_btn, 1, 0)

        # زر تفاصيل
        details_btn = QPushButton("تفاصيل")
        details_btn.setCheckable(True)
        view_buttons_layout.addWidget(details_btn, 1, 1)

        # مجموعة الأزرار للتأكد من اختيار واحد فقط
        self.view_buttons = [large_icons_btn, medium_icons_btn, small_icons_btn, details_btn]

        large_icons_btn.clicked.connect(lambda: self.toggle_view_button(large_icons_btn))
        medium_icons_btn.clicked.connect(lambda: self.toggle_view_button(medium_icons_btn))
        small_icons_btn.clicked.connect(lambda: self.toggle_view_button(small_icons_btn))
        details_btn.clicked.connect(lambda: self.toggle_view_button(details_btn))

        view_options_layout.addLayout(view_buttons_layout)

        # إضافة قسم البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_label.setStyleSheet("font-weight: bold;")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("أدخل كلمات البحث...")
        self.search_input.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #3f51b5;
            }
        """)
        search_btn = QPushButton("بحث")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #303f9f;
            }
        """)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, 1)
        search_layout.addWidget(search_btn)

        # ربط حدث النقر على زر البحث وضغط Enter في حقل البحث
        search_btn.clicked.connect(self.apply_search)
        self.search_input.returnPressed.connect(self.apply_search)

        view_options_layout.addLayout(search_layout)

        # إضافة قسم خيارات الفرز
        sort_label = QLabel("فرز حسب:")
        sort_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 10px;")
        view_options_layout.addWidget(sort_label)

        # أزرار خيارات الفرز
        sort_buttons_layout = QGridLayout()

        # زر الفرز حسب الاسم
        sort_by_name_btn = QPushButton("الاسم")
        sort_by_name_btn.setCheckable(True)
        sort_by_name_btn.setChecked(True)  # افتراضي
        sort_buttons_layout.addWidget(sort_by_name_btn, 0, 0)

        # زر الفرز حسب التاريخ
        sort_by_date_btn = QPushButton("التاريخ")
        sort_by_date_btn.setCheckable(True)
        sort_buttons_layout.addWidget(sort_by_date_btn, 0, 1)

        # زر الفرز حسب النوع
        sort_by_type_btn = QPushButton("النوع")
        sort_by_type_btn.setCheckable(True)
        sort_buttons_layout.addWidget(sort_by_type_btn, 1, 0)

        # زر الفرز حسب الحجم
        sort_by_size_btn = QPushButton("الحجم")
        sort_by_size_btn.setCheckable(True)
        sort_buttons_layout.addWidget(sort_by_size_btn, 1, 1)

        # مجموعة أزرار الفرز للتأكد من اختيار واحد فقط
        self.sort_buttons = [sort_by_name_btn, sort_by_date_btn, sort_by_type_btn, sort_by_size_btn]

        sort_by_name_btn.clicked.connect(lambda: self.toggle_sort_button(sort_by_name_btn))
        sort_by_date_btn.clicked.connect(lambda: self.toggle_sort_button(sort_by_date_btn))
        sort_by_type_btn.clicked.connect(lambda: self.toggle_sort_button(sort_by_type_btn))
        sort_by_size_btn.clicked.connect(lambda: self.toggle_sort_button(sort_by_size_btn))

        view_options_layout.addLayout(sort_buttons_layout)

        # إضافة قسم فلترة حسب الشهر
        month_filter_label = QLabel("فلترة حسب الشهر:")
        month_filter_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 10px;")
        view_options_layout.addWidget(month_filter_label)

        # إضافة قائمة منسدلة للشهور
        month_filter_layout = QHBoxLayout()

        self.month_combo = QComboBox()
        self.month_combo.addItem("جميع الشهور", "all")
        # إضافة الشهور الافتراضية
        for i in range(1, 13):
            self.month_combo.addItem(f"الشهر {i}", str(i))

        month_filter_layout.addWidget(self.month_combo)

        # زر تطبيق الفلترة
        apply_month_filter_btn = QPushButton("تطبيق")
        apply_month_filter_btn.clicked.connect(self.apply_month_filter)
        month_filter_layout.addWidget(apply_month_filter_btn)

        view_options_layout.addLayout(month_filter_layout)
        view_options_layout.addStretch()

        top_layout.addLayout(view_options_layout, 1)

        main_layout.addLayout(top_layout)

        # القسم السفلي: عرض الملفات
        self.files_label = QLabel("محتويات المجلد:")
        self.files_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 10px;")
        main_layout.addWidget(self.files_label)

        # إنشاء عرض التفاصيل (جدول)
        self.files_table = FileTableWidget(self, self.file_info_cache, self.parent.db)
        self.files_table.setColumnCount(5)
        self.files_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ الإنشاء", "تاريخ التعديل"])
        self.files_table.horizontalHeader().setStretchLastSection(True)
        self.files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.files_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #3f51b5;
                color: black;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #e8eaf6;
                color: black;
            }
        """)
        self.files_table.setVisible(False)  # مخفي افتراضيًا
        main_layout.addWidget(self.files_table)

        # قائمة الملفات بالأيقونات
        self.files_list = FileListWidget(self, self.file_info_cache, self.parent.db)
        self.files_list.setAlternatingRowColors(True)
        self.files_list.setIconSize(QSize(64, 64))  # افتراضي: أيقونات كبيرة
        self.files_list.setViewMode(QListView.ViewMode.IconMode)
        self.files_list.setGridSize(QSize(120, 150))
        self.files_list.setResizeMode(QListView.ResizeMode.Adjust)
        self.files_list.setWrapping(True)
        self.files_list.setWordWrap(True)
        self.files_list.setSpacing(10)
        self.files_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 12px;
            }
            QListWidget::item {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #f5f5f5;
            }
            QListWidget::item:selected {
                background-color: #e8eaf6;
                border: 2px solid #3f51b5;
            }
            QListWidget::item:hover {
                background-color: #eeeeee;
                border: 1px solid #c5cae9;
            }
        """)
        main_layout.addWidget(self.files_list)

        # ربط النقر المزدوج بفتح الملف
        self.files_list.itemDoubleClicked.connect(self.open_file_from_list)
        self.files_table.itemDoubleClicked.connect(self.open_file_from_table)

        # ربط أزرار العرض بدوالها
        large_icons_btn.clicked.connect(self.change_to_large_icons)
        medium_icons_btn.clicked.connect(self.change_to_medium_icons)
        small_icons_btn.clicked.connect(self.change_to_small_icons)
        details_btn.clicked.connect(self.change_to_details)

        # ربط زر عرض محتويات المجلد بدالته
        show_folder_btn.clicked.connect(self.show_folder_files)

        # إضافة زر إغلاق في الأسفل
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        main_layout.addWidget(close_btn)

    def _copy_tree_items(self, source_item, target_item):
        """نسخ عناصر الشجرة بشكل متكرر"""
        for i in range(source_item.childCount()):
            child = source_item.child(i)
            new_child = QTreeWidgetItem([child.text(0)])
            new_child.setData(0, Qt.ItemDataRole.UserRole, child.data(0, Qt.ItemDataRole.UserRole))
            target_item.addChild(new_child)

            # نسخ العناصر الفرعية بشكل متكرر
            self._copy_tree_items(child, new_child)

    def toggle_view_button(self, button):
        for btn in self.view_buttons:
            if btn != button:
                btn.setChecked(False)
        button.setChecked(True)

    def toggle_sort_button(self, button):
        """تبديل زر الفرز وتطبيق الفرز"""
        for btn in self.sort_buttons:
            if btn != button:
                btn.setChecked(False)
        button.setChecked(True)

        # تطبيق الفرز على العرض الحالي
        self.apply_sorting()

    def change_to_large_icons(self):
        self.files_list.setVisible(True)
        self.files_table.setVisible(False)
        self.files_list.setViewMode(QListView.ViewMode.IconMode)
        self.files_list.setIconSize(QSize(96, 96))
        self.files_list.setGridSize(QSize(150, 180))
        self.files_list.setSpacing(15)

    def change_to_medium_icons(self):
        self.files_list.setVisible(True)
        self.files_table.setVisible(False)
        self.files_list.setViewMode(QListView.ViewMode.IconMode)
        self.files_list.setIconSize(QSize(64, 64))
        self.files_list.setGridSize(QSize(120, 150))
        self.files_list.setSpacing(10)

    def change_to_small_icons(self):
        self.files_list.setVisible(True)
        self.files_table.setVisible(False)
        self.files_list.setViewMode(QListView.ViewMode.IconMode)
        self.files_list.setIconSize(QSize(32, 32))
        self.files_list.setGridSize(QSize(100, 120))
        self.files_list.setSpacing(8)

    def change_to_details(self):
        self.files_list.setVisible(False)
        self.files_table.setVisible(True)
        # إذا كان هناك ملفات معروضة، قم بعرضها في جدول التفاصيل
        if self.current_category_id is not None:
            self.show_files_in_details_view()

    def create_thumbnail(self, file_path, file_type):
        """إنشاء صورة مصغرة للملف"""
        if not file_path or not os.path.exists(file_path):
            return self.get_default_icon(file_type)

        try:
            if 'pdf' in file_type.lower():
                # يمكن استخدام مكتبة خارجية مثل PyMuPDF لإنشاء صورة مصغرة من PDF
                return QIcon("resources/pdf.png")
            elif 'word' in file_type.lower() or 'doc' in file_type.lower():
                return QIcon("resources/word.png")
            elif 'excel' in file_type.lower() or 'xls' in file_type.lower():
                return QIcon("resources/excel.png")
            elif ('image' in file_type.lower() or
                  'jpg' in file_type.lower() or
                  'jpeg' in file_type.lower() or
                  'png' in file_type.lower() or
                  'gif' in file_type.lower()):
                # إنشاء صورة مصغرة للصور
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    pixmap = pixmap.scaled(96, 96, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    return QIcon(pixmap)
                else:
                    return QIcon("resources/image.png")
            else:
                return self.get_default_icon(file_type)
        except Exception as e:
            print(f"خطأ في إنشاء صورة مصغرة: {str(e)}")
            return self.get_default_icon(file_type)

    def get_default_icon(self, file_type):
        """الحصول على أيقونة افتراضية بناءً على نوع الملف"""
        file_type = file_type.lower() if file_type else ""
        if 'pdf' in file_type:
            return QIcon("resources/pdf.png")
        elif 'word' in file_type or 'doc' in file_type:
            return QIcon("resources/word.png")
        elif 'excel' in file_type or 'xls' in file_type:
            return QIcon("resources/excel.png")
        elif 'image' in file_type or 'jpg' in file_type or 'png' in file_type:
            return QIcon("resources/image.png")
        else:
            return QIcon("resources/file.png")

    def format_size(self, size_bytes):
        """تنسيق حجم الملف بشكل مقروء"""
        if size_bytes < 1024:
            return f"{size_bytes} بايت"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} كيلوبايت"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} ميجابايت"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} جيجابايت"

    def format_date(self, date_str):
        """تنسيق التاريخ بشكل مقروء"""
        if not date_str:
            return ""
        try:
            date_obj = datetime.fromisoformat(date_str)
            return date_obj.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return date_str

    def show_files_in_details_view(self, sort_type=None):
        """عرض الملفات في عرض التفاصيل"""
        try:
            self.files_table.setRowCount(0)

            if not self.file_info_cache:
                return

            # تحديد عدد الأعمدة وعناوينها
            show_category = self.show_all_files_option and self.files_label.text().startswith("جميع الملفات")
            show_month = True  # دائماً نعرض عمود الشهر

            # تعديل عدد الأعمدة وعناوينها
            if show_category and show_month:
                # عرض التصنيف والشهر
                self.files_table.setColumnCount(7)
                self.files_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ الإنشاء", "تاريخ التعديل", "التصنيف", "الشهر"])
            elif show_category:
                # عرض التصنيف فقط
                self.files_table.setColumnCount(6)
                self.files_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ الإنشاء", "تاريخ التعديل", "التصنيف"])
            elif show_month:
                # عرض الشهر فقط
                self.files_table.setColumnCount(6)
                self.files_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ الإنشاء", "تاريخ التعديل", "الشهر"])
            else:
                # لا نعرض التصنيف ولا الشهر
                self.files_table.setColumnCount(5)
                self.files_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ الإنشاء", "تاريخ التعديل"])

            # فرز المستندات إذا تم تحديد نوع الفرز
            documents = list(self.file_info_cache.values())
            if sort_type:
                documents = self.sort_documents(documents, sort_type)
            else:
                # تحديد نوع الفرز المحدد من الأزرار
                for btn in self.sort_buttons:
                    if btn.isChecked():
                        documents = self.sort_documents(documents, btn.text())
                        break

            for doc in documents:
                try:
                    doc_id = doc['id']
                    row = self.files_table.rowCount()
                    self.files_table.insertRow(row)

                    # إنشاء عنصر للاسم مع أيقونة
                    name_item = QTableWidgetItem(doc['title'])
                    name_item.setData(Qt.ItemDataRole.UserRole, doc_id)
                    icon = self.get_default_icon(doc.get('file_type', ''))
                    name_item.setIcon(icon)
                    self.files_table.setItem(row, 0, name_item)

                    # نوع الملف
                    type_item = QTableWidgetItem(doc.get('file_type', ''))
                    self.files_table.setItem(row, 1, type_item)

                    # حجم الملف
                    size_item = QTableWidgetItem(self.format_size(doc.get('file_size', 0)))
                    self.files_table.setItem(row, 2, size_item)

                    # تاريخ الإنشاء
                    creation_date_item = QTableWidgetItem(self.format_date(doc.get('creation_date', '')))
                    self.files_table.setItem(row, 3, creation_date_item)

                    # تاريخ التعديل
                    modification_date_item = QTableWidgetItem(self.format_date(doc.get('modification_date', '')))
                    self.files_table.setItem(row, 4, modification_date_item)

                    # تحديد الأعمدة الإضافية
                    next_column = 5

                    # إضافة اسم التصنيف إذا كان خيار عرض جميع الملفات مفعلاً
                    if show_category:
                        category_name = "غير مصنف"
                        try:
                            # الحصول على تصنيفات المستند
                            document_categories = self.parent.db.get_document_categories(doc_id)
                            if document_categories:
                                # استخدام أول تصنيف للعرض
                                category_name = document_categories[0]['name']
                        except Exception as e:
                            print(f"خطأ في الحصول على تصنيفات المستند في عرض التفاصيل: {str(e)}")

                        category_item = QTableWidgetItem(category_name)
                        self.files_table.setItem(row, next_column, category_item)
                        next_column += 1

                    # إضافة قيمة الشهر من البيانات الوصفية
                    if show_month:
                        month_value = "غير محدد"
                        try:
                            # الحصول على البيانات الوصفية المخصصة للمستند
                            custom_metadata = self.parent.db.get_custom_metadata(doc_id)
                            if 'الشهر' in custom_metadata and custom_metadata['الشهر']:
                                month_value = custom_metadata['الشهر']
                        except Exception as e:
                            print(f"خطأ في الحصول على بيانات الشهر: {str(e)}")

                        month_item = QTableWidgetItem(month_value)
                        self.files_table.setItem(row, next_column, month_item)
                except Exception as e:
                    print(f"خطأ في إضافة المستند {doc.get('id', 'غير معروف')} إلى جدول التفاصيل: {str(e)}")
                    continue
        except Exception as e:
            print(f"خطأ في دالة show_files_in_details_view: {str(e)}")
            # عرض رسالة خطأ بدلاً من إغلاق البرنامج
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء عرض الملفات في جدول التفاصيل: {str(e)}")

    def open_file_from_list(self, item):
        """فتح الملف عند النقر المزدوج على عنصر في القائمة"""
        doc_id = item.data(Qt.ItemDataRole.UserRole)
        if doc_id in self.file_info_cache:
            file_path = self.file_info_cache[doc_id].get('file_path', '')
            if file_path and os.path.exists(file_path):
                try:
                    # فتح الملف باستخدام التطبيق الافتراضي في نظام التشغيل
                    os.startfile(file_path)  # Windows

                    # تحديث تاريخ التصدير في قاعدة البيانات
                    self.parent.db.update_document_export_date(doc_id)

                    # تحديث العرض لإظهار تاريخ التصدير المحدث
                    if self.current_category_id is not None:
                        self.show_folder_files()
                    else:
                        self.show_all_files()
                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في فتح الملف: {str(e)}")
            else:
                QMessageBox.warning(self, "خطأ", "الملف غير موجود")

    def open_file_from_table(self, item):
        """فتح الملف عند النقر المزدوج على عنصر في الجدول"""
        if item.column() == 0:  # فقط عند النقر على عمود الاسم
            doc_id = item.data(Qt.ItemDataRole.UserRole)
            if doc_id in self.file_info_cache:
                file_path = self.file_info_cache[doc_id].get('file_path', '')
                if file_path and os.path.exists(file_path):
                    try:
                        # فتح الملف باستخدام التطبيق الافتراضي في نظام التشغيل
                        os.startfile(file_path)  # Windows

                        # تحديث تاريخ التصدير في قاعدة البيانات
                        self.parent.db.update_document_export_date(doc_id)

                        # تحديث العرض لإظهار تاريخ التصدير المحدث
                        if self.current_category_id is not None:
                            self.show_folder_files()
                        else:
                            self.show_all_files()
                    except Exception as e:
                        QMessageBox.warning(self, "خطأ", f"فشل في فتح الملف: {str(e)}")
                else:
                    QMessageBox.warning(self, "خطأ", "الملف غير موجود")

    def update_month_combo(self):
        """تحديث قائمة الشهور بناءً على البيانات الوصفية للملفات المعروضة"""
        try:
            # حفظ الشهر المحدد حالياً
            current_month = self.month_combo.currentData()

            # لا نحتاج إلى مسح القائمة المنسدلة لأننا أضفنا الشهور الافتراضية بالفعل
            # نحتفظ بالقائمة كما هي

            # جمع جميع قيم الشهور المتاحة من البيانات الوصفية
            available_months = set()

            for doc_id in self.file_info_cache:
                # الحصول على البيانات الوصفية المخصصة للمستند
                custom_metadata = self.parent.db.get_custom_metadata(doc_id)

                # إضافة قيمة الشهر إذا كانت موجودة
                if 'الشهر' in custom_metadata and custom_metadata['الشهر']:
                    available_months.add(custom_metadata['الشهر'])

            # إضافة الشهور المتاحة إلى القائمة المنسدلة إذا لم تكن موجودة بالفعل
            for month in sorted(available_months, key=lambda x: int(x) if x.isdigit() else 0):
                # التحقق مما إذا كان الشهر موجوداً بالفعل في القائمة
                index = self.month_combo.findData(month)
                if index == -1:  # إذا لم يكن موجوداً
                    self.month_combo.addItem(f"الشهر {month}", month)

            # إعادة تحديد الشهر السابق إذا كان موجوداً
            if current_month != "all":
                index = self.month_combo.findData(current_month)
                if index != -1:
                    self.month_combo.setCurrentIndex(index)
                else:
                    self.month_combo.setCurrentIndex(0)  # تحديد "جميع الشهور"

        except Exception as e:
            print(f"خطأ في تحديث قائمة الشهور: {str(e)}")

    def get_arabic_month_name(self, month_number):
        """الحصول على اسم الشهر بالعربية"""
        months = {
            1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل",
            5: "مايو", 6: "يونيو", 7: "يوليو", 8: "أغسطس",
            9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
        }
        return months.get(month_number, f"الشهر {month_number}")

    def show_folder_files(self):
        """عرض الملفات عند النقر على زر عرض محتويات المجلد"""
        selected_items = self.categories_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مجلد أولاً")
            return

        selected_item = selected_items[0]
        category_id = selected_item.data(0, Qt.ItemDataRole.UserRole)
        self.current_category_id = category_id

        # تحميل الملفات من قاعدة البيانات
        if category_id is None:
            documents = self.parent.db.get_documents()
        else:
            documents = self.parent.db.get_documents(category_id)

        # تخزين معلومات الملفات
        self.file_info_cache.clear()
        for doc in documents:
            self.file_info_cache[doc['id']] = doc

        # تحديث معلومات الملفات في الفئات المخصصة
        self.files_list.file_info_cache = self.file_info_cache
        self.files_table.file_info_cache = self.file_info_cache

        # تحديث عنوان القسم
        folder_name = selected_item.text(0)
        self.files_label.setText(f"محتويات المجلد: {folder_name} ({len(documents)} ملف)")

        # تحديث قائمة الشهور
        self.update_month_combo()

        # إعادة تعيين حقل البحث
        self.search_text = ""
        self.search_input.setText("")

        # تطبيق الفرز وعرض الملفات
        self.apply_sorting()

    def apply_sorting(self):
        """تطبيق الفرز على العرض الحالي"""
        try:
            # تحديد نوع الفرز المحدد
            sort_type = None
            for button in self.sort_buttons:
                if button.isChecked():
                    sort_type = button.text()
                    break

            # تحديد الشهر المحدد للفلترة
            selected_month = self.month_combo.currentText()

            # الحصول على المستندات المفلترة حسب الشهر
            if selected_month != "جميع الشهور":
                filtered_docs = {}
                for doc_id, doc_info in self.file_info_cache.items():
                    # استخراج الشهر من تاريخ الاستيراد أو تاريخ الإرسال
                    import_date = doc_info.get('import_date')
                    if import_date:
                        try:
                            date_obj = datetime.strptime(import_date, "%Y-%m-%d")
                            month_name = self.get_arabic_month_name(date_obj.month)
                            if month_name == selected_month:
                                filtered_docs[doc_id] = doc_info
                        except ValueError:
                            pass
            else:
                filtered_docs = self.file_info_cache

            # تطبيق فلترة البحث إذا كان هناك نص بحث
            if self.search_text:
                search_filtered_docs = {}
                search_text_lower = self.search_text.lower()
                for doc_id, doc_info in filtered_docs.items():
                    # البحث في اسم الملف
                    if search_text_lower in doc_info.get('title', '').lower():
                        search_filtered_docs[doc_id] = doc_info
                        continue

                    # البحث في نوع الملف
                    if 'file_type' in doc_info and search_text_lower in doc_info['file_type'].lower():
                        search_filtered_docs[doc_id] = doc_info
                        continue

                    # البحث في الوصف إذا كان متاحاً
                    if 'description' in doc_info and doc_info['description'] and search_text_lower in doc_info['description'].lower():
                        search_filtered_docs[doc_id] = doc_info
                        continue

                    # البحث في العنوان إذا كان متاحاً
                    if 'title' in doc_info and doc_info['title'] and search_text_lower in doc_info['title'].lower():
                        search_filtered_docs[doc_id] = doc_info
                        continue

                filtered_docs = search_filtered_docs

            # فرز المستندات
            sorted_docs = self.sort_documents(list(filtered_docs.values()), sort_type)

            # تحديد طريقة العرض المحددة
            view_type = None
            for button in self.view_buttons:
                if button.isChecked():
                    view_type = button.text()
                    break

            # عرض الملفات حسب طريقة العرض المحددة
            if view_type == "تفاصيل":
                # عرض في جدول
                self.files_table.setRowCount(0)  # مسح الجدول
                self.files_table.setRowCount(len(sorted_docs))

                for row, doc in enumerate(sorted_docs):
                    # الاسم
                    name_item = QTableWidgetItem(doc.get('title', ''))
                    name_item.setData(Qt.ItemDataRole.UserRole, doc['id'])
                    self.files_table.setItem(row, 0, name_item)

                    # النوع
                    type_item = QTableWidgetItem(doc.get('file_type', ''))
                    self.files_table.setItem(row, 1, type_item)

                    # الحجم
                    file_path = doc.get('file_path', '')
                    size_bytes = os.path.getsize(file_path) if file_path and os.path.exists(file_path) else 0
                    size_item = QTableWidgetItem(self.format_size(size_bytes))
                    self.files_table.setItem(row, 2, size_item)

                    # تاريخ الاستيراد
                    import_date = doc.get('import_date', '')
                    import_date_item = QTableWidgetItem(self.format_date(import_date) if import_date else '')
                    self.files_table.setItem(row, 3, import_date_item)

                    # تاريخ الإرسال
                    send_date = doc.get('send_date', '')
                    send_date_item = QTableWidgetItem(self.format_date(send_date) if send_date else '')
                    self.files_table.setItem(row, 4, send_date_item)

                # إظهار جدول الملفات وإخفاء قائمة الملفات
                self.files_table.setVisible(True)
                self.files_list.setVisible(False)
            else:
                # عرض في قائمة
                self.files_list.clear()

                # تعيين حجم العناصر حسب طريقة العرض
                if view_type == "أيقونات كبيرة":
                    self.change_to_large_icons()
                elif view_type == "أيقونات متوسطة":
                    self.change_to_medium_icons()
                elif view_type == "أيقونات صغيرة":
                    self.change_to_small_icons()

                # إضافة الملفات إلى القائمة
                for doc in sorted_docs:
                    item = QListWidgetItem()
                    item.setText(doc.get('title', ''))
                    item.setData(Qt.ItemDataRole.UserRole, doc['id'])

                    # إنشاء صورة مصغرة للملف
                    file_path = doc.get('file_path', '')
                    file_type = doc.get('file_type', '')
                    thumbnail = self.create_thumbnail(file_path, file_type)
                    item.setIcon(QIcon(thumbnail))

                    self.files_list.addItem(item)

                # إظهار قائمة الملفات وإخفاء جدول الملفات
                self.files_list.setVisible(True)
                self.files_table.setVisible(False)

            # تحديث عنوان القسم ليعكس نتائج البحث إذا كان هناك بحث
            if self.search_text:
                current_title = self.files_label.text()
                # استخراج الجزء الأول من العنوان (قبل القوسين)
                base_title = current_title.split("(")[0].strip()
                self.files_label.setText(f"{base_title} - نتائج البحث: {self.search_text} ({len(sorted_docs)} ملف)")

        except Exception as e:
            print(f"خطأ في دالة apply_sorting: {str(e)}")

    def sort_documents(self, documents, sort_type):
        """فرز المستندات حسب المعيار المحدد"""
        if sort_type == "الاسم":
            return sorted(documents, key=lambda x: x.get('title', ''))
        elif sort_type == "النوع":
            return sorted(documents, key=lambda x: x.get('file_type', ''))
        elif sort_type == "الحجم":
            return sorted(documents, key=lambda x: os.path.getsize(x.get('file_path', '')) if x.get('file_path') and os.path.exists(x['file_path']) else 0)
        elif sort_type == "التاريخ":
            return sorted(documents, key=lambda x: x.get('import_date', '') or '', reverse=True)
        else:
            return documents

    def apply_search(self):
        """تطبيق البحث على الملفات المعروضة"""
        # الحصول على نص البحث
        self.search_text = self.search_input.text().strip()

        # تطبيق الفرز والبحث
        self.apply_sorting()

    def apply_month_filter(self):
        """تطبيق فلترة الملفات حسب الشهر المحدد"""
        try:
            selected_month = self.month_combo.currentData()

            if selected_month == "all":
                # إذا تم اختيار "جميع الشهور"، قم بإعادة عرض جميع الملفات
                if self.current_category_id is not None:
                    self.show_folder_files()
                else:
                    self.show_all_files()
                return

            # الحصول على نسخة من الملفات الأصلية قبل الفلترة
            original_files = self.file_info_cache.copy()

            # فلترة الملفات حسب الشهر المحدد
            filtered_files = {}
            for doc_id, doc in original_files.items():
                # الحصول على البيانات الوصفية المخصصة للمستند
                custom_metadata = self.parent.db.get_custom_metadata(doc_id)

                # التحقق من وجود حقل الشهر وقيمته
                if 'الشهر' in custom_metadata and custom_metadata['الشهر'] == selected_month:
                    filtered_files[doc_id] = doc

            # تحديث قائمة الملفات المعروضة
            self.file_info_cache = filtered_files

            # تحديث عنوان القسم
            current_title = self.files_label.text().split('(')[0].strip()
            self.files_label.setText(f"{current_title} - فلترة: الشهر {selected_month} ({len(filtered_files)} ملف)")

            # تحديث العرض
            self.files_list.clear()

            for doc in filtered_files.values():
                try:
                    item = QListWidgetItem(doc['title'])
                    item.setData(Qt.ItemDataRole.UserRole, doc['id'])

                    # إضافة صورة مصغرة للملف
                    file_path = doc.get('file_path', '')
                    file_type = doc.get('file_type', '')
                    item.setIcon(self.create_thumbnail(file_path, file_type))

                    # إضافة معلومات إضافية للتلميح
                    tooltip = f"الاسم: {doc['title']}"

                    # إضافة الوصف إذا وجد
                    description = doc.get('description', '')
                    if description:
                        tooltip += f"\nالوصف: {description}"

                    # إضافة تاريخ الاستيراد
                    import_date = doc.get('import_date', '')
                    if import_date:
                        tooltip += f"\nتاريخ الاستيراد: {import_date}"

                    # إضافة تاريخ التصدير إذا وجد
                    export_date = doc.get('export_date', '')
                    if export_date:
                        tooltip += f"\nتاريخ التصدير: {export_date}"

                    # إضافة معلومات الشهر من البيانات الوصفية
                    custom_metadata = self.parent.db.get_custom_metadata(doc['id'])
                    if 'الشهر' in custom_metadata:
                        tooltip += f"\nالشهر: {custom_metadata['الشهر']}"

                    item.setToolTip(tooltip)
                    self.files_list.addItem(item)
                except Exception as e:
                    print(f"خطأ في إضافة المستند {doc.get('id', 'غير معروف')} إلى القائمة: {str(e)}")
                    continue

            # عرض الملفات في جدول التفاصيل إذا كان مرئيًا
            if self.files_table.isVisible():
                self.show_files_in_details_view()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تطبيق فلترة الشهر: {str(e)}")
            print(f"خطأ في دالة apply_month_filter: {str(e)}")

    def show_all_files(self):
        """عرض جميع الملفات من جميع المجلدات"""
        try:
            # تحميل جميع الملفات من قاعدة البيانات بدون تصفية حسب التصنيف
            documents = self.parent.db.get_documents()

            # تخزين معلومات الملفات
            self.file_info_cache.clear()
            for doc in documents:
                self.file_info_cache[doc['id']] = doc

            # تحديث معلومات الملفات في الفئات المخصصة
            self.files_list.file_info_cache = self.file_info_cache
            self.files_table.file_info_cache = self.file_info_cache

            # عرض الملفات في القائمة
            self.files_list.clear()

            for doc in documents:
                try:
                    item = QListWidgetItem(doc['title'])
                    item.setData(Qt.ItemDataRole.UserRole, doc['id'])

                    # إضافة صورة مصغرة للملف
                    file_path = doc.get('file_path', '')
                    file_type = doc.get('file_type', '')
                    item.setIcon(self.create_thumbnail(file_path, file_type))

                    # إضافة معلومات إضافية للتلميح
                    tooltip = f"الاسم: {doc['title']}"

                    # إضافة الوصف إذا وجد
                    description = doc.get('description', '')
                    if description:
                        tooltip += f"\nالوصف: {description}"

                    # إضافة تاريخ الاستيراد
                    import_date = doc.get('import_date', '')
                    if import_date:
                        tooltip += f"\nتاريخ الاستيراد: {import_date}"

                    # إضافة تاريخ التصدير إذا وجد
                    export_date = doc.get('export_date', '')
                    if export_date:
                        tooltip += f"\nتاريخ التصدير: {export_date}"

                    # إضافة معلومات الشهر من البيانات الوصفية
                    try:
                        custom_metadata = self.parent.db.get_custom_metadata(doc['id'])
                        if 'الشهر' in custom_metadata and custom_metadata['الشهر']:
                            tooltip += f"\nالشهر: {custom_metadata['الشهر']}"
                    except Exception as metadata_error:
                        print(f"خطأ في الحصول على البيانات الوصفية: {str(metadata_error)}")

                    item.setToolTip(tooltip)

                    self.files_list.addItem(item)
                except Exception as e:
                    print(f"خطأ في إضافة المستند {doc.get('id', 'غير معروف')} إلى القائمة: {str(e)}")
                    continue

            # عرض الملفات في جدول التفاصيل إذا كان مرئيًا
            if self.files_table.isVisible():
                try:
                    self.show_files_in_details_view()
                except Exception as e:
                    print(f"خطأ في عرض الملفات في جدول التفاصيل: {str(e)}")

            # تحديث عنوان القسم
            self.files_label.setText(f"جميع الملفات من جميع المجلدات ({len(documents)} ملف)")

            # تعيين current_category_id إلى None لتجنب الأخطاء
            self.current_category_id = None

            # تحديث قائمة الشهور
            self.update_month_combo()

        except Exception as e:
            # عرض رسالة خطأ بدلاً من إغلاق البرنامج
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء عرض جميع الملفات: {str(e)}")
            print(f"خطأ في دالة show_all_files: {str(e)}")

# فئة مخصصة لقائمة الملفات تدعم السحب والإفلات
class FileListWidget(QListWidget):
    def __init__(self, parent=None, file_info_cache=None, db=None):
        super().__init__(parent)
        self.file_info_cache = file_info_cache
        self.db = db
        self.setDragEnabled(True)
        self.parent_dialog = parent

    def startDrag(self, supportedActions):
        try:
            items = self.selectedItems()
            if not items:
                return

            # إنشاء كائن QMimeData
            mime_data = QMimeData()
            urls = []
            exported_doc_ids = []  # قائمة بمعرفات المستندات المصدرة

            for item in items:
                doc_id = item.data(Qt.ItemDataRole.UserRole)
                if doc_id in self.file_info_cache:
                    file_path = self.file_info_cache[doc_id].get('file_path', '')
                    if file_path and os.path.exists(file_path):
                        url = QUrl.fromLocalFile(file_path)
                        urls.append(url)
                        exported_doc_ids.append(doc_id)  # إضافة معرف المستند إلى قائمة المستندات المصدرة

            if urls:
                mime_data.setUrls(urls)
                drag = QDrag(self)
                drag.setMimeData(mime_data)

                # تعيين صورة للسحب
                pixmap = QPixmap("resources/file.png").scaled(32, 32)
                drag.setPixmap(pixmap)

                # بدء عملية السحب
                result = drag.exec(Qt.DropAction.CopyAction)

                # تسجيل الملفات المصدرة في قاعدة البيانات
                if result == Qt.DropAction.CopyAction:
                    try:
                        # استخدام قاعدة البيانات لتحديث تاريخ التصدير
                        if self.db is not None:
                            for doc_id in exported_doc_ids:
                                self.db.update_document_export_date(doc_id)
                            print("تم تسجيل عملية التصدير بنجاح")

                            # تحديث العرض لإظهار تاريخ التصدير المحدث
                            if self.parent_dialog:
                                if hasattr(self.parent_dialog, 'current_category_id') and self.parent_dialog.current_category_id is not None:
                                    self.parent_dialog.show_folder_files()
                                elif hasattr(self.parent_dialog, 'show_all_files'):
                                    self.parent_dialog.show_all_files()
                    except Exception as e:
                        print(f"خطأ في تسجيل عملية التصدير: {str(e)}")
        except Exception as e:
            print(f"خطأ في عملية السحب: {str(e)}")

# فئة مخصصة لجدول الملفات تدعم السحب والإفلات
class FileTableWidget(QTableWidget):
    def __init__(self, parent=None, file_info_cache=None, db=None):
        super().__init__(parent)
        self.file_info_cache = file_info_cache
        self.db = db
        self.setDragEnabled(True)
        self.parent_dialog = parent

    def startDrag(self, supportedActions):
        try:
            items = self.selectedItems()
            if not items:
                return

            # إنشاء كائن QMimeData
            mime_data = QMimeData()
            urls = []
            exported_doc_ids = []  # قائمة بمعرفات المستندات المصدرة
            selected_rows = set()

            for item in items:
                row = item.row()
                selected_rows.add(row)

            for row in selected_rows:
                item = self.item(row, 0)  # الحصول على العنصر في العمود الأول (الاسم)
                if item:
                    doc_id = item.data(Qt.ItemDataRole.UserRole)
                    if doc_id in self.file_info_cache:
                        file_path = self.file_info_cache[doc_id].get('file_path', '')
                        if file_path and os.path.exists(file_path):
                            url = QUrl.fromLocalFile(file_path)
                            urls.append(url)
                            exported_doc_ids.append(doc_id)  # إضافة معرف المستند إلى قائمة المستندات المصدرة

            if urls:
                mime_data.setUrls(urls)
                drag = QDrag(self)
                drag.setMimeData(mime_data)

                # تعيين صورة للسحب
                pixmap = QPixmap("resources/file.png").scaled(32, 32)
                drag.setPixmap(pixmap)

                # بدء عملية السحب
                result = drag.exec(Qt.DropAction.CopyAction)

                # تسجيل الملفات المصدرة في قاعدة البيانات
                if result == Qt.DropAction.CopyAction:
                    try:
                        # استخدام قاعدة البيانات لتحديث تاريخ التصدير
                        if self.db is not None:
                            for doc_id in exported_doc_ids:
                                self.db.update_document_export_date(doc_id)
                            print("تم تسجيل عملية التصدير بنجاح")

                            # تحديث العرض لإظهار تاريخ التصدير المحدث
                            if self.parent_dialog:
                                if hasattr(self.parent_dialog, 'current_category_id') and self.parent_dialog.current_category_id is not None:
                                    self.parent_dialog.show_folder_files()
                                elif hasattr(self.parent_dialog, 'show_all_files'):
                                    self.parent_dialog.show_all_files()
                    except Exception as e:
                        print(f"خطأ في تسجيل عملية التصدير: {str(e)}")
        except Exception as e:
            print(f"خطأ في عملية السحب: {str(e)}")

def show_view_dialog(parent):
    """عرض نافذة حوار العرض"""
    dialog = ViewDialog(parent)
    dialog.exec()
