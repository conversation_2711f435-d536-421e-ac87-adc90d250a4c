"""
أداة لإنشاء أيقونات الملفات اللازمة للبرنامج
"""

import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QIcon, QPixmap, QPainter, QColor, QPen, QBrush, QFont
from PyQt6.QtCore import Qt, QSize, QRect, QPoint

def create_pdf_icon():
    """إنشاء أيقونة ملف PDF"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم ورقة
    painter.setPen(QPen(QColor("#666666"), 1))
    painter.setBrush(QBrush(QColor("#ffffff")))
    painter.drawRect(8, 4, 48, 56)
    
    # رسم شريط أحمر في الأعلى
    painter.setPen(Qt.PenStyle.NoPen)
    painter.setBrush(QBrush(QColor("#e53935")))
    painter.drawRect(8, 4, 48, 10)
    
    # كتابة PDF
    painter.setPen(QPen(QColor("#e53935"), 2))
    painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
    painter.drawText(QRect(8, 20, 48, 30), Qt.AlignmentFlag.AlignCenter, "PDF")
    
    painter.end()
    return pixmap

def create_word_icon():
    """إنشاء أيقونة ملف Word"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم ورقة
    painter.setPen(QPen(QColor("#666666"), 1))
    painter.setBrush(QBrush(QColor("#ffffff")))
    painter.drawRect(8, 4, 48, 56)
    
    # رسم شريط أزرق في الأعلى
    painter.setPen(Qt.PenStyle.NoPen)
    painter.setBrush(QBrush(QColor("#2196f3")))
    painter.drawRect(8, 4, 48, 10)
    
    # كتابة DOC
    painter.setPen(QPen(QColor("#2196f3"), 2))
    painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
    painter.drawText(QRect(8, 20, 48, 30), Qt.AlignmentFlag.AlignCenter, "DOC")
    
    painter.end()
    return pixmap

def create_excel_icon():
    """إنشاء أيقونة ملف Excel"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم ورقة
    painter.setPen(QPen(QColor("#666666"), 1))
    painter.setBrush(QBrush(QColor("#ffffff")))
    painter.drawRect(8, 4, 48, 56)
    
    # رسم شريط أخضر في الأعلى
    painter.setPen(Qt.PenStyle.NoPen)
    painter.setBrush(QBrush(QColor("#4caf50")))
    painter.drawRect(8, 4, 48, 10)
    
    # كتابة XLS
    painter.setPen(QPen(QColor("#4caf50"), 2))
    painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
    painter.drawText(QRect(8, 20, 48, 30), Qt.AlignmentFlag.AlignCenter, "XLS")
    
    painter.end()
    return pixmap

def create_image_icon():
    """إنشاء أيقونة ملف صورة"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار الصورة
    painter.setPen(QPen(QColor("#666666"), 1))
    painter.setBrush(QBrush(QColor("#ffffff")))
    painter.drawRect(8, 4, 48, 56)
    
    # رسم شريط أرجواني في الأعلى
    painter.setPen(Qt.PenStyle.NoPen)
    painter.setBrush(QBrush(QColor("#9c27b0")))
    painter.drawRect(8, 4, 48, 10)
    
    # رسم شمس بسيطة
    painter.setPen(QPen(QColor("#ffc107"), 2))
    painter.setBrush(QBrush(QColor("#ffeb3b")))
    painter.drawEllipse(20, 25, 15, 15)
    
    # رسم جبل
    points = [QPoint(8, 60), QPoint(30, 35), QPoint(56, 60)]
    painter.setPen(Qt.PenStyle.NoPen)
    painter.setBrush(QBrush(QColor("#4caf50")))
    painter.drawPolygon(points)
    
    painter.end()
    return pixmap

def create_file_icon():
    """إنشاء أيقونة ملف عام"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم ورقة
    painter.setPen(QPen(QColor("#666666"), 1))
    painter.setBrush(QBrush(QColor("#ffffff")))
    painter.drawRect(8, 4, 48, 56)
    
    # رسم شريط رمادي في الأعلى
    painter.setPen(Qt.PenStyle.NoPen)
    painter.setBrush(QBrush(QColor("#9e9e9e")))
    painter.drawRect(8, 4, 48, 10)
    
    # رسم خطوط تمثل نص
    painter.setPen(QPen(QColor("#9e9e9e"), 2))
    painter.drawLine(16, 25, 48, 25)
    painter.drawLine(16, 35, 48, 35)
    painter.drawLine(16, 45, 40, 45)
    
    painter.end()
    return pixmap

def save_icons():
    """حفظ جميع الأيقونات"""
    app = QApplication([])
    
    resources_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources")
    
    # إنشاء وحفظ الأيقونات
    create_pdf_icon().save(os.path.join(resources_dir, "pdf.png"))
    create_word_icon().save(os.path.join(resources_dir, "word.png"))
    create_excel_icon().save(os.path.join(resources_dir, "excel.png"))
    create_image_icon().save(os.path.join(resources_dir, "image.png"))
    create_file_icon().save(os.path.join(resources_dir, "file.png"))
    
    print("تم إنشاء أيقونات الملفات بنجاح!")

if __name__ == "__main__":
    save_icons()
