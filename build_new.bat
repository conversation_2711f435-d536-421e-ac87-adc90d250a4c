@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ===== بدء عملية بناء البرنامج =====
echo.

:: تعيين المسارات
set "PYTHON=python"
set "PYINSTALLER=pyinstaller"
set "APP_NAME=أرشيف الدائرة البشرية"
set "APP_VERSION=1.0"
set "MAIN_SCRIPT=main.py"
set "SPEC_FILE=archiver.spec"
set "ISS_FILE=installer_fixed.iss"
set "OUTPUT_DIR=Output"

:: تنظيف البناء السابق
echo ===== تنظيف البناء السابق =====
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "%OUTPUT_DIR%" (
    rmdir /s /q "%OUTPUT_DIR%"
    mkdir "%OUTPUT_DIR%"
)

:: بناء البرنامج
echo ===== بناء البرنامج التنفيذي =====
%PYTHON% -m PyInstaller --clean --noconfirm --windowed --icon=resources/logo.png.ico --name "%APP_NAME%" "%MAIN_SCRIPT%"

if %ERRORLEVEL% NEQ 0 (
    echo ! خطأ: فشل بناء البرنامج التنفيذي.
    pause
    exit /b 1
)

:: التحقق من Inno Setup
echo ===== التحقق من وجود Inno Setup =====
set "INNO_PATH="

if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
) else (
    echo ! خطأ: Inno Setup غير مثبت.
    echo الرجاء تنزيل وتثبيت Inno Setup من:
    echo https://jrsoftware.org/isdl.php
    pause
    exit /b 1
)

echo تم العثور على Inno Setup في: %INNO_PATH%

:: بناء ملف التثبيت
echo ===== بناء ملف التثبيت =====
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
"%INNO_PATH%" /q "%ISS_FILE%"

if %ERRORLEVEL% NEQ 0 (
    echo ! خطأ: فشل بناء ملف التثبيت.
    pause
    exit /b 1
)

echo ===== تم بناء البرنامج وملف التثبيت بنجاح =====
echo.
echo تم فتح مجلد الإخراج...
start "" "%OUTPUT_DIR%"

pause
