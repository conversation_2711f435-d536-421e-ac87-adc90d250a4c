@echo off
echo ===== بدء عملية إنشاء ملف تنفيذي واحد =====

echo.
echo 1. إنشاء مجلدات الإخراج إذا لم تكن موجودة
if not exist "dist" mkdir "dist"

echo.
echo 2. تثبيت المكتبات المطلوبة
pip install -r requirements.txt
pip install pyinstaller

echo.
echo 3. تنظيف مخرجات البناء السابقة
if exist "dist\أرشفة.exe" del "dist\أرشفة.exe"
if exist "build" rmdir /s /q "build"

echo.
echo 4. إنشاء الملف التنفيذي باستخدام PyInstaller
pyinstaller --clean --noconfirm onefile.spec

echo.
echo 5. التحقق من نجاح عملية البناء
if exist "dist\أرشفة.exe" (
    echo تم إنشاء الملف التنفيذي بنجاح!
    echo يمكنك العثور على الملف التنفيذي في: %CD%\dist\أرشفة.exe
    
    echo.
    echo 6. إنشاء اختصار على سطح المكتب
    echo @echo off > create_shortcut.bat
    echo set SCRIPT="%TEMP%\%RANDOM%-%RANDOM%-%RANDOM%-%RANDOM%.vbs" >> create_shortcut.bat
    echo echo Set oWS = WScript.CreateObject("WScript.Shell") >> %%SCRIPT%% >> create_shortcut.bat
    echo echo sLinkFile = "%USERPROFILE%\Desktop\أرشفة.lnk" >> %%SCRIPT%% >> create_shortcut.bat
    echo echo Set oLink = oWS.CreateShortcut(sLinkFile) >> %%SCRIPT%% >> create_shortcut.bat
    echo echo oLink.TargetPath = "%CD%\dist\أرشفة.exe" >> %%SCRIPT%% >> create_shortcut.bat
    echo echo oLink.WorkingDirectory = "%CD%\dist" >> %%SCRIPT%% >> create_shortcut.bat
    echo echo oLink.Description = "برنامج أرشفة المستندات" >> %%SCRIPT%% >> create_shortcut.bat
    echo echo oLink.IconLocation = "%CD%\resources\logo.png.ico" >> %%SCRIPT%% >> create_shortcut.bat
    echo echo oLink.Save >> %%SCRIPT%% >> create_shortcut.bat
    echo cscript //nologo %%SCRIPT%% >> create_shortcut.bat
    echo del %%SCRIPT%% >> create_shortcut.bat
    
    call create_shortcut.bat
    del create_shortcut.bat
    
    echo تم إنشاء اختصار على سطح المكتب بنجاح!
) else (
    echo حدث خطأ أثناء إنشاء الملف التنفيذي.
    echo يرجى التحقق من الأخطاء المذكورة أعلاه.
)

echo.
echo ===== اكتملت عملية إنشاء الملف التنفيذي =====
echo.

pause
