import os
from datetime import datetime

class Document:
    """نموذج المستند في نظام الأرشفة"""
    
    def __init__(self, id=None, title="", description="", file_path="", file_type="", 
                 file_size=0, original_filename="", import_date=None, creation_date=None, 
                 modification_date=None, keywords=""):
        """تهيئة المستند"""
        self.id = id
        self.title = title
        self.description = description
        self.file_path = file_path
        self.file_type = file_type
        self.file_size = file_size
        self.original_filename = original_filename
        self.import_date = import_date or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.creation_date = creation_date or self.import_date
        self.modification_date = modification_date or self.import_date
        self.keywords = keywords
        self.categories = []
        self.custom_metadata = {}
    
    @classmethod
    def from_dict(cls, data):
        """إنشاء كائن مستند من قاموس"""
        return cls(
            id=data.get('id'),
            title=data.get('title', ''),
            description=data.get('description', ''),
            file_path=data.get('file_path', ''),
            file_type=data.get('file_type', ''),
            file_size=data.get('file_size', 0),
            original_filename=data.get('original_filename', ''),
            import_date=data.get('import_date'),
            creation_date=data.get('creation_date'),
            modification_date=data.get('modification_date'),
            keywords=data.get('keywords', '')
        )
    
    def to_dict(self):
        """تحويل المستند إلى قاموس"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'file_path': self.file_path,
            'file_type': self.file_type,
            'file_size': self.file_size,
            'original_filename': self.original_filename,
            'import_date': self.import_date,
            'creation_date': self.creation_date,
            'modification_date': self.modification_date,
            'keywords': self.keywords
        }
    
    def get_file_extension(self):
        """الحصول على امتداد الملف"""
        return os.path.splitext(self.file_path)[1].lower()
    
    def get_keywords_list(self):
        """الحصول على قائمة الكلمات المفتاحية"""
        if not self.keywords:
            return []
        return [k.strip() for k in self.keywords.split(',') if k.strip()]
    
    def add_keyword(self, keyword):
        """إضافة كلمة مفتاحية"""
        keywords = self.get_keywords_list()
        if keyword not in keywords:
            keywords.append(keyword)
            self.keywords = ', '.join(keywords)
    
    def remove_keyword(self, keyword):
        """إزالة كلمة مفتاحية"""
        keywords = self.get_keywords_list()
        if keyword in keywords:
            keywords.remove(keyword)
            self.keywords = ', '.join(keywords)
    
    def __str__(self):
        """تمثيل المستند كنص"""
        return f"{self.title} ({self.file_type})"
