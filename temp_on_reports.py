def on_reports(self):
    """عرض تقارير الأرشفة والتصدير"""
    # إنشاء نافذة حوار لعرض التقارير
    dialog = QDialog(self)
    dialog.setWindowTitle("تقارير الأرشفة والتصدير")
    dialog.setMinimumWidth(900)
    dialog.setMinimumHeight(700)
    dialog.setWindowIcon(QIcon("resources/reports.png"))

    # إضافة كائن قاعدة البيانات إلى نافذة الحوار
    dialog.db = self.db

    # التخطيط الرئيسي
    main_layout = QVBoxLayout(dialog)

    # عنوان النافذة
    title_label = QLabel("تقارير الأرشفة والتصدير")
    title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #3f51b5; margin-bottom: 15px;")
    title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    main_layout.addWidget(title_label)

    # إنشاء علامات التبويب
    tabs = QTabWidget()
    main_layout.addWidget(tabs)

    # ===== تبويب التقارير الزمنية =====
    time_reports_tab = QWidget()
    time_reports_layout = QVBoxLayout(time_reports_tab)

    # عنوان التقرير
    time_report_title = QLabel("تقارير المستندات المؤرشفة حسب الفترة الزمنية")
    time_report_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #3f51b5; margin-bottom: 10px;")
    time_reports_layout.addWidget(time_report_title)

    # خيارات التقرير
    options_layout = QHBoxLayout()

    # نوع التقرير
    report_type_label = QLabel("نوع التقرير:")
    options_layout.addWidget(report_type_label)

    report_type_combo = QComboBox()
    report_type_combo.addItem("يومي", "daily")
    report_type_combo.addItem("أسبوعي", "weekly")
    report_type_combo.addItem("شهري", "monthly")
    report_type_combo.addItem("سنوي", "yearly")
    options_layout.addWidget(report_type_combo)

    # تاريخ البداية
    start_date_label = QLabel("من تاريخ:")
    options_layout.addWidget(start_date_label)

    start_date_edit = QDateEdit()
    start_date_edit.setCalendarPopup(True)
    start_date_edit.setDate(QDate.currentDate().addMonths(-1))
    options_layout.addWidget(start_date_edit)

    # تاريخ النهاية
    end_date_label = QLabel("إلى تاريخ:")
    options_layout.addWidget(end_date_label)

    end_date_edit = QDateEdit()
    end_date_edit.setCalendarPopup(True)
    end_date_edit.setDate(QDate.currentDate())
    options_layout.addWidget(end_date_edit)

    # زر إنشاء التقرير
    generate_btn = QPushButton("إنشاء التقرير")
    generate_btn.setStyleSheet("""
        QPushButton {
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5c6bc0;
        }
    """)
    options_layout.addWidget(generate_btn)

    time_reports_layout.addLayout(options_layout)

    # ملخص التقرير
    summary_layout = QHBoxLayout()
    total_docs_label = QLabel("إجمالي المستندات: 0")
    summary_layout.addWidget(total_docs_label)
    summary_layout.addStretch()
    total_size_label = QLabel("إجمالي الحجم: 0 بايت")
    summary_layout.addWidget(total_size_label)
    time_reports_layout.addLayout(summary_layout)

    # جدول تفاصيل الملفات
    files_details_label = QLabel("تفاصيل الملفات:")
    files_details_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
    time_reports_layout.addWidget(files_details_label)

    files_details_table = QTableWidget()
    files_details_table.setColumnCount(7)
    files_details_table.setHorizontalHeaderLabels(["العدد", "اسم الملف", "النوع", "التصنيف", "الحجم", "تاريخ الأرشفة", "معاينة"])
    files_details_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
    files_details_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
    files_details_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
    files_details_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
    files_details_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
    files_details_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
    files_details_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
    files_details_table.setStyleSheet("""
        QTableWidget {
            border: 1px solid #c5cae9;
            border-radius: 4px;
            background-color: white;
            padding: 5px;
        }
        QHeaderView::section {
            background-color: #3f51b5;
            color: black;
            padding: 5px;
            font-weight: bold;
        }
    """)
    time_reports_layout.addWidget(files_details_table)

    # أزرار التصدير
    export_buttons_layout = QHBoxLayout()
    export_buttons_layout.addStretch()

    export_excel_btn = QPushButton("تصدير إلى Excel")
    export_excel_btn.setIcon(QIcon("resources/excel.png"))
    export_buttons_layout.addWidget(export_excel_btn)

    export_pdf_btn = QPushButton("تصدير إلى PDF")
    export_pdf_btn.setIcon(QIcon("resources/pdf.png"))
    export_buttons_layout.addWidget(export_pdf_btn)

    time_reports_layout.addLayout(export_buttons_layout)

    # ===== تبويب تقارير التصدير =====
    export_reports_tab = QWidget()
    export_reports_layout = QVBoxLayout(export_reports_tab)

    # عنوان التقرير
    export_report_title = QLabel("تقارير المستندات المصدرة")
    export_report_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #3f51b5; margin-bottom: 10px;")
    export_reports_layout.addWidget(export_report_title)

    # خيارات التقرير
    export_options_layout = QHBoxLayout()

    # تاريخ البداية
    export_start_date_label = QLabel("من تاريخ:")
    export_options_layout.addWidget(export_start_date_label)

    export_start_date_edit = QDateEdit()
    export_start_date_edit.setCalendarPopup(True)
    export_start_date_edit.setDate(QDate.currentDate().addMonths(-1))
    export_options_layout.addWidget(export_start_date_edit)

    # تاريخ النهاية
    export_end_date_label = QLabel("إلى تاريخ:")
    export_options_layout.addWidget(export_end_date_label)

    export_end_date_edit = QDateEdit()
    export_end_date_edit.setCalendarPopup(True)
    export_end_date_edit.setDate(QDate.currentDate())
    export_options_layout.addWidget(export_end_date_edit)

    # زر إنشاء التقرير
    export_generate_btn = QPushButton("إنشاء التقرير")
    export_generate_btn.setStyleSheet("""
        QPushButton {
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5c6bc0;
        }
    """)
    export_options_layout.addWidget(export_generate_btn)

    export_reports_layout.addLayout(export_options_layout)

    # ملخص التقرير
    export_summary_layout = QHBoxLayout()
    export_total_docs_label = QLabel("إجمالي المستندات المصدرة: 0")
    export_summary_layout.addWidget(export_total_docs_label)
    export_summary_layout.addStretch()
    export_total_size_label = QLabel("إجمالي الحجم: 0 بايت")
    export_summary_layout.addWidget(export_total_size_label)
    export_reports_layout.addLayout(export_summary_layout)

    # جدول تفاصيل الملفات المصدرة
    exported_docs_label = QLabel("تفاصيل الملفات المصدرة:")
    exported_docs_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
    export_reports_layout.addWidget(exported_docs_label)

    exported_docs_table = QTableWidget()
    exported_docs_table.setColumnCount(8)
    exported_docs_table.setHorizontalHeaderLabels(["العدد", "اسم الملف", "التصنيف", "النوع", "تاريخ الأرشفة", "تاريخ التصدير", "الحجم", "معاينة"])
    exported_docs_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
    exported_docs_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
    exported_docs_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
    exported_docs_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
    exported_docs_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
    exported_docs_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
    exported_docs_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
    exported_docs_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)
    exported_docs_table.setStyleSheet("""
        QTableWidget {
            border: 1px solid #c5cae9;
            border-radius: 4px;
            background-color: white;
            padding: 5px;
        }
        QHeaderView::section {
            background-color: #3f51b5;
            color: black;
            padding: 5px;
            font-weight: bold;
        }
    """)
    export_reports_layout.addWidget(exported_docs_table)

    # أزرار التصدير
    export_report_buttons_layout = QHBoxLayout()
    export_report_buttons_layout.addStretch()

    export_report_excel_btn = QPushButton("تصدير إلى Excel")
    export_report_excel_btn.setIcon(QIcon("resources/excel.png"))
    export_report_buttons_layout.addWidget(export_report_excel_btn)

    export_report_pdf_btn = QPushButton("تصدير إلى PDF")
    export_report_pdf_btn.setIcon(QIcon("resources/pdf.png"))
    export_report_buttons_layout.addWidget(export_report_pdf_btn)

    export_reports_layout.addLayout(export_report_buttons_layout)

    # إضافة التبويبات إلى العنصر الرئيسي
    tabs.addTab(time_reports_tab, "تقارير زمنية")
    tabs.addTab(export_reports_tab, "تقارير التصدير")

    # دالة لتنسيق حجم الملف
    def format_size(size):
        """تنسيق حجم الملف بشكل مقروء"""
        if size is None:
            return "0 بايت"

        if size < 1024:
            return f"{size} بايت"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} كيلوبايت"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} ميجابايت"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} جيجابايت"

    def generate_time_report():
        """إنشاء التقرير الزمني"""
        report_type = report_type_combo.currentData()
        start_date = start_date_edit.date().toString("yyyy-MM-dd")
        end_date = end_date_edit.date().toString("yyyy-MM-dd")

        # الحصول على إحصائيات المستندات
        stats = self.db.get_documents_stats(report_type, start_date, end_date)

        # تحديث ملخص التقرير
        total_docs_label.setText(f"إجمالي المستندات: {stats['total_documents']}")
        total_size_label.setText(f"إجمالي الحجم: {format_size(stats['total_size'])}")

        # عرض تفاصيل الملفات
        if 'files_details' in stats and stats['files_details']:
            files_details_table.setRowCount(len(stats['files_details']))

            for i, file_info in enumerate(stats['files_details']):
                # العدد
                count_item = QTableWidgetItem(f"{i+1}/{len(stats['files_details'])}")
                count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                files_details_table.setItem(i, 0, count_item)

                # اسم الملف
                file_name = os.path.basename(file_info['file_path']) if file_info['file_path'] else file_info['title']
                name_item = QTableWidgetItem(file_name)
                name_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                files_details_table.setItem(i, 1, name_item)

                # النوع
                file_type = file_info['file_type'] if file_info['file_type'] else "غير معروف"
                type_item = QTableWidgetItem(file_type)
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                files_details_table.setItem(i, 2, type_item)

                # التصنيف
                category_name = file_info.get('category_name', "غير مصنف")
                category_item = QTableWidgetItem(category_name)
                category_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                files_details_table.setItem(i, 3, category_item)

                # الحجم
                size = file_info.get('file_size', 0)
                size_item = QTableWidgetItem(format_size(size))
                size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                files_details_table.setItem(i, 4, size_item)

                # تاريخ الأرشفة
                import_date = file_info.get('import_date', "")
                import_date_item = QTableWidgetItem(import_date)
                import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                files_details_table.setItem(i, 5, import_date_item)

                # زر المعاينة
                preview_item = QTableWidgetItem("فتح الملف")
                preview_item.setForeground(QColor("#3f51b5"))
                preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                preview_item.setData(Qt.ItemDataRole.UserRole, file_info['file_path'])
                files_details_table.setItem(i, 6, preview_item)

            # تعديل ارتفاع الصفوف لاحتواء النص
            files_details_table.resizeRowsToContents()
        else:
            files_details_table.setRowCount(0)

    def generate_exported_docs_report():
        """عرض المستندات المصدرة"""
        start_date = export_start_date_edit.date().toString("yyyy-MM-dd")
        end_date = export_end_date_edit.date().toString("yyyy-MM-dd")

        # الحصول على المستندات المصدرة
        exported_docs = self.db.get_exported_documents(start_date, end_date)

        # عرض النتائج في الجدول
        exported_docs_table.setRowCount(len(exported_docs))

        total_size = 0

        for i, doc in enumerate(exported_docs):
            # العدد
            count_item = QTableWidgetItem(f"{i+1}/{len(exported_docs)}")
            count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            exported_docs_table.setItem(i, 0, count_item)

            # العنوان
            title_item = QTableWidgetItem(doc['title'])
            title_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
            exported_docs_table.setItem(i, 1, title_item)

            # التصنيف
            category_name = doc.get('category_name', "غير مصنف")
            category_item = QTableWidgetItem(category_name)
            category_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
            exported_docs_table.setItem(i, 2, category_item)

            # النوع
            file_type = doc.get('file_type', "غير معروف")
            type_item = QTableWidgetItem(file_type)
            type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            exported_docs_table.setItem(i, 3, type_item)

            # تاريخ الأرشفة
            import_date_item = QTableWidgetItem(doc['import_date'])
            import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            exported_docs_table.setItem(i, 4, import_date_item)

            # تاريخ التصدير
            export_date_item = QTableWidgetItem(doc['export_date'])
            export_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            exported_docs_table.setItem(i, 5, export_date_item)

            # الحجم
            size = doc.get('file_size', 0)
            total_size += size or 0
            size_item = QTableWidgetItem(format_size(size))
            size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            exported_docs_table.setItem(i, 6, size_item)

            # زر المعاينة
            if 'file_path' in doc and doc['file_path'] and os.path.exists(doc['file_path']):
                preview_item = QTableWidgetItem("فتح الملف")
                preview_item.setForeground(QColor("#3f51b5"))
                preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                preview_item.setData(Qt.ItemDataRole.UserRole, doc['file_path'])
            else:
                preview_item = QTableWidgetItem("غير متاح")
                preview_item.setForeground(QColor("#999999"))
                preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            exported_docs_table.setItem(i, 7, preview_item)

        # تعديل ارتفاع الصفوف لاحتواء النص
        exported_docs_table.resizeRowsToContents()

        # تحديث ملخص التقرير
        export_total_docs_label.setText(f"إجمالي المستندات المصدرة: {len(exported_docs)}")
        export_total_size_label.setText(f"إجمالي الحجم: {format_size(total_size)}")
