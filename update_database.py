import os
import sqlite3

def update_database():
    """تحديث قاعدة البيانات لإضافة العمود المفقود"""
    # استخدام مجلد AppData لتخزين قاعدة البيانات
    app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'أرشفة')
    if not os.path.exists(app_data_dir):
        os.makedirs(app_data_dir)
    db_path = os.path.join(app_data_dir, "archive.db")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # التحقق من وجود العمود قبل إضافته
    cursor.execute("PRAGMA table_info(categories)")
    columns = [column[1] for column in cursor.fetchall()]

    if 'allowed_file_types' not in columns:
        cursor.execute("ALTER TABLE categories ADD COLUMN allowed_file_types TEXT")
        conn.commit()
        print("تم إضافة العمود allowed_file_types بنجاح.")
    else:
        print("العمود allowed_file_types موجود بالفعل.")

    conn.close()

if __name__ == "__main__":
    update_database()