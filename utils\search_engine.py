import re
from datetime import datetime

class SearchEngine:
    """محرك البحث لنظام الأرشفة"""
    
    def __init__(self, database):
        """تهيئة محرك البحث"""
        self.db = database
    
    def search(self, query, category_id=None, date_from=None, date_to=None, file_type=None, limit=100, offset=0):
        """
        بحث متقدم في المستندات
        
        المعلمات:
            query (str): نص البحث
            category_id (int): معرف التصنيف للتصفية (اختياري)
            date_from (str): تاريخ البداية بتنسيق YYYY-MM-DD (اختياري)
            date_to (str): تاريخ النهاية بتنسيق YYYY-MM-DD (اختياري)
            file_type (str): نوع الملف للتصفية (اختياري)
            limit (int): الحد الأقصى لعدد النتائج
            offset (int): الإزاحة للصفحات
            
        العائد:
            list: قائمة المستندات المطابقة
        """
        # بناء استعلام SQL
        sql_query = """
        SELECT DISTINCT d.* FROM documents d
        LEFT JOIN document_categories dc ON d.id = dc.document_id
        LEFT JOIN custom_metadata cm ON d.id = cm.document_id
        WHERE 1=1
        """
        
        params = []
        
        # إضافة شرط البحث النصي
        if query:
            sql_query += """
            AND (
                d.title LIKE ? OR
                d.description LIKE ? OR
                d.keywords LIKE ? OR
                d.file_path LIKE ? OR
                cm.field_value LIKE ?
            )
            """
            search_pattern = f"%{query}%"
            params.extend([search_pattern] * 5)
        
        # إضافة شرط التصنيف
        if category_id:
            sql_query += " AND dc.category_id = ?"
            params.append(category_id)
        
        # إضافة شرط نوع الملف
        if file_type:
            sql_query += " AND d.file_type = ?"
            params.append(file_type)
        
        # إضافة شروط التاريخ
        if date_from:
            sql_query += " AND d.import_date >= ?"
            params.append(f"{date_from} 00:00:00")
        
        if date_to:
            sql_query += " AND d.import_date <= ?"
            params.append(f"{date_to} 23:59:59")
        
        # إضافة الترتيب والحدود
        sql_query += " ORDER BY d.import_date DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        # تنفيذ الاستعلام
        self.db.cursor.execute(sql_query, params)
        return [dict(row) for row in self.db.cursor.fetchall()]
    
    def search_by_keywords(self, keywords, operator="OR", **kwargs):
        """
        البحث باستخدام الكلمات المفتاحية
        
        المعلمات:
            keywords (list): قائمة الكلمات المفتاحية
            operator (str): العامل المنطقي بين الكلمات ("AND" أو "OR")
            **kwargs: معلمات إضافية للبحث
            
        العائد:
            list: قائمة المستندات المطابقة
        """
        if not keywords:
            return []
        
        # بناء استعلام البحث
        keyword_conditions = []
        params = []
        
        for keyword in keywords:
            keyword_conditions.append("d.keywords LIKE ?")
            params.append(f"%{keyword}%")
        
        logical_op = " AND " if operator.upper() == "AND" else " OR "
        keyword_query = f"({logical_op.join(keyword_conditions)})"
        
        # بناء استعلام SQL
        sql_query = f"""
        SELECT DISTINCT d.* FROM documents d
        LEFT JOIN document_categories dc ON d.id = dc.document_id
        WHERE {keyword_query}
        """
        
        # إضافة شروط إضافية
        if 'category_id' in kwargs and kwargs['category_id']:
            sql_query += " AND dc.category_id = ?"
            params.append(kwargs['category_id'])
        
        if 'file_type' in kwargs and kwargs['file_type']:
            sql_query += " AND d.file_type = ?"
            params.append(kwargs['file_type'])
        
        # إضافة الترتيب والحدود
        limit = kwargs.get('limit', 100)
        offset = kwargs.get('offset', 0)
        sql_query += " ORDER BY d.import_date DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        # تنفيذ الاستعلام
        self.db.cursor.execute(sql_query, params)
        return [dict(row) for row in self.db.cursor.fetchall()]
    
    def parse_search_query(self, query_text):
        """
        تحليل نص البحث إلى مكونات
        
        المعلمات:
            query_text (str): نص البحث
            
        العائد:
            dict: قاموس يحتوي على مكونات البحث
        """
        result = {
            'text': '',
            'keywords': [],
            'file_type': None,
            'date_from': None,
            'date_to': None
        }
        
        # استخراج الكلمات المفتاحية
        keyword_matches = re.findall(r'keyword:(\w+)', query_text)
        for match in keyword_matches:
            result['keywords'].append(match)
            query_text = query_text.replace(f'keyword:{match}', '')
        
        # استخراج نوع الملف
        type_match = re.search(r'type:(\w+)', query_text)
        if type_match:
            result['file_type'] = type_match.group(1)
            query_text = query_text.replace(f'type:{result["file_type"]}', '')
        
        # استخراج تاريخ البداية
        date_from_match = re.search(r'from:(\d{4}-\d{2}-\d{2})', query_text)
        if date_from_match:
            result['date_from'] = date_from_match.group(1)
            query_text = query_text.replace(f'from:{result["date_from"]}', '')
        
        # استخراج تاريخ النهاية
        date_to_match = re.search(r'to:(\d{4}-\d{2}-\d{2})', query_text)
        if date_to_match:
            result['date_to'] = date_to_match.group(1)
            query_text = query_text.replace(f'to:{result["date_to"]}', '')
        
        # النص المتبقي هو نص البحث
        result['text'] = query_text.strip()
        
        return result
