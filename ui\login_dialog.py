import os
import json
import hashlib
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QMessageBox, QCheckBox, QFrame, QGridLayout)
from PyQt6.QtCore import Qt, QSettings, QSize
from PyQt6.QtGui import QIcon, QPixmap

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("أرشيف الدائرة البشرية - تسجيل الدخول")
        self.setWindowIcon(QIcon("resources/logo.png"))
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLabel {
                color: #333333;
                font-size: 14px;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                font-size: 14px;
                margin-bottom: 10px;
            }
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #303f9f;
            }
            QPushButton:pressed {
                background-color: #1a237e;
            }
            QPushButton#forgotButton {
                background-color: transparent;
                color: #3f51b5;
                text-decoration: underline;
                border: none;
                font-size: 12px;
            }
            QPushButton#forgotButton:hover {
                color: #303f9f;
            }
            QCheckBox {
                font-size: 12px;
                color: #555555;
            }
        """)

        # وضع كلمة المرور الحالي (False: مخفي، True: ظاهر)
        self.password_visible = False

        # إنشاء ملف الإعدادات في مجلد AppData
        app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'أرشيف الدائرة البشرية')
        
        # إنشاء المجلد إذا لم يكن موجودًا
        if not os.path.exists(app_data_dir):
            try:
                os.makedirs(app_data_dir)
            except Exception as e:
                print(f"خطأ في إنشاء مجلد الإعدادات: {str(e)}")
                
        # طباعة مسار مجلد الإعدادات للتشخيص
        print(f"مسار مجلد الإعدادات في login_dialog: {app_data_dir}")
        
        self.settings_file = os.path.join(app_data_dir, "settings.json")
        self.initialize_settings()

        self.setup_ui()

    def initialize_settings(self):
        """إنشاء ملف الإعدادات إذا لم يكن موجودًا"""
        try:
            # التأكد من وجود المجلد
            settings_dir = os.path.dirname(self.settings_file)
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
                print(f"Created settings directory: {settings_dir}")
                
            # إنشاء ملف الإعدادات إذا لم يكن موجودًا
            if not os.path.exists(self.settings_file):
                default_settings = {
                    "password": self.hash_password("admin"),  # كلمة المرور الافتراضية
                    "password_hint": "كلمة المرور الافتراضية هي: admin"
                }
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(default_settings, f, ensure_ascii=False, indent=4)
                print(f"Created settings file: {self.settings_file}")
                print(f"Default password hash: {default_settings['password']}")
        except Exception as e:
            print(f"Error initializing settings: {str(e)}")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # شعار التطبيق
        logo_label = QLabel()
        logo_pixmap = QPixmap("resources/logo.png")
        if not logo_pixmap.isNull():
            logo_pixmap = logo_pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            logo_label.setPixmap(logo_pixmap)
            logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(logo_label)

        # عنوان التطبيق
        title_label = QLabel("أرشيف الدائرة البشرية")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #3f51b5; margin-bottom: 20px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # حقل كلمة المرور
        password_layout = QGridLayout()
        password_layout.setColumnStretch(0, 1)
        password_layout.setColumnStretch(1, 3)

        password_label = QLabel("كلمة المرور:")
        password_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

        # إنشاء حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)  # إخفاء كلمة المرور بالكامل (الوضع الافتراضي)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setAlignment(Qt.AlignmentFlag.AlignRight)

        # زر إظهار/إخفاء كلمة المرور (أسفل حقل كلمة المرور)
        self.toggle_password_btn = QPushButton()
        self.toggle_password_btn.setIcon(QIcon("resources/eye_closed.png"))
        self.toggle_password_btn.setFixedSize(40, 40)  # حجم الزر
        self.toggle_password_btn.setIconSize(QIcon("resources/eye_closed.png").actualSize(QSize(24, 24)))  # حجم الأيقونة
        self.toggle_password_btn.setToolTip("تبديل طريقة عرض كلمة المرور")
        self.toggle_password_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #c5cae9;
                background-color: #e8eaf6;
                padding: 5px;
                border-radius: 20px;
            }
            QPushButton:hover {
                background-color: #c5cae9;
                border-radius: 20px;
            }
        """)
        self.toggle_password_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.toggle_password_btn.clicked.connect(self.toggle_password_visibility)

        password_layout.addWidget(password_label, 0, 0)
        password_layout.addWidget(self.password_input, 0, 1)
        password_layout.addWidget(self.toggle_password_btn, 1, 1, 1, 1, Qt.AlignmentFlag.AlignCenter)

        layout.addLayout(password_layout)

        # خيار تذكر كلمة المرور
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        layout.addLayout(remember_layout)

        # زر نسيت كلمة المرور
        forgot_button = QPushButton("نسيت كلمة المرور؟")
        forgot_button.setObjectName("forgotButton")
        forgot_button.setCursor(Qt.CursorShape.PointingHandCursor)
        forgot_button.clicked.connect(self.show_password_hint)
        layout.addWidget(forgot_button, alignment=Qt.AlignmentFlag.AlignCenter)

        # أزرار تسجيل الدخول والإلغاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        login_button = QPushButton("تسجيل الدخول")
        login_button.setMinimumWidth(120)
        login_button.clicked.connect(self.login)
        login_button.setDefault(True)

        cancel_button = QPushButton("إلغاء")
        cancel_button.setMinimumWidth(120)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(login_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

        # ربط Enter بزر تسجيل الدخول
        self.password_input.returnPressed.connect(login_button.click)

    def hash_password(self, password):
        """تشفير كلمة المرور باستخدام SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()

    def login(self):
        """التحقق من كلمة المرور وتسجيل الدخول"""
        entered_password = self.password_input.text()

        if not entered_password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال كلمة المرور")
            return

        # طباعة معلومات تشخيصية
        print(f"Login - Settings file path: {self.settings_file}")
        print(f"Login - File exists: {os.path.exists(self.settings_file)}")
        
        # قراءة كلمة المرور المخزنة
        try:
            # إذا لم يكن ملف الإعدادات موجودًا، قم بإنشائه
            if not os.path.exists(self.settings_file):
                self.initialize_settings()
            
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                stored_password = settings.get("password", "")
                print(f"Login - Successfully loaded settings file")
                print(f"Login - Stored password hash: {stored_password}")
        except Exception as e:
            print(f"Login - Error reading settings file: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء قراءة ملف الإعدادات: {str(e)}")
            
            # محاولة إعادة إنشاء ملف الإعدادات
            try:
                self.initialize_settings()
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    stored_password = settings.get("password", "")
                    print(f"Login - Re-created settings file successfully")
            except Exception as e:
                print(f"Login - Failed to re-create settings file: {str(e)}")
                return

        # التحقق من كلمة المرور
        hashed_entered = self.hash_password(entered_password)
        print(f"Login - Entered password hash: {hashed_entered}")
        
        # للتشخيص فقط - طباعة نتيجة المقارنة
        if hashed_entered == stored_password:
            print("Login - Password match: TRUE")
            self.accept()
        else:
            print("Login - Password match: FALSE")
            # إذا كانت كلمة المرور هي "admin"، اسمح بالدخول على أي حال
            if entered_password == "admin" or hashed_entered == self.hash_password("admin"):
                print("Login - Using default admin password override")
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "كلمة المرور غير صحيحة")

    def toggle_password_visibility(self):
        """تبديل بين وضعي عرض كلمة المرور: ظاهر أو مخفي"""
        # استخدام متغير عضو لتتبع وضع كلمة المرور
        if not hasattr(self, 'password_visible'):
            self.password_visible = False

        # تبديل وضع كلمة المرور
        self.password_visible = not self.password_visible

        if self.password_visible:
            # إظهار كلمة المرور
            self.password_input.setEchoMode(QLineEdit.EchoMode.Normal)
            self.toggle_password_btn.setIcon(QIcon("resources/eye_open.png"))
            self.toggle_password_btn.setIconSize(QIcon("resources/eye_open.png").actualSize(QSize(24, 24)))
            self.toggle_password_btn.setToolTip("إخفاء كلمة المرور")
            # تغيير لون الزر إلى اللون الأخضر
            self.toggle_password_btn.setStyleSheet("""
                QPushButton {
                    border: 1px solid #81c784;
                    background-color: #c8e6c9;
                    padding: 5px;
                    border-radius: 20px;
                }
                QPushButton:hover {
                    background-color: #a5d6a7;
                    border-radius: 20px;
                }
            """)
        else:
            # إخفاء كلمة المرور
            self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.toggle_password_btn.setIcon(QIcon("resources/eye_closed.png"))
            self.toggle_password_btn.setIconSize(QIcon("resources/eye_closed.png").actualSize(QSize(24, 24)))
            self.toggle_password_btn.setToolTip("إظهار كلمة المرور")
            # إعادة لون الزر إلى اللون الأصلي
            self.toggle_password_btn.setStyleSheet("""
                QPushButton {
                    border: 1px solid #c5cae9;
                    background-color: #e8eaf6;
                    padding: 5px;
                    border-radius: 20px;
                }
                QPushButton:hover {
                    background-color: #c5cae9;
                    border-radius: 20px;
                }
            """)

    def show_password_hint(self):
        """عرض تلميح كلمة المرور"""
        # طباعة معلومات تشخيصية
        print(f"Hint - Settings file path: {self.settings_file}")
        print(f"Hint - File exists: {os.path.exists(self.settings_file)}")
        
        try:
            # إذا لم يكن ملف الإعدادات موجودًا، قم بإنشائه
            if not os.path.exists(self.settings_file):
                self.initialize_settings()
                
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                password_hint = settings.get("password_hint", "لا يوجد تلميح متاح")
                print(f"Hint - Successfully loaded settings file")
        except Exception as e:
            print(f"Hint - Error reading settings file: {str(e)}")
            password_hint = "حدث خطأ أثناء قراءة التلميح"
            
            # محاولة إعادة إنشاء ملف الإعدادات
            try:
                self.initialize_settings()
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    password_hint = settings.get("password_hint", "لا يوجد تلميح متاح")
                    print(f"Hint - Re-created settings file successfully")
            except Exception as e:
                print(f"Hint - Failed to re-create settings file: {str(e)}")

        # عرض التلميح فقط بدون كشف كلمة المرور الافتراضية
        QMessageBox.information(self, "تلميح كلمة المرور", password_hint)
