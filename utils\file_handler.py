import os
import shutil
import hashlib
from datetime import datetime
from PIL import Image
from PIL.ExifTags import TAGS

class FileHandler:
    """معالج الملفات لنظام الأرشفة"""

    def __init__(self, storage_dir="storage"):
        """تهيئة معالج الملفات"""
        self.storage_dir = storage_dir
        self._ensure_storage_dir()

    def _ensure_storage_dir(self):
        """التأكد من وجود مجلد التخزين"""
        if not os.path.exists(self.storage_dir):
            os.makedirs(self.storage_dir)

    def import_file(self, source_path, target_dir=None, subfolder=None, preserve_original=True):
        """استيراد ملف إلى نظام الأرشفة مع دعم المجلدات الفرعية"""
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"الملف غير موجود: {source_path}")

        # الحصول على معلومات الملف
        file_info = self.get_file_info(source_path)

        # إنشاء اسم ملف فريد باستخدام البصمة والتاريخ
        file_hash = self._calculate_file_hash(source_path)
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        file_ext = os.path.splitext(source_path)[1].lower()
        new_filename = f"{file_hash}_{timestamp}{file_ext}"

        # تحديد مسار التخزين
        if target_dir:
            storage_subdir = target_dir
            if subfolder:
                storage_subdir = os.path.join(storage_subdir, subfolder)
        else:
            year_month = datetime.now().strftime("%Y/%m")
            storage_subdir = os.path.join(self.storage_dir, year_month)

        if not os.path.exists(storage_subdir):
            os.makedirs(storage_subdir)

        # مسار الملف الجديد
        new_path = os.path.join(storage_subdir, new_filename)

        # نسخ الملف
        if preserve_original:
            shutil.copy2(source_path, new_path)
        else:
            shutil.move(source_path, new_path)

        # إضافة مسار التخزين إلى معلومات الملف
        file_info['file_path'] = new_path

        return file_info

    def get_file_info(self, file_path):
        """الحصول على معلومات الملف"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"الملف غير موجود: {file_path}")

        file_stat = os.stat(file_path)
        file_name = os.path.basename(file_path)
        file_ext = os.path.splitext(file_name)[1].lower()

        # معلومات أساسية
        file_info = {
            'original_filename': file_name,
            'file_type': self._get_file_type(file_ext),
            'file_size': file_stat.st_size,
            'creation_date': datetime.fromtimestamp(file_stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S"),
            'modification_date': datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
        }

        # استخراج البيانات الوصفية للصور
        if file_info['file_type'] == 'image':
            try:
                exif_data = self._extract_image_metadata(file_path)
                if exif_data:
                    file_info['metadata'] = exif_data
            except Exception as e:
                print(f"خطأ في استخراج بيانات الصورة: {e}")

        return file_info

    def _calculate_file_hash(self, file_path, algorithm='md5', block_size=65536):
        """حساب بصمة الملف"""
        hash_obj = hashlib.new(algorithm)

        with open(file_path, 'rb') as f:
            for block in iter(lambda: f.read(block_size), b''):
                hash_obj.update(block)

        return hash_obj.hexdigest()

    def _get_file_type(self, extension):
        """تحديد نوع الملف بناءً على الامتداد"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
        document_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf']
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
        audio_extensions = ['.mp3', '.wav', '.ogg', '.flac', '.aac']

        if extension in image_extensions:
            return 'image'
        elif extension in document_extensions:
            return 'document'
        elif extension in video_extensions:
            return 'video'
        elif extension in audio_extensions:
            return 'audio'
        else:
            return 'other'

    def _extract_image_metadata(self, image_path):
        """استخراج البيانات الوصفية من الصورة"""
        try:
            with Image.open(image_path) as img:
                # الحصول على بيانات EXIF
                exif_data = {}
                if hasattr(img, '_getexif') and img._getexif():
                    for tag, value in img._getexif().items():
                        tag_name = TAGS.get(tag, tag)
                        exif_data[tag_name] = str(value)

                # إضافة أبعاد الصورة
                exif_data['width'], exif_data['height'] = img.size

                return exif_data
        except Exception as e:
            print(f"خطأ في استخراج بيانات EXIF: {e}")
            return {}

    def delete_file(self, file_path):
        """حذف ملف من نظام التخزين"""
        if os.path.exists(file_path) and file_path.startswith(self.storage_dir):
            os.remove(file_path)
            return True
        return False

    def export_files(self, files_list, destination_dir, db=None):
        """تصدير ملفات إلى مجلد محدد في الجهاز المحلي

        Args:
            files_list: قائمة بمسارات الملفات المراد تصديرها
            destination_dir: مسار المجلد الوجهة
            db: كائن قاعدة البيانات (اختياري) للحصول على معلومات إضافية عن الملفات

        Returns:
            dict: معلومات عن عملية التصدير (عدد الملفات المصدرة، الملفات التي فشل تصديرها)
        """
        if not os.path.exists(destination_dir):
            os.makedirs(destination_dir)

        result = {
            'exported_count': 0,
            'failed_files': []
        }

        # قاموس لتخزين الأسماء المستخدمة لتجنب تكرار الأسماء
        used_filenames = {}

        for file_path in files_list:
            if not os.path.exists(file_path):
                result['failed_files'].append({
                    'path': file_path,
                    'reason': 'الملف غير موجود'
                })
                continue

            try:
                # محاولة الحصول على اسم الملف الأصلي من قاعدة البيانات
                original_filename = None

                # إذا كان كائن قاعدة البيانات متاحاً، نحاول الحصول على اسم الملف الأصلي
                if db:
                    # البحث عن المستند بواسطة مسار الملف
                    db.cursor.execute("SELECT original_filename, title FROM documents WHERE file_path = ?", (file_path,))
                    doc_info = db.cursor.fetchone()

                    if doc_info:
                        # استخدام اسم الملف الأصلي إذا كان متاحاً
                        if doc_info['original_filename']:
                            original_filename = doc_info['original_filename']
                        # وإلا استخدام عنوان المستند مع امتداد الملف
                        elif doc_info['title']:
                            file_ext = os.path.splitext(file_path)[1]
                            original_filename = f"{doc_info['title']}{file_ext}"

                # إذا لم نتمكن من الحصول على اسم الملف الأصلي، نستخدم اسم الملف الحالي
                if not original_filename:
                    original_filename = os.path.basename(file_path)

                    # إذا كان اسم الملف يبدو أنه تم إنشاؤه بواسطة النظام (يحتوي على بصمة وطابع زمني)
                    if '_' in original_filename and len(original_filename.split('_')[0]) >= 32:
                        # استخراج الامتداد
                        file_ext = os.path.splitext(original_filename)[1]
                        # إنشاء اسم ملف جديد باستخدام الطابع الزمني
                        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                        original_filename = f"exported_{timestamp}{file_ext}"

                # التأكد من عدم تكرار اسم الملف
                base_name, file_ext = os.path.splitext(original_filename)
                new_filename = original_filename
                counter = 1

                while new_filename in used_filenames:
                    new_filename = f"{base_name}_{counter}{file_ext}"
                    counter += 1

                # تسجيل اسم الملف كمستخدم
                used_filenames[new_filename] = True

                # مسار الملف الجديد في المجلد الوجهة
                dest_path = os.path.join(destination_dir, new_filename)

                # نسخ الملف
                shutil.copy2(file_path, dest_path)
                result['exported_count'] += 1

                # تحديث تاريخ التصدير في قاعدة البيانات إذا كان متاحاً
                if db:
                    try:
                        # البحث عن المستند بواسطة مسار الملف
                        db.cursor.execute("SELECT id FROM documents WHERE file_path = ?", (file_path,))
                        doc_info = db.cursor.fetchone()

                        if doc_info:
                            # تحديث تاريخ التصدير
                            db.update_document_export_date(doc_info['id'])
                    except Exception as e:
                        print(f"خطأ في تحديث تاريخ التصدير: {e}")

            except Exception as e:
                result['failed_files'].append({
                    'path': file_path,
                    'reason': str(e)
                })

        return result

    def export_category_files(self, category_id, destination_dir, db):
        """تصدير جميع ملفات تصنيف معين إلى مجلد محدد

        Args:
            category_id: معرف التصنيف
            destination_dir: مسار المجلد الوجهة
            db: كائن قاعدة البيانات

        Returns:
            dict: معلومات عن عملية التصدير
        """
        # إنشاء مجلد التصنيف في الوجهة
        category = db.get_category(category_id)
        if not category:
            return {'error': 'التصنيف غير موجود'}

        category_name = category['name']
        category_dir = os.path.join(destination_dir, category_name)

        if not os.path.exists(category_dir):
            os.makedirs(category_dir)

        # الحصول على جميع المستندات في التصنيف
        documents = db.get_documents(category_id)
        file_paths = [doc['file_path'] for doc in documents if 'file_path' in doc and doc['file_path']]

        # تصدير الملفات مع تمرير كائن قاعدة البيانات للحصول على معلومات إضافية
        result = self.export_files(file_paths, category_dir, db)
        result['category_name'] = category_name
        result['total_documents'] = len(documents)

        return result
