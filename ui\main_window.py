import sys
import os
import subprocess
import datetime

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QSplitter, QTreeWidget, QTreeWidgetItem, QListWidget,
                             QListWidgetItem, QLabel, QLineEdit, QPushButton,
                             QToolBar, QStatusBar, QFileDialog, QMenu, QMessageBox,
                             QTabWidget, QComboBox, QDialog, QFormLayout, QTextEdit,
                             QFrame, QGridLayout, QToolButton, QSizePolicy, QListView,
                             QHeaderView, QTableWidget, QTableWidgetItem, QApplication,
                             QRadioButton, QDateEdit, QGroupBox, QStackedWidget)
from PyQt6.QtCore import Qt, QSize, QMimeData, QUrl, pyqtSignal, QDate
from PyQt6.QtGui import QIcon, QAction, QPixmap, QFont, QColor, QDrag, QCursor, QPainter, QPen, QDesktopServices

from ui.import_dialog import ImportDialog
from ui.metadata_editor import MetadataEditor
from ui.search_panel import SearchPanel
from ui.scan_dialog import ScanDialog
from ui.settings_dialog import SettingsDialog

class DropFileListWidget(QListWidget):
    """قائمة مستندات تدعم السحب والإفلات"""

    filesDropped = pyqtSignal(list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        # إضافة دعم لأنماط العرض المختلفة
        self.setViewMode(QListView.ViewMode.ListMode)
        self.setResizeMode(QListView.ResizeMode.Adjust)
        self.setWrapping(True)
        self.setWordWrap(True)

    def dragEnterEvent(self, event):
        """معالجة بدء السحب"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dragMoveEvent(self, event):
        """معالجة حركة السحب"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        """معالجة الإفلات"""
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_paths.append(url.toLocalFile())

            if file_paths:
                self.filesDropped.emit(file_paths)
                event.acceptProposedAction()


class DraggableCategoriesTreeWidget(QTreeWidget):
    """شجرة تصنيفات تدعم السحب والإفلات لإعادة ترتيب العناصر"""

    categoryMoved = pyqtSignal(int, int)  # إشارة لإعلام النافذة الرئيسية بتحريك تصنيف (معرف التصنيف، الترتيب الجديد)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDragDropMode(QTreeWidget.DragDropMode.InternalMove)
        self.setSelectionMode(QTreeWidget.SelectionMode.SingleSelection)
        self.setDropIndicatorShown(True)

        # تخزين معلومات العنصر المسحوب
        self.dragged_item = None
        self.dragged_item_parent = None
        self.dragged_item_index = -1

    def startDrag(self, supportedActions):
        """بدء عملية السحب"""
        self.dragged_item = self.currentItem()
        if self.dragged_item:
            # تخزين معلومات العنصر قبل السحب
            self.dragged_item_parent = self.dragged_item.parent()
            if self.dragged_item_parent:
                self.dragged_item_index = self.dragged_item_parent.indexOfChild(self.dragged_item)
            else:
                self.dragged_item_index = self.indexOfTopLevelItem(self.dragged_item)

            # التحقق من أن العنصر ليس "جميع المستندات"
            category_id = self.dragged_item.data(0, Qt.ItemDataRole.UserRole)
            if category_id is None and self.dragged_item.text(0) == "جميع المستندات":
                return  # لا يمكن سحب "جميع المستندات"

            # بدء عملية السحب
            super().startDrag(supportedActions)

    def dropEvent(self, event):
        """معالجة إفلات العنصر"""
        if not self.dragged_item:
            return

        # التحقق من أن العنصر ليس "جميع المستندات"
        category_id = self.dragged_item.data(0, Qt.ItemDataRole.UserRole)
        if category_id is None and self.dragged_item.text(0) == "جميع المستندات":
            event.ignore()
            return

        # الحصول على موقع الإفلات
        try:
            # للإصدارات الأحدث من PyQt6
            drop_pos = event.position().toPoint()
        except AttributeError:
            # للإصدارات الأقدم من PyQt5
            drop_pos = event.pos()

        drop_item = self.itemAt(drop_pos)

        # التحقق من أن الإفلات ليس على "جميع المستندات"
        if drop_item and drop_item.text(0) == "جميع المستندات" and drop_item.data(0, Qt.ItemDataRole.UserRole) is None:
            event.ignore()
            return

        # تنفيذ الإفلات الافتراضي
        super().dropEvent(event)

        # حساب الترتيب الجديد
        new_parent = self.dragged_item.parent()
        if new_parent == self.dragged_item_parent:  # نفس المستوى
            if new_parent:
                new_index = new_parent.indexOfChild(self.dragged_item)
            else:
                new_index = self.indexOfTopLevelItem(self.dragged_item)

            # إرسال إشارة بتغيير الترتيب
            if new_index != self.dragged_item_index and category_id is not None:
                self.categoryMoved.emit(category_id, new_index)


class MainWindow(QMainWindow):
    """النافذة الرئيسية لبرنامج الأرشفة"""

    def __init__(self, database, file_handler, search_engine):
        """تهيئة النافذة الرئيسية"""
        super().__init__()
        self.db = database
        self.file_handler = file_handler
        self.search_engine = search_engine

        self.current_category_id = None
        self.current_document = None

        # إضافة متغير لتخزين نمط العرض الحالي
        self.current_view_mode = "list"  # القيم الممكنة: list, icon_small, icon_medium, icon_large, details

        self.init_ui()
        self.load_categories()
        self.load_documents()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("أرشيف الدائرة البشرية")
        self.setMinimumSize(1000, 600)

        # إضافة الشعار بجانب اسم البرنامج
        self.setWindowIcon(QIcon("resources/logo.png"))

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء شريط الحالة
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)

        # إنشاء الواجهة الرئيسية
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        main_layout = QVBoxLayout(main_widget)

        # إضافة شعار وعنوان البرنامج في الأعلى
        header_layout = QHBoxLayout()

        # إضافة الشعار
        logo_label = QLabel()
        logo_pixmap = QPixmap("resources/logo.png")
        logo_label.setPixmap(logo_pixmap.scaled(64, 64, Qt.AspectRatioMode.KeepAspectRatio))
        header_layout.addWidget(logo_label)

        # إضافة عنوان البرنامج
        title_label = QLabel("أرشيف الدائرة البشرية")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)

        # إضافة مساحة فارغة لدفع العناصر إلى اليمين
        header_layout.addStretch()

        main_layout.addLayout(header_layout)

        # إضافة إطار السحب والإفلات في الأعلى بتصميم عصري
        drop_frame = QFrame()
        drop_frame.setAcceptDrops(True)
        drop_frame.setMinimumHeight(140)  # تعيين ارتفاع أكبر
        drop_frame.setMaximumHeight(170)  # تحديد الحد الأقصى للارتفاع
        drop_frame.setStyleSheet("""
            QFrame {
                border: none;
                border-radius: 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #3f51b5, stop:1 #5c6bc0);
                padding: 20px;
                margin: 20px 40px;
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
                color: white;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #303f9f, stop:1 #3f51b5);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
            }
        """)

        drop_layout = QVBoxLayout(drop_frame)

        # تنظيم أفقي للمحتوى
        drop_content_layout = QHBoxLayout()
        drop_layout.addLayout(drop_content_layout)

        # أيقونة الملف
        drop_icon_label = QLabel()
        drop_icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        drop_icon_label.setPixmap(QIcon("resources/upload.png").pixmap(80, 80))
        drop_icon_label.setStyleSheet("background-color: rgba(255, 255, 255, 0.2); border-radius: 40px; padding: 10px;")
        drop_content_layout.addWidget(drop_icon_label)

        # نص توجيهي
        drop_text_layout = QVBoxLayout()
        drop_content_layout.addLayout(drop_text_layout)

        drop_title_label = QLabel("استيراد سريع للملفات")
        drop_title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        drop_title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        drop_title_label.setStyleSheet("color: white; margin-bottom: 8px;")
        drop_text_layout.addWidget(drop_title_label)

        drop_hint_label = QLabel("اسحب وأفلت الملفات هنا مباشرة للاستيراد")
        drop_hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        drop_hint_label.setFont(QFont("Arial", 14))
        drop_hint_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        drop_text_layout.addWidget(drop_hint_label)

        # إضافة نص إضافي
        drop_extra_hint = QLabel("يمكنك سحب ملف واحد أو عدة ملفات في وقت واحد")
        drop_extra_hint.setAlignment(Qt.AlignmentFlag.AlignCenter)
        drop_extra_hint.setFont(QFont("Arial", 12))
        drop_extra_hint.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-style: italic;")
        drop_text_layout.addWidget(drop_extra_hint)

        # تعريف دالة معالجة السحب والإفلات
        def dragEnterEvent(event):
            if event.mimeData().hasUrls():
                event.acceptProposedAction()
                drop_frame.setStyleSheet("""
                    QFrame {
                        border: none;
                        border-radius: 16px;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4caf50, stop:1 #8bc34a);
                        padding: 20px;
                        margin: 20px 40px;
                        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
                        color: white;
                    }
                """)

                # تغيير نص التلميح عند السحب
                drop_hint_label.setText("أفلت الملفات هنا للاستيراد")
                drop_hint_label.setStyleSheet("color: white; font-weight: bold;")
                drop_extra_hint.setText("جاهز للاستيراد...")
                drop_extra_hint.setStyleSheet("color: rgba(255, 255, 255, 0.9); font-style: italic;")

                # تغيير خلفية الأيقونة
                drop_icon_label.setStyleSheet("background-color: rgba(255, 255, 255, 0.3); border-radius: 40px; padding: 10px;")

        def dragLeaveEvent(event):
            drop_frame.setStyleSheet("""
                QFrame {
                    border: none;
                    border-radius: 16px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #3f51b5, stop:1 #5c6bc0);
                    padding: 20px;
                    margin: 20px 40px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
                    color: white;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #303f9f, stop:1 #3f51b5);
                    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
                }
            """)

            # إعادة النص الأصلي
            drop_hint_label.setText("اسحب وأفلت الملفات هنا مباشرة للاستيراد")
            drop_hint_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
            drop_extra_hint.setText("يمكنك سحب ملف واحد أو عدة ملفات في وقت واحد")
            drop_extra_hint.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-style: italic;")

            # إعادة خلفية الأيقونة
            drop_icon_label.setStyleSheet("background-color: rgba(255, 255, 255, 0.2); border-radius: 40px; padding: 10px;")

        def dropEvent(event):
            if event.mimeData().hasUrls():
                file_paths = []
                for url in event.mimeData().urls():
                    if url.isLocalFile():
                        file_paths.append(url.toLocalFile())

                if file_paths:
                    self.on_files_dropped(file_paths)
                    event.acceptProposedAction()

            # إعادة التصميم الأصلي بعد الإفلات
            drop_frame.setStyleSheet("""
                QFrame {
                    border: none;
                    border-radius: 16px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #3f51b5, stop:1 #5c6bc0);
                    padding: 20px;
                    margin: 20px 40px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
                    color: white;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #303f9f, stop:1 #3f51b5);
                    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
                }
            """)

            # إعادة النص الأصلي
            drop_hint_label.setText("اسحب وأفلت الملفات هنا مباشرة للاستيراد")
            drop_hint_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
            drop_extra_hint.setText("يمكنك سحب ملف واحد أو عدة ملفات في وقت واحد")
            drop_extra_hint.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-style: italic;")

            # إعادة خلفية الأيقونة
            drop_icon_label.setStyleSheet("background-color: rgba(255, 255, 255, 0.2); border-radius: 40px; padding: 10px;")

        # تعيين معالجات الأحداث
        drop_frame.dragEnterEvent = dragEnterEvent
        drop_frame.dragLeaveEvent = dragLeaveEvent
        drop_frame.dropEvent = dropEvent

        main_layout.addWidget(drop_frame)

        # إنشاء مقسم رئيسي
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # إنشاء لوحة التصنيفات
        categories_widget = QWidget()
        categories_layout = QVBoxLayout(categories_widget)

        categories_label = QLabel("التصنيفات")
        categories_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 5px;")
        categories_layout.addWidget(categories_label)

        # إضافة تلميح للسحب والإفلات
        drag_drop_hint = QLabel("يمكنك سحب وإفلات التصنيفات لإعادة ترتيبها")
        drag_drop_hint.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
        categories_layout.addWidget(drag_drop_hint)

        # استخدام شجرة التصنيفات القابلة للسحب
        self.categories_tree = DraggableCategoriesTreeWidget()
        self.categories_tree.setHeaderLabels(["الاسم"])
        self.categories_tree.setHeaderHidden(True)  # إخفاء رأس العمود
        self.categories_tree.setAnimated(True)  # تمكين التأثيرات الحركية
        self.categories_tree.setIconSize(QSize(24, 24))  # تكبير الأيقونات
        self.categories_tree.setFont(QFont("Arial", 11))  # تكبير الخط
        self.categories_tree.setMinimumHeight(300)  # تعيين الحد الأدنى للارتفاع
        self.categories_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 30px;
            }
            QTreeWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #e8eaf6;
            }
        """)
        self.categories_tree.itemClicked.connect(self.on_category_clicked)
        # ربط إشارة تحريك التصنيف بدالة تحديث الترتيب
        self.categories_tree.categoryMoved.connect(self.on_category_moved)
        categories_layout.addWidget(self.categories_tree)

        categories_buttons_layout = QHBoxLayout()

        self.add_category_btn = QPushButton(QIcon("resources/add.png"), "إضافة")
        self.add_category_btn.setToolTip("إضافة تصنيف جديد")
        self.add_category_btn.clicked.connect(self.on_add_category)
        categories_buttons_layout.addWidget(self.add_category_btn)

        self.edit_category_btn = QPushButton(QIcon("resources/edit.png"), "تعديل")
        self.edit_category_btn.setToolTip("تعديل التصنيف المحدد")
        self.edit_category_btn.clicked.connect(self.on_edit_category)
        categories_buttons_layout.addWidget(self.edit_category_btn)

        self.delete_category_btn = QPushButton(QIcon("resources/delete.png"), "حذف")
        self.delete_category_btn.setToolTip("حذف التصنيف المحدد")
        self.delete_category_btn.clicked.connect(self.on_delete_category)
        categories_buttons_layout.addWidget(self.delete_category_btn)

        categories_layout.addLayout(categories_buttons_layout)

        # إنشاء لوحة المستندات
        documents_widget = QWidget()
        documents_layout = QVBoxLayout(documents_widget)

        # إضافة شريط البحث
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.returnPressed.connect(self.on_search)
        search_layout.addWidget(self.search_input)

        self.search_btn = QPushButton("بحث")
        self.search_btn.clicked.connect(self.on_search)
        search_layout.addWidget(self.search_btn)

        self.advanced_search_btn = QPushButton("بحث متقدم")
        self.advanced_search_btn.clicked.connect(self.on_advanced_search)
        search_layout.addWidget(self.advanced_search_btn)

        documents_layout.addLayout(search_layout)

        # قائمة المستندات
        documents_label = QLabel("المستندات")
        documents_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 5px;")
        documents_layout.addWidget(documents_label)

        # إضافة تلميح للسحب والإفلات
        drag_drop_hint = QLabel("يمكنك سحب وإفلات الملفات هنا للاستيراد")
        drag_drop_hint.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
        documents_layout.addWidget(drag_drop_hint)

        # استخدام القائمة المخصصة التي تدعم السحب والإفلات
        self.documents_list = DropFileListWidget()
        self.documents_list.setAlternatingRowColors(True)
        self.documents_list.setIconSize(QSize(32, 32))  # تكبير الأيقونات
        self.documents_list.setFont(QFont("Arial", 11))  # تكبير الخط
        self.documents_list.setMinimumHeight(350)  # تعيين الحد الأدنى للارتفاع
        self.documents_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #eee;
                min-height: 40px;
            }
            QListWidget::item:selected {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #e8eaf6;
                border-radius: 4px;
            }
        """)
        self.documents_list.itemClicked.connect(self.on_document_clicked)
        self.documents_list.itemDoubleClicked.connect(self.on_document_double_clicked)
        self.documents_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.documents_list.customContextMenuRequested.connect(self.show_document_context_menu)
        self.documents_list.filesDropped.connect(self.on_files_dropped)

        documents_layout.addWidget(self.documents_list)

        # إنشاء لوحة تفاصيل المستند
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)

        details_label = QLabel("التفاصيل")
        details_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 5px;")
        details_layout.addWidget(details_label)

        # إنشاء علامات تبويب للتفاصيل
        self.details_tabs = QTabWidget()
        self.details_tabs.setMinimumHeight(400)  # تعيين الحد الأدنى للارتفاع
        self.details_tabs.setFont(QFont("Arial", 11))  # تكبير الخط
        self.details_tabs.setIconSize(QSize(24, 24))  # تكبير الأيقونات
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                padding: 15px;
            }
            QTabBar::tab {
                font-size: 12px;
                padding: 10px 20px;
            }
        """)

        # علامة تبويب المعلومات الأساسية
        basic_info_widget = QWidget()
        basic_info_layout = QFormLayout(basic_info_widget)
        basic_info_layout.setVerticalSpacing(10)
        basic_info_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)

        # تحسين شكل العناوين
        title_style = "font-weight: bold; color: #3f51b5; font-size: 12px;"
        value_style = "padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-size: 12px; min-height: 30px;"

        # العنوان
        title_label_header = QLabel("العنوان:")
        title_label_header.setStyleSheet(title_style)
        self.title_label = QLabel()
        self.title_label.setStyleSheet(value_style)
        self.title_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        basic_info_layout.addRow(title_label_header, self.title_label)

        # الوصف
        desc_label_header = QLabel("الوصف:")
        desc_label_header.setStyleSheet(title_style)
        self.description_label = QLabel()
        self.description_label.setStyleSheet(value_style)
        self.description_label.setWordWrap(True)
        self.description_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        basic_info_layout.addRow(desc_label_header, self.description_label)

        # النوع
        type_label_header = QLabel("النوع:")
        type_label_header.setStyleSheet(title_style)
        self.file_type_label = QLabel()
        self.file_type_label.setStyleSheet(value_style)
        self.file_type_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        basic_info_layout.addRow(type_label_header, self.file_type_label)

        # الحجم
        size_label_header = QLabel("الحجم:")
        size_label_header.setStyleSheet(title_style)
        self.file_size_label = QLabel()
        self.file_size_label.setStyleSheet(value_style)
        self.file_size_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        basic_info_layout.addRow(size_label_header, self.file_size_label)

        # تاريخ الاستيراد
        import_date_header = QLabel("تاريخ الاستيراد:")
        import_date_header.setStyleSheet(title_style)
        self.import_date_label = QLabel()
        self.import_date_label.setStyleSheet(value_style)
        self.import_date_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        basic_info_layout.addRow(import_date_header, self.import_date_label)

        # تاريخ الإنشاء
        creation_date_header = QLabel("تاريخ الإنشاء:")
        creation_date_header.setStyleSheet(title_style)
        self.creation_date_label = QLabel()
        self.creation_date_label.setStyleSheet(value_style)
        self.creation_date_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        basic_info_layout.addRow(creation_date_header, self.creation_date_label)

        # الكلمات المفتاحية
        keywords_header = QLabel("الكلمات المفتاحية:")
        keywords_header.setStyleSheet(title_style)
        self.keywords_label = QLabel()
        self.keywords_label.setStyleSheet(value_style)
        self.keywords_label.setWordWrap(True)
        self.keywords_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        basic_info_layout.addRow(keywords_header, self.keywords_label)

        self.details_tabs.addTab(basic_info_widget, "معلومات أساسية")

        # علامة تبويب البيانات الوصفية
        self.metadata_widget = QWidget()
        metadata_layout = QVBoxLayout(self.metadata_widget)
        self.metadata_table = QTableWidget()
        self.metadata_table.setColumnCount(2)
        self.metadata_table.setHorizontalHeaderLabels(["الحقل", "القيمة"])
        self.metadata_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.metadata_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        metadata_layout.addWidget(self.metadata_table)
        self.details_tabs.addTab(self.metadata_widget, "بيانات وصفية")

        details_layout.addWidget(self.details_tabs)

        # أزرار التحكم بالمستند
        document_buttons_layout = QHBoxLayout()

        self.open_document_btn = QPushButton(QIcon("resources/open.png"), "فتح")
        self.open_document_btn.setToolTip("فتح المستند")
        self.open_document_btn.clicked.connect(self.on_open_document)
        document_buttons_layout.addWidget(self.open_document_btn)

        self.edit_document_btn = QPushButton(QIcon("resources/edit.png"), "تعديل")
        self.edit_document_btn.setToolTip("تعديل بيانات المستند")
        self.edit_document_btn.clicked.connect(self.on_edit_document)
        document_buttons_layout.addWidget(self.edit_document_btn)

        self.delete_document_btn = QPushButton(QIcon("resources/delete.png"), "حذف")
        self.delete_document_btn.setToolTip("حذف المستند")
        self.delete_document_btn.clicked.connect(self.on_delete_document)
        document_buttons_layout.addWidget(self.delete_document_btn)

        details_layout.addLayout(document_buttons_layout)

        # إضافة الأقسام إلى المقسم الرئيسي
        splitter.addWidget(categories_widget)
        splitter.addWidget(documents_widget)
        splitter.addWidget(details_widget)

        # ضبط أحجام الأقسام
        splitter.setSizes([200, 300, 500])

        # تعطيل أزرار المستند في البداية
        self.toggle_document_buttons(False)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar("شريط الأدوات الرئيسي")
        toolbar.setIconSize(QSize(32, 32))
        self.addToolBar(toolbar)

        # زر استيراد
        import_action = QAction(QIcon("resources/import.png"), "استيراد", self)
        import_action.setToolTip("استيراد ملف جديد")
        import_action.triggered.connect(self.on_import)
        toolbar.addAction(import_action)

        # زر التصدير
        version_action = QAction(QIcon("resources/export.png"), "التصدير", self)
        version_action.setToolTip("تصدير مجلد واحد (أساسي أو فرعي) مع محتوياته إلى الجهاز المحلي")
        version_action.triggered.connect(self.on_version)
        toolbar.addAction(version_action)

        # زر المسح الضوئي
        scan_action = QAction(QIcon("resources/scan.png"), "المسح الضوئي", self)
        scan_action.setToolTip("مسح ضوئي لمستند جديد")
        scan_action.triggered.connect(self.on_scan)
        toolbar.addAction(scan_action)

        # زر العرض (بدون أيقونة)
        new_view_action = QAction("العرض", self)
        new_view_action.setToolTip("عرض محتويات المجلدات مع خيار عرض جميع الملفات")
        new_view_action.triggered.connect(self.show_all_files_view_dialog)
        toolbar.addAction(new_view_action)

        # زر تحديث
        refresh_action = QAction(QIcon("resources/refresh.png"), "تحديث", self)
        refresh_action.setToolTip("تحديث البيانات")
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)

        # زر التقارير
        reports_action = QAction(QIcon("resources/reports.png"), "التقارير", self)
        reports_action.setToolTip("عرض تقارير الأرشفة والتصدير")
        reports_action.triggered.connect(self.on_reports)  # إعادة استخدام دالة on_reports
        toolbar.addAction(reports_action)

        # زر تفعيل الترخيص
        activate_license_action = QAction(QIcon("resources/license.png"), "تفعيل الترخيص", self)
        activate_license_action.setToolTip("تفعيل الترخيص")
        activate_license_action.triggered.connect(self.on_activate_license)
        toolbar.addAction(activate_license_action)

        # زر الإعدادات
        settings_action = QAction(QIcon("resources/settings.png"), "الإعدادات", self)
        settings_action.setToolTip("إعدادات البرنامج")
        settings_action.triggered.connect(self.on_settings)
        toolbar.addAction(settings_action)

    def load_categories(self, parent_item=None, parent_id=None):
        """تحميل التصنيفات"""
        categories = self.db.get_categories(parent_id)

        if parent_item is None:
            self.categories_tree.clear()

        for category in categories:
            category_name = category['name']
            category_id = category['id']

            if parent_item is None:
                item = QTreeWidgetItem(self.categories_tree, [category_name])
            else:
                item = QTreeWidgetItem(parent_item, [category_name])

            item.setData(0, Qt.ItemDataRole.UserRole, category_id)

            # تحميل التصنيفات الفرعية
            self.load_categories(item, category_id)

        if parent_item is None:
            # إضافة عنصر "جميع المستندات"
            all_item = QTreeWidgetItem(self.categories_tree, ["جميع المستندات"])
            all_item.setData(0, Qt.ItemDataRole.UserRole, None)
            self.categories_tree.insertTopLevelItem(0, all_item)

            # توسيع الشجرة
            self.categories_tree.expandAll()

    def load_documents(self, category_id=None, search_term=None):
        """تحميل المستندات"""
        self.documents_list.clear()

        if search_term:
            documents = self.search_engine.search(search_term, category_id)
        else:
            documents = self.db.get_documents(category_id)

        for doc in documents:
            item = QListWidgetItem(doc['title'])
            item.setData(Qt.ItemDataRole.UserRole, doc['id'])
            self.documents_list.addItem(item)

        self.statusBar.showMessage(f"تم العثور على {len(documents)} مستند")

    def show_document_details(self, document_id):
        """عرض تفاصيل المستند"""
        if document_id is None:
            # مسح التفاصيل إذا لم يتم تحديد مستند
            self.title_label.setText("لم يتم تحديد مستند")
            self.description_label.clear()
            self.file_type_label.clear()
            self.file_size_label.clear()
            self.import_date_label.clear()
            self.creation_date_label.clear()
            self.keywords_label.clear()
            self.load_metadata(None)  # مسح البيانات الوصفية
            self.toggle_document_buttons(False)
            self.current_document = None
            return

        document = self.db.get_document(document_id)
        if not document:
            # إذا تم حذف المستند أثناء تحديده، قم بمسح التفاصيل
            self.show_document_details(None)
            return

        self.current_document = document

        # عرض المعلومات الأساسية
        self.title_label.setText(document.get('title', ''))
        self.description_label.setText(document.get('description', ''))
        self.file_type_label.setText(document.get('file_type', ''))

        # تنسيق حجم الملف
        file_size = document.get('file_size') or 0
        if file_size < 1024:
            size_str = f"{file_size} بايت"
        elif file_size < 1024 * 1024:
            size_str = f"{file_size / 1024:.2f} كيلوبايت"
        else:
            size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"
        self.file_size_label.setText(size_str)

        # تحويل التواريخ إلى نصوص قبل عرضها
        import_date = document.get('import_date')
        if import_date and isinstance(import_date, datetime.datetime):
            self.import_date_label.setText(import_date.strftime('%Y-%m-%d %H:%M:%S'))
        else:
            self.import_date_label.setText(str(import_date) if import_date else '')

        creation_date = document.get('creation_date')
        if creation_date and isinstance(creation_date, datetime.datetime):
            self.creation_date_label.setText(creation_date.strftime('%Y-%m-%d %H:%M:%S'))
        else:
            self.creation_date_label.setText(str(creation_date) if creation_date else '')

        self.keywords_label.setText(document.get('keywords', ''))

        # عرض البيانات الوصفية المخصصة
        self.load_metadata(document_id)

        # تفعيل أزرار المستند
        self.toggle_document_buttons(True)

    def load_metadata(self, document_id):
        """تحميل البيانات الوصفية المخصصة"""
        # مسح البيانات السابقة من الجدول
        self.metadata_table.setRowCount(0)

        if document_id is None:
            return

        # الحصول على البيانات الوصفية
        metadata = self.db.get_custom_metadata(document_id)
        if not metadata:
            return

        # عرض البيانات الوصفية في الجدول
        self.metadata_table.setRowCount(len(metadata))
        for i, row in enumerate(metadata):
            field_name = row.get('field_name', '')
            field_value = row.get('field_value', '')

            # التأكد من أن القيمة نصية قبل عرضها
            value_str = str(field_value) if field_value is not None else ""

            name_item = QTableWidgetItem(field_name)
            value_item = QTableWidgetItem(value_str)

            # جعل الخلايا غير قابلة للتعديل
            name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            value_item.setFlags(value_item.flags() & ~Qt.ItemFlag.ItemIsEditable)

            self.metadata_table.setItem(i, 0, name_item)
            self.metadata_table.setItem(i, 1, value_item)

    def toggle_document_buttons(self, enabled):
        """تفعيل/تعطيل أزرار المستند"""
        self.open_document_btn.setEnabled(enabled)
        self.edit_document_btn.setEnabled(enabled)
        self.delete_document_btn.setEnabled(enabled)

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_categories()
        self.load_documents(self.current_category_id)

        if self.current_document:
            self.show_document_details(self.current_document['id'])

    # معالجات الأحداث

    def on_category_clicked(self, item, column):
        """معالج النقر على تصنيف"""
        category_id = item.data(0, Qt.ItemDataRole.UserRole)
        self.current_category_id = category_id
        self.load_documents(category_id)

    def on_document_clicked(self, item):
        """معالج النقر على مستند"""
        document_id = item.data(Qt.ItemDataRole.UserRole)
        self.show_document_details(document_id)

    def on_document_double_clicked(self, item):
        """معالج النقر المزدوج على مستند لفتح الملف"""
        document_id = item.data(Qt.ItemDataRole.UserRole)

        # الحصول على معلومات المستند من قاعدة البيانات
        document = self.db.get_document(document_id)
        if not document:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على المستند")
            return

        file_path = document.get('file_path')
        if not file_path:
            QMessageBox.warning(self, "خطأ", "مسار الملف غير محدد")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "خطأ", f"الملف غير موجود في المسار المحدد:\n{file_path}")
            return

        try:
            # استخدام QDesktopServices لفتح الملف بطريقة آمنة ومتوافقة مع Qt
            url = QUrl.fromLocalFile(file_path)
            if not QDesktopServices.openUrl(url):
                QMessageBox.warning(self, "خطأ", "فشل في فتح الملف باستخدام التطبيق الافتراضي.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ فادح", f"حدث خطأ غير متوقع عند محاولة فتح الملف:\n{str(e)}")

    def on_import(self):
        """معالج استيراد ملف"""
        dialog = ImportDialog(self, self.db, self.file_handler)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.refresh_data()

    def on_search(self):
        """معالج البحث البسيط"""
        search_term = self.search_input.text().strip()
        if search_term:
            self.load_documents(self.current_category_id, search_term)
        else:
            self.load_documents(self.current_category_id)

    def on_advanced_search(self):
        """معالج البحث المتقدم"""
        search_panel = SearchPanel(self, self.search_engine, self.db)
        if search_panel.exec() == QDialog.DialogCode.Accepted:
            # تحديث قائمة المستندات بنتائج البحث
            self.documents_list.clear()

            for doc in search_panel.search_results:
                item = QListWidgetItem(doc['title'])
                item.setData(Qt.ItemDataRole.UserRole, doc['id'])
                self.documents_list.addItem(item)

            self.statusBar.showMessage(f"تم العثور على {len(search_panel.search_results)} مستند")

    def on_add_category(self):
        """معالج إضافة تصنيف"""
        parent_id = None
        selected_items = self.categories_tree.selectedItems()

        if selected_items:
            parent_id = selected_items[0].data(0, Qt.ItemDataRole.UserRole)
            # إذا كان العنصر المحدد هو "جميع المستندات"، فلا يوجد أب
            if parent_id is None and selected_items[0].text(0) == "جميع المستندات":
                parent_id = None

        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة تصنيف جديد")

        layout = QFormLayout(dialog)

        name_input = QLineEdit()
        layout.addRow("الاسم:", name_input)

        description_input = QTextEdit()
        layout.addRow("الوصف:", description_input)

        allowed_types_input = QLineEdit()
        allowed_types_input.setPlaceholderText("مثال: .pdf, .docx, image/jpeg")
        layout.addRow("أنواع الملفات المسموح بها (مفصولة بفواصل):", allowed_types_input)

        buttons_layout = QHBoxLayout()
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(dialog.accept)
        buttons_layout.addWidget(save_btn)

        layout.addRow("", buttons_layout)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            name = name_input.text().strip()
            description = description_input.toPlainText().strip()
            allowed_file_types = allowed_types_input.text().strip()

            if name:
                self.db.add_category(name, description, parent_id, allowed_file_types)
                self.load_categories()

    def on_edit_category(self):
        """معالج تعديل تصنيف"""
        selected_items = self.categories_tree.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        category_id = item.data(0, Qt.ItemDataRole.UserRole)

        # لا يمكن تعديل "جميع المستندات"
        if category_id is None and item.text(0) == "جميع المستندات":
            QMessageBox.warning(self, "تحذير", "لا يمكن تعديل هذا العنصر")
            return

        # الحصول على بيانات التصنيف
        self.db.cursor.execute("SELECT * FROM categories WHERE id = ?", (category_id,))
        category = dict(self.db.cursor.fetchone() or {})

        if not category:
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("تعديل تصنيف")

        layout = QFormLayout(dialog)

        name_input = QLineEdit(category['name'])
        layout.addRow("الاسم:", name_input)

        description_input = QTextEdit()
        description_input.setText(category['description'] or "")
        layout.addRow("الوصف:", description_input)

        allowed_types_input = QLineEdit(category.get('allowed_file_types', '') or "")
        allowed_types_input.setPlaceholderText("مثال: .pdf, .docx, image/jpeg")
        layout.addRow("أنواع الملفات المسموح بها (مفصولة بفواصل):", allowed_types_input)

        buttons_layout = QHBoxLayout()
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(dialog.accept)
        buttons_layout.addWidget(save_btn)

        layout.addRow("", buttons_layout)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            name = name_input.text().strip()
            description = description_input.toPlainText().strip()
            allowed_file_types = allowed_types_input.text().strip()

            if name:
                self.db.cursor.execute(
                    "UPDATE categories SET name = ?, description = ?, allowed_file_types = ? WHERE id = ?",
                    (name, description, allowed_file_types, category_id)
                )
                self.db.conn.commit()
                self.load_categories()

    def on_delete_category(self):
        """معالج حذف تصنيف"""
        selected_items = self.categories_tree.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        category_id = item.data(0, Qt.ItemDataRole.UserRole)

        # لا يمكن حذف "جميع المستندات"
        if category_id is None and item.text(0) == "جميع المستندات":
            QMessageBox.warning(self, "تحذير", "لا يمكن حذف هذا العنصر")
            return

        # التأكد من عدم وجود تصنيفات فرعية
        self.db.cursor.execute("SELECT COUNT(*) FROM categories WHERE parent_id = ?", (category_id,))
        subcategories_count = self.db.cursor.fetchone()[0]

        if subcategories_count > 0:
            QMessageBox.warning(
                self,
                "تحذير",
                "لا يمكن حذف هذا التصنيف لأنه يحتوي على تصنيفات فرعية. قم بحذف التصنيفات الفرعية أولاً."
            )
            return

        # التأكد من رغبة المستخدم في الحذف
        confirm = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف التصنيف '{item.text(0)}'؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            self.db.delete_category(category_id)
            self.load_categories()

    def on_category_moved(self, category_id, new_order):
        """معالج تحريك التصنيف"""
        # تحديث ترتيب التصنيف في قاعدة البيانات
        if self.db.update_category_order(category_id, new_order):
            # عرض رسالة نجاح
            self.statusBar.showMessage("تم تغيير ترتيب التصنيف بنجاح", 3000)
        else:
            # عرض رسالة خطأ
            self.statusBar.showMessage("فشل في تغيير ترتيب التصنيف", 3000)

    def on_open_document(self):
        """معالج فتح المستند"""
        if not self.current_document:
            return

        file_path = self.current_document['file_path']

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "خطأ", "الملف غير موجود")
            return

        # فتح الملف باستخدام التطبيق الافتراضي
        try:
            if os.name == 'nt':  # Windows
                os.startfile(file_path)
            elif os.name == 'posix':  # macOS/Linux
                subprocess.call(('xdg-open', file_path))
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح الملف: {str(e)}")

    def on_edit_document(self):
        """معالج تعديل المستند"""
        if not self.current_document:
            return

        editor = MetadataEditor(self, self.db, self.current_document)
        if editor.exec() == QDialog.DialogCode.Accepted:
            self.show_document_details(self.current_document['id'])

    def on_delete_document(self):
        """معالج حذف المستند"""
        if not self.current_document:
            return

        # التأكد من رغبة المستخدم في الحذف
        confirm = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف المستند '{self.current_document['title']}'؟\n"
            "سيتم حذف الملف من نظام التخزين أيضاً.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            # حذف الملف من نظام التخزين
            file_path = self.current_document['file_path']
            self.file_handler.delete_file(file_path)

            # حذف المستند من قاعدة البيانات
            self.db.delete_document(self.current_document['id'])

            # تحديث الواجهة
            self.current_document = None
            self.toggle_document_buttons(False)
            self.load_documents(self.current_category_id)

    def show_document_context_menu(self, position):
        """عرض قائمة السياق للمستند"""
        item = self.documents_list.itemAt(position)
        if not item:
            return

        document_id = item.data(Qt.ItemDataRole.UserRole)

        menu = QMenu()

        open_action = menu.addAction("فتح")
        edit_action = menu.addAction("تعديل")
        delete_action = menu.addAction("حذف")

        menu.addSeparator()

        assign_category_action = menu.addAction("تعيين تصنيف")

        action = menu.exec(self.documents_list.mapToGlobal(position))

        if action == open_action:
            self.show_document_details(document_id)
            self.on_open_document()
        elif action == edit_action:
            self.show_document_details(document_id)
            self.on_edit_document()
        elif action == delete_action:
            self.show_document_details(document_id)
            self.on_delete_document()
        elif action == assign_category_action:
            self.assign_category_to_document(document_id)

    def assign_category_to_document(self, document_id):
        """تعيين تصنيف للمستند"""
        dialog = QDialog(self)
        dialog.setWindowTitle("تعيين تصنيف")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        label = QLabel("اختر التصنيف:")
        layout.addWidget(label)

        category_combo = QComboBox()

        # إضافة التصنيفات (جميع التصنيفات بما في ذلك الفرعية)
        categories = self.db.get_all_categories()
        for category in categories:
            # تنسيق اسم التصنيف لعرض التسلسل الهرمي
            if category.get('parent_name'):
                display_name = f"{category['parent_name']} ← {category['name']}"
            else:
                display_name = category['name']
            category_combo.addItem(display_name, category['id'])

        layout.addWidget(category_combo)

        buttons_layout = QHBoxLayout()
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(dialog.accept)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            category_id = category_combo.currentData()
            if category_id:
                self.db.assign_category_to_document(document_id, category_id)
                self.refresh_data()

    def open_incoming_folder(self):
        """فتح مجلد الوارد لإضافة مستندات"""
        incoming_folder = os.path.join("storage", "الوارد")

        if not os.path.exists(incoming_folder):
            QMessageBox.warning(self, "خطأ", "مجلد الوارد غير موجود.")
            return

        # فتح المجلد باستخدام مستعرض الملفات الافتراضي
        try:
            if os.name == 'nt':  # Windows
                os.startfile(incoming_folder)
            elif os.name == 'posix':  # macOS/Linux
                subprocess.call(['open', incoming_folder])
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح المجلد: {str(e)}")

    def on_scan(self):
        """فتح نافذة المسح الضوئي"""
        scan_dialog = ScanDialog(self, self.file_handler, self.db)
        if scan_dialog.exec() == QDialog.DialogCode.Accepted:
            scanned_file = scan_dialog.scanned_file_path
            if scanned_file:
                if scan_dialog.imported_document_id:
                    # إذا تم استيراد الملف مباشرة، قم بتحديث الواجهة
                    self.refresh_data()
                    QMessageBox.information(self, "نجاح", "تم استيراد الملف الممسوح ضوئيًا بنجاح")
                else:
                    # إذا تم حفظ الملف فقط
                    QMessageBox.information(self, "نجاح", f"تم حفظ الملف الممسوح ضوئيًا: {scanned_file}")

    def on_reports(self):
        """عرض تقارير الأرشفة والتصدير"""
        # إنشاء نافذة حوار لعرض التقارير
        dialog = QDialog(self)
        dialog.setWindowTitle("تقارير الأرشفة والتصدير")
        dialog.setMinimumWidth(900)
        dialog.setMinimumHeight(700)
        dialog.setWindowIcon(QIcon("resources/reports.png"))

        # إضافة كائن قاعدة البيانات إلى نافذة الحوار
        dialog.db = self.db

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(dialog)

        # عنوان النافذة
        title_label = QLabel("تقارير الأرشفة والتصدير")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #3f51b5; margin-bottom: 15px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # إنشاء علامات التبويب
        tabs = QTabWidget()
        main_layout.addWidget(tabs)

        # ===== تبويب التقارير الزمنية =====
        time_reports_tab = QWidget()
        time_reports_layout = QVBoxLayout(time_reports_tab)

        # عنوان التقرير
        time_report_title = QLabel("تقارير المستندات المؤرشفة حسب الفترة الزمنية")
        time_report_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #3f51b5; margin-bottom: 10px;")
        time_reports_layout.addWidget(time_report_title)

        # خيارات التقرير
        options_layout = QHBoxLayout()

        # نوع التقرير
        report_type_label = QLabel("نوع التقرير:")
        options_layout.addWidget(report_type_label)

        report_type_combo = QComboBox()
        report_type_combo.addItem("يومي", "daily")
        report_type_combo.addItem("أسبوعي", "weekly")
        report_type_combo.addItem("شهري", "monthly")
        report_type_combo.addItem("سنوي", "yearly")
        options_layout.addWidget(report_type_combo)

        # تاريخ البداية
        start_date_label = QLabel("من تاريخ:")
        options_layout.addWidget(start_date_label)

        start_date_edit = QDateEdit()
        start_date_edit.setCalendarPopup(True)
        start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        options_layout.addWidget(start_date_edit)

        # تاريخ النهاية
        end_date_label = QLabel("إلى تاريخ:")
        options_layout.addWidget(end_date_label)

        end_date_edit = QDateEdit()
        end_date_edit.setCalendarPopup(True)
        end_date_edit.setDate(QDate.currentDate())
        options_layout.addWidget(end_date_edit)

        # زر إنشاء التقرير
        generate_btn = QPushButton("إنشاء التقرير")
        generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5c6bc0;
            }
        """)
        options_layout.addWidget(generate_btn)

        time_reports_layout.addLayout(options_layout)

        # ملخص التقرير
        summary_layout = QHBoxLayout()
        total_docs_label = QLabel("إجمالي المستندات: 0")
        summary_layout.addWidget(total_docs_label)
        summary_layout.addStretch()
        total_size_label = QLabel("إجمالي الحجم: 0 بايت")
        summary_layout.addWidget(total_size_label)
        time_reports_layout.addLayout(summary_layout)

        # جدول تفاصيل الملفات
        files_details_label = QLabel("تفاصيل الملفات:")
        files_details_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        time_reports_layout.addWidget(files_details_label)

        files_details_table = QTableWidget()
        files_details_table.setColumnCount(7)
        files_details_table.setHorizontalHeaderLabels(["العدد", "اسم الملف", "النوع", "التصنيف", "الحجم", "تاريخ الأرشفة", "معاينة"])
        files_details_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        files_details_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        files_details_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        files_details_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        files_details_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        files_details_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        files_details_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        files_details_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #3f51b5;
                color: black;
                padding: 5px;
                font-weight: bold;
            }
        """)
        time_reports_layout.addWidget(files_details_table)

        # أزرار التصدير
        export_buttons_layout = QHBoxLayout()
        export_buttons_layout.addStretch()

        export_excel_btn = QPushButton("تصدير إلى Excel")
        export_excel_btn.setIcon(QIcon("resources/excel.png"))
        export_buttons_layout.addWidget(export_excel_btn)

        export_pdf_btn = QPushButton("تصدير إلى PDF")
        export_pdf_btn.setIcon(QIcon("resources/pdf.png"))
        export_buttons_layout.addWidget(export_pdf_btn)

        time_reports_layout.addLayout(export_buttons_layout)

        # ===== تبويب تقارير التصدير =====
        export_reports_tab = QWidget()
        export_reports_layout = QVBoxLayout(export_reports_tab)

        # عنوان التقرير
        export_report_title = QLabel("تقارير المستندات المصدرة")
        export_report_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #3f51b5; margin-bottom: 10px;")
        export_reports_layout.addWidget(export_report_title)

        # خيارات التقرير
        export_options_layout = QHBoxLayout()

        # تاريخ البداية
        export_start_date_label = QLabel("من تاريخ:")
        export_options_layout.addWidget(export_start_date_label)

        export_start_date_edit = QDateEdit()
        export_start_date_edit.setCalendarPopup(True)
        export_start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        export_options_layout.addWidget(export_start_date_edit)

        # تاريخ النهاية
        export_end_date_label = QLabel("إلى تاريخ:")
        export_options_layout.addWidget(export_end_date_label)

        export_end_date_edit = QDateEdit()
        export_end_date_edit.setCalendarPopup(True)
        export_end_date_edit.setDate(QDate.currentDate())
        export_options_layout.addWidget(export_end_date_edit)

        # زر إنشاء التقرير
        export_generate_btn = QPushButton("إنشاء التقرير")
        export_generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5c6bc0;
            }
        """)
        export_options_layout.addWidget(export_generate_btn)

        export_reports_layout.addLayout(export_options_layout)

        # ملخص التقرير
        export_summary_layout = QHBoxLayout()
        export_total_docs_label = QLabel("إجمالي المستندات المصدرة: 0")
        export_summary_layout.addWidget(export_total_docs_label)
        export_summary_layout.addStretch()
        export_total_size_label = QLabel("إجمالي الحجم: 0 بايت")
        export_summary_layout.addWidget(export_total_size_label)
        export_reports_layout.addLayout(export_summary_layout)

        # جدول تفاصيل الملفات المصدرة
        exported_docs_label = QLabel("تفاصيل الملفات المصدرة:")
        exported_docs_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        export_reports_layout.addWidget(exported_docs_label)

        exported_docs_table = QTableWidget()
        exported_docs_table.setColumnCount(8)
        exported_docs_table.setHorizontalHeaderLabels(["العدد", "اسم الملف", "التصنيف", "النوع", "تاريخ الأرشفة", "تاريخ التصدير", "الحجم", "معاينة"])
        exported_docs_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        exported_docs_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        exported_docs_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        exported_docs_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        exported_docs_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        exported_docs_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        exported_docs_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        exported_docs_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)
        exported_docs_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #3f51b5;
                color: black;
                padding: 5px;
                font-weight: bold;
            }
        """)
        export_reports_layout.addWidget(exported_docs_table)

        # أزرار التصدير
        export_report_buttons_layout = QHBoxLayout()
        export_report_buttons_layout.addStretch()

        export_report_excel_btn = QPushButton("تصدير إلى Excel")
        export_report_excel_btn.setIcon(QIcon("resources/excel.png"))
        export_report_buttons_layout.addWidget(export_report_excel_btn)

        export_report_pdf_btn = QPushButton("تصدير إلى PDF")
        export_report_pdf_btn.setIcon(QIcon("resources/pdf.png"))
        export_report_buttons_layout.addWidget(export_report_pdf_btn)

        export_reports_layout.addLayout(export_report_buttons_layout)

        # إضافة التبويبات إلى العنصر الرئيسي
        tabs.addTab(time_reports_tab, "تقارير زمنية")
        tabs.addTab(export_reports_tab, "تقارير التصدير")

        # دالة لتنسيق حجم الملف
        def format_size(size):
            """تنسيق حجم الملف بشكل مقروء"""
            if size is None:
                return "0 بايت"

            if size < 1024:
                return f"{size} بايت"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} كيلوبايت"
            elif size < 1024 * 1024 * 1024:
                return f"{size / (1024 * 1024):.1f} ميجابايت"
            else:
                return f"{size / (1024 * 1024 * 1024):.1f} جيجابايت"

        def generate_time_report():
            """إنشاء التقرير الزمني"""
            report_type = report_type_combo.currentData()
            start_date = start_date_edit.date().toString("yyyy-MM-dd")
            end_date = end_date_edit.date().toString("yyyy-MM-dd")

            # الحصول على إحصائيات المستندات
            stats = self.db.get_documents_stats(report_type, start_date, end_date)

            # تحديث ملخص التقرير
            total_docs_label.setText(f"إجمالي المستندات: {stats['total_documents']}")
            total_size_label.setText(f"إجمالي الحجم: {format_size(stats['total_size'])}")

            # عرض تفاصيل الملفات
            if 'files_details' in stats and stats['files_details']:
                files_details_table.setRowCount(len(stats['files_details']))

                for i, file_info in enumerate(stats['files_details']):
                    # العدد
                    count_item = QTableWidgetItem(f"{i+1}/{len(stats['files_details'])}")
                    count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 0, count_item)

                    # اسم الملف
                    file_name = os.path.basename(file_info['file_path']) if file_info['file_path'] else file_info['title']
                    name_item = QTableWidgetItem(file_name)
                    name_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                    files_details_table.setItem(i, 1, name_item)

                    # النوع
                    file_type = file_info['file_type'] if file_info['file_type'] else "غير معروف"
                    type_item = QTableWidgetItem(file_type)
                    type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 2, type_item)

                    # التصنيف
                    category_name = file_info.get('category_name', "غير مصنف")
                    category_item = QTableWidgetItem(category_name)
                    category_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                    files_details_table.setItem(i, 3, category_item)

                    # الحجم
                    size = file_info.get('file_size', 0)
                    size_item = QTableWidgetItem(format_size(size))
                    size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 4, size_item)

                    # تاريخ الأرشفة
                    import_date = file_info.get('import_date', "")
                    import_date_item = QTableWidgetItem(import_date)
                    import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 5, import_date_item)

                    # زر المعاينة
                    preview_item = QTableWidgetItem("فتح الملف")
                    preview_item.setForeground(QColor("#3f51b5"))
                    preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    preview_item.setData(Qt.ItemDataRole.UserRole, file_info['file_path'])
                    files_details_table.setItem(i, 6, preview_item)

                # تعديل ارتفاع الصفوف لاحتواء النص
                files_details_table.resizeRowsToContents()
            else:
                files_details_table.setRowCount(0)

        def generate_exported_docs_report():
            """عرض المستندات المصدرة"""
            start_date = export_start_date_edit.date().toString("yyyy-MM-dd")
            end_date = export_end_date_edit.date().toString("yyyy-MM-dd")

            # الحصول على المستندات المصدرة
            exported_docs = self.db.get_exported_documents(start_date, end_date)

            # عرض النتائج في الجدول
            exported_docs_table.setRowCount(len(exported_docs))

            total_size = 0

            for i, doc in enumerate(exported_docs):
                # العدد
                count_item = QTableWidgetItem(f"{i+1}/{len(exported_docs)}")
                count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 0, count_item)

                # العنوان
                title_item = QTableWidgetItem(doc['title'])
                title_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                exported_docs_table.setItem(i, 1, title_item)

                # التصنيف
                category_name = doc.get('category_name', "غير مصنف")
                category_item = QTableWidgetItem(category_name)
                category_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                exported_docs_table.setItem(i, 2, category_item)

                # النوع
                file_type = doc.get('file_type', "غير معروف")
                type_item = QTableWidgetItem(file_type)
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 3, type_item)

                # تاريخ الأرشفة
                import_date_item = QTableWidgetItem(doc['import_date'])
                import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 4, import_date_item)

                # تاريخ التصدير
                export_date_item = QTableWidgetItem(doc['export_date'])
                export_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 5, export_date_item)

                # الحجم
                size = doc.get('file_size', 0)
                total_size += size or 0
                size_item = QTableWidgetItem(format_size(size))
                size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 6, size_item)

                # زر المعاينة
                if 'file_path' in doc and doc['file_path'] and os.path.exists(doc['file_path']):
                    preview_item = QTableWidgetItem("فتح الملف")
                    preview_item.setForeground(QColor("#3f51b5"))
                    preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    preview_item.setData(Qt.ItemDataRole.UserRole, doc['file_path'])
                else:
                    preview_item = QTableWidgetItem("غير متاح")
                    preview_item.setForeground(QColor("#999999"))
                    preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 7, preview_item)

            # تعديل ارتفاع الصفوف لاحتواء النص
            exported_docs_table.resizeRowsToContents()

            # تحديث ملخص التقرير
            export_total_docs_label.setText(f"إجمالي المستندات المصدرة: {len(exported_docs)}")
            export_total_size_label.setText(f"إجمالي الحجم: {format_size(total_size)}")

        def export_to_excel(table, filename):
            """تصدير الجدول إلى ملف Excel"""
            try:
                # التحقق من تثبيت المكتبات المطلوبة
                try:
                    import pandas as pd
                    import openpyxl
                except ImportError:
                    QMessageBox.warning(
                        dialog,
                        "مكتبات مفقودة",
                        "يرجى تثبيت المكتبات المطلوبة باستخدام الأمر:\npip install pandas openpyxl"
                    )
                    return

                # تحويل بيانات الجدول إلى DataFrame
                data = []
                headers = []

                # الحصول على العناوين
                for j in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(j).text())

                # الحصول على البيانات
                for i in range(table.rowCount()):
                    row_data = []
                    for j in range(table.columnCount()):
                        item = table.item(i, j)
                        if item is not None:
                            row_data.append(item.text())
                        else:
                            row_data.append("")
                    data.append(row_data)

                # إنشاء DataFrame
                df = pd.DataFrame(data, columns=headers)

                # حفظ الملف
                file_path, _ = QFileDialog.getSaveFileName(
                    dialog,
                    "حفظ التقرير",
                    filename,
                    "Excel Files (*.xlsx)"
                )

                if file_path:
                    df.to_excel(file_path, index=False)
                    QMessageBox.information(dialog, "نجاح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

                    # فتح الملف بعد التصدير
                    try:
                        import os
                        os.startfile(file_path)
                    except Exception as open_error:
                        print(f"تعذر فتح الملف: {str(open_error)}")

            except Exception as e:
                QMessageBox.warning(dialog, "خطأ", f"فشل في تصدير التقرير: {str(e)}")
                # طباعة تفاصيل الخطأ للمساعدة في التشخيص
                import traceback
                traceback.print_exc()

        def export_to_pdf(table, title, filename):
            """تصدير الجدول إلى ملف PDF"""
            try:
                # التحقق من تثبيت المكتبات المطلوبة
                try:
                    import reportlab
                    import arabic_reshaper
                    import bidi.algorithm
                except ImportError:
                    QMessageBox.warning(
                        dialog,
                        "مكتبات مفقودة",
                        "يرجى تثبيت المكتبات المطلوبة باستخدام الأمر:\npip install reportlab arabic-reshaper python-bidi"
                    )
                    return

                from reportlab.lib import colors
                from reportlab.lib.pagesizes import A4, landscape
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                from reportlab.lib.enums import TA_CENTER, TA_RIGHT
                import datetime
                import os
                import sys

                # حفظ الملف
                file_path, _ = QFileDialog.getSaveFileName(
                    dialog,
                    "حفظ التقرير",
                    filename,
                    "PDF Files (*.pdf)"
                )

                if not file_path:
                    return

                # تسجيل الخط العربي
                # محاولة استخدام خط عربي من النظام إذا كان متاحاً
                arabic_font_path = None
                arabic_font_name = "Arabic"

                # قائمة بالخطوط العربية الشائعة في نظام Windows
                system_arabic_fonts = [
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'arial.ttf'), "Arial"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'tahoma.ttf'), "Tahoma"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'simpo.ttf'), "Simplified Arabic"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'arabtype.ttf'), "Arabic Typesetting"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'segoeui.ttf'), "Segoe UI")
                ]

                # التحقق من وجود الخطوط في النظام
                for font_path, font_name in system_arabic_fonts:
                    if os.path.exists(font_path):
                        arabic_font_path = font_path
                        arabic_font_name = font_name
                        break

                # إذا لم يتم العثور على خط عربي في النظام، استخدم الخط المضمن في المشروع
                if not arabic_font_path:
                    # استخدام الخط المضمن في المشروع
                    arabic_font_path = "resources/fonts/Amiri-Regular.ttf"
                    arabic_font_name = "Amiri"

                    # التحقق من وجود الخط
                    if not os.path.exists(arabic_font_path):
                        # إذا لم يكن الخط موجوداً، استخدم الخط الافتراضي
                        QMessageBox.warning(
                            dialog,
                            "تحذير",
                            "لم يتم العثور على خط عربي. سيتم استخدام الخط الافتراضي."
                        )

                # تسجيل الخط العربي إذا كان متاحاً
                if arabic_font_path and os.path.exists(arabic_font_path):
                    try:
                        pdfmetrics.registerFont(TTFont(arabic_font_name, arabic_font_path))
                        print(f"تم تسجيل الخط العربي: {arabic_font_name}")
                    except Exception as font_error:
                        print(f"خطأ في تسجيل الخط: {str(font_error)}")

                # تحويل بيانات الجدول إلى قائمة
                data = []

                # إضافة العناوين
                headers = []
                for j in range(table.columnCount()):
                    # معالجة النص العربي
                    header_text = table.horizontalHeaderItem(j).text()
                    try:
                        # إعادة تشكيل النص العربي وتحويله إلى النص ثنائي الاتجاه
                        reshaped_text = arabic_reshaper.reshape(header_text)
                        bidi_text = bidi.algorithm.get_display(reshaped_text)
                        headers.append(bidi_text)
                    except Exception as text_error:
                        print(f"خطأ في معالجة النص: {str(text_error)}")
                        headers.append(header_text)
                data.append(headers)

                # إضافة البيانات
                for i in range(table.rowCount()):
                    row_data = []
                    for j in range(table.columnCount()):
                        item = table.item(i, j)
                        if item is not None:
                            # معالجة النص العربي
                            cell_text = item.text()
                            try:
                                # إعادة تشكيل النص العربي وتحويله إلى النص ثنائي الاتجاه
                                reshaped_text = arabic_reshaper.reshape(cell_text)
                                bidi_text = bidi.algorithm.get_display(reshaped_text)
                                row_data.append(bidi_text)
                            except Exception as text_error:
                                print(f"خطأ في معالجة النص: {str(text_error)}")
                                row_data.append(cell_text)
                        else:
                            row_data.append("")
                    data.append(row_data)

                # إنشاء ملف PDF
                doc = SimpleDocTemplate(
                    file_path,
                    pagesize=landscape(A4),
                    rightMargin=30,
                    leftMargin=30,
                    topMargin=30,
                    bottomMargin=30
                )

                # إنشاء قائمة العناصر
                elements = []

                # إنشاء أنماط النصوص
                styles = getSampleStyleSheet()

                # إنشاء نمط للعنوان باستخدام الخط العربي
                title_style = ParagraphStyle(
                    name='ArabicTitle',
                    parent=styles['Heading1'],
                    fontName=arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica-Bold',
                    alignment=TA_CENTER,
                    fontSize=18
                )

                # إنشاء نمط للنص العادي باستخدام الخط العربي
                normal_style = ParagraphStyle(
                    name='ArabicNormal',
                    parent=styles['Normal'],
                    fontName=arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica',
                    alignment=TA_CENTER,
                    fontSize=12
                )

                # إضافة التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # معالجة العنوان والتاريخ للعربية
                try:
                    reshaped_title = arabic_reshaper.reshape(title)
                    bidi_title = bidi.algorithm.get_display(reshaped_title)

                    date_text = f"تاريخ التقرير: {current_datetime}"
                    reshaped_date = arabic_reshaper.reshape(date_text)
                    bidi_date = bidi.algorithm.get_display(reshaped_date)

                    # إضافة العنوان والتاريخ
                    elements.append(Paragraph(bidi_title, title_style))
                    elements.append(Paragraph(bidi_date, normal_style))
                except Exception as text_error:
                    print(f"خطأ في معالجة النص: {str(text_error)}")
                    elements.append(Paragraph(title, title_style))
                    elements.append(Paragraph(f"تاريخ التقرير: {current_datetime}", normal_style))

                elements.append(Spacer(1, 20))

                # إنشاء جدول
                table_obj = Table(data)

                # تنسيق الجدول
                table_style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica-Bold'),
                    ('FONTNAME', (0, 1), (-1, -1), arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ])

                # تطبيق التنسيق على الجدول
                table_obj.setStyle(table_style)

                # إضافة الجدول إلى العناصر
                elements.append(table_obj)

                # بناء المستند
                doc.build(elements)

                QMessageBox.information(dialog, "نجاح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

                # فتح الملف بعد التصدير
                try:
                    os.startfile(file_path)
                except Exception as open_error:
                    print(f"تعذر فتح الملف: {str(open_error)}")

            except Exception as e:
                QMessageBox.warning(dialog, "خطأ", f"فشل في تصدير التقرير: {str(e)}")
                # طباعة تفاصيل الخطأ للمساعدة في التشخيص
                import traceback
                traceback.print_exc()

        def open_file_from_table(table, row, column):
            """فتح الملف عند النقر على زر المعاينة"""
            # التحقق من أن العمود هو عمود المعاينة
            if (table == files_details_table and column == 6) or (table == exported_docs_table and column == 7):
                item = table.item(row, column)
                if item and item.text() == "فتح الملف":
                    file_path = item.data(Qt.ItemDataRole.UserRole)
                    if file_path and os.path.exists(file_path):
                        try:
                            # فتح الملف باستخدام التطبيق الافتراضي
                            os.startfile(file_path)
                        except Exception as e:
                            QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                            print(f"خطأ في فتح الملف: {str(e)}")

        # ربط الإشارات
        generate_btn.clicked.connect(generate_time_report)
        export_generate_btn.clicked.connect(generate_exported_docs_report)

        # ربط النقر على الجداول لفتح الملفات
        files_details_table.cellClicked.connect(lambda row, column: open_file_from_table(files_details_table, row, column))
        exported_docs_table.cellClicked.connect(lambda row, column: open_file_from_table(exported_docs_table, row, column))

        export_excel_btn.clicked.connect(lambda: export_to_excel(files_details_table, "تقرير_زمني.xlsx"))
        export_pdf_btn.clicked.connect(lambda: export_to_pdf(files_details_table, "تقرير زمني للمستندات المؤرشفة", "تقرير_زمني.pdf"))

        export_report_excel_btn.clicked.connect(lambda: export_to_excel(exported_docs_table, "تقرير_المستندات_المصدرة.xlsx"))
        export_report_pdf_btn.clicked.connect(lambda: export_to_pdf(exported_docs_table, "تقرير المستندات المصدرة", "تقرير_المستندات_المصدرة.pdf"))

        # عرض النافذة
        dialog.exec()

    def get_all_subcategories(self, category_id):
        """الحصول على جميع المجلدات الفرعية لتصنيف معين بشكل متكرر"""
        subcategories = []

        # الحصول على المجلدات الفرعية المباشرة
        direct_subcategories = self.db.get_categories(category_id)

        # إضافة المجلدات الفرعية المباشرة إلى القائمة
        subcategories.extend(direct_subcategories)

        # الحصول على المجلدات الفرعية لكل مجلد فرعي بشكل متكرر
        for subcat in direct_subcategories:
            subcategories.extend(self.get_all_subcategories(subcat['id']))

        return subcategories

    def on_version(self):
        """تصدير مجلد واحد (أساسي أو فرعي) مع محتوياته إلى الجهاز المحلي"""
        # إنشاء نافذة حوار لعرض وتصدير الملفات
        dialog = QDialog(self)
        dialog.setWindowTitle("تصدير الإصدار")
        dialog.setFixedWidth(950)  # زيادة عرض النافذة
        dialog.setFixedHeight(550)  # تقليل ارتفاع النافذة
        dialog.setWindowIcon(QIcon("resources/export.png"))

        # إضافة كائن قاعدة البيانات إلى نافذة الحوار
        dialog.db = self.db

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(dialog)
        main_layout.setContentsMargins(10, 5, 10, 10)  # تقليل الهوامش خاصة في الأعلى
        main_layout.setSpacing(5)  # تقليل المسافة بين العناصر

        # عنوان النافذة - تقليل المساحة العلوية
        title_label = QLabel("تصدير الإصدار (مجلد واحد)")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #3f51b5; margin: 0px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setMaximumHeight(25)  # تحديد ارتفاع أقصى للعنوان
        main_layout.addWidget(title_label)

        # تقسيم النافذة إلى قسمين
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter, 1)  # إعطاء وزن أكبر للمقسم في التخطيط

        # تعيين نسبة التوزيع المبدئية للمساحة (70% للملفات، 30% للتصنيفات)
        splitter.setSizes([550, 250])

        # القسم الأيمن: شجرة التصنيفات
        categories_widget = QWidget()
        categories_layout = QVBoxLayout(categories_widget)
        categories_layout.setContentsMargins(5, 0, 5, 5)  # تقليل الهوامش
        categories_layout.setSpacing(5)  # تقليل المسافة بين العناصر

        categories_label = QLabel("التصنيفات")
        categories_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 0px;")
        categories_layout.addWidget(categories_label)

        categories_tree = QTreeWidget()
        categories_tree.setHeaderHidden(True)
        categories_tree.setIconSize(QSize(24, 24))
        categories_tree.setMinimumWidth(250)  # تحديد عرض أدنى لشجرة التصنيفات
        categories_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 30px;
            }
            QTreeWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #e8eaf6;
            }
        """)
        categories_layout.addWidget(categories_tree)

        # القسم الأيسر: عرض الملفات
        files_widget = QWidget()
        files_layout = QVBoxLayout(files_widget)
        files_layout.setContentsMargins(5, 0, 5, 5)  # تقليل الهوامش
        files_layout.setSpacing(5)  # تقليل المسافة بين العناصر
        files_widget.setMinimumWidth(550)  # زيادة عرض أدنى لجدول الملفات

        # عنوان محتويات المجلد
        folder_contents_label = QLabel("محتويات المجلد")
        folder_contents_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 0px;")
        files_layout.addWidget(folder_contents_label)

        # إضافة الأقسام إلى المقسم
        splitter.addWidget(files_widget)
        splitter.addWidget(categories_widget)

        # أزرار التصدير في الأسفل
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(10, 5, 10, 10)

        export_selected_btn = QPushButton("تصدير المحدد")
        export_selected_btn.setIcon(QIcon("resources/export.png"))
        export_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #303f9f;
            }
        """)
        buttons_layout.addWidget(export_selected_btn)

        export_folder_btn = QPushButton("تصدير المجلد المحدد")
        export_folder_btn.setIcon(QIcon("resources/export.png"))
        export_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388e3c;
            }
        """)
        buttons_layout.addWidget(export_folder_btn)

        main_layout.addLayout(buttons_layout)

        # متغيرات لتخزين البيانات
        current_category_id = None
        file_info_cache = {}

        # دالة لتحميل التصنيفات
        def load_categories():
            """تحميل التصنيفات في شجرة التصنيفات"""
            categories_tree.clear()

            # إضافة العقدة الجذرية
            root_item = QTreeWidgetItem(categories_tree, ["جميع التصنيفات"])
            root_item.setIcon(0, QIcon("resources/folder.png"))
            root_item.setData(0, Qt.ItemDataRole.UserRole, None)  # لا يوجد معرف للجذر
            root_item.setExpanded(True)

            # الحصول على التصنيفات الرئيسية
            categories = dialog.db.get_categories(None)

            # إضافة التصنيفات إلى الشجرة
            for category in categories:
                add_category_to_tree(root_item, category)

        # دالة لإضافة تصنيف إلى الشجرة
        def add_category_to_tree(parent_item, category):
            """إضافة تصنيف إلى شجرة التصنيفات"""
            item = QTreeWidgetItem(parent_item, [category['name']])
            item.setIcon(0, QIcon("resources/folder.png"))
            item.setData(0, Qt.ItemDataRole.UserRole, category['id'])

            # إضافة التصنيفات الفرعية
            subcategories = dialog.db.get_categories(category['id'])
            for subcategory in subcategories:
                add_category_to_tree(item, subcategory)

        # إضافة خيارات العرض والفرز
        options_frame = QFrame()
        options_frame.setFrameShape(QFrame.Shape.StyledPanel)
        options_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
                padding: 5px;
            }
            QLabel {
                font-weight: bold;
                color: #3f51b5;
            }
        """)
        options_layout = QHBoxLayout(options_frame)
        options_layout.setContentsMargins(10, 5, 10, 5)
        options_layout.setSpacing(15)

        # خيارات العرض
        view_options_group = QGroupBox("طريقة العرض")
        view_options_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c5cae9;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #3f51b5;
            }
        """)
        view_options_layout = QHBoxLayout(view_options_group)
        view_options_layout.setContentsMargins(10, 5, 10, 5)
        view_options_layout.setSpacing(10)

        view_as_list_btn = QRadioButton("قائمة")
        view_as_list_btn.setChecked(True)
        view_options_layout.addWidget(view_as_list_btn)

        view_as_details_btn = QRadioButton("تفاصيل")
        view_options_layout.addWidget(view_as_details_btn)

        options_layout.addWidget(view_options_group)

        # خيارات الفرز
        sort_options_group = QGroupBox("فرز حسب")
        sort_options_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c5cae9;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #3f51b5;
            }
        """)
        sort_options_layout = QHBoxLayout(sort_options_group)
        sort_options_layout.setContentsMargins(10, 5, 10, 5)
        sort_options_layout.setSpacing(10)

        # تنسيق مشترك لأزرار الفرز
        sort_button_style = """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.8);
                border: 1px solid #c5cae9;
                border-radius: 3px;
                padding: 5px 8px;
                margin: 0px 2px;
                color: #3f51b5;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #3f51b5;
                color: white;
                border: 1px solid #303f9f;
            }
            QPushButton:hover {
                background-color: #e8eaf6;
            }
        """

        sort_by_name_btn = QPushButton("الاسم")
        sort_by_name_btn.setCheckable(True)
        sort_by_name_btn.setChecked(True)
        sort_by_name_btn.setStyleSheet(sort_button_style)
        sort_by_name_btn.setMinimumWidth(60)
        sort_options_layout.addWidget(sort_by_name_btn)

        sort_by_date_btn = QPushButton("التاريخ")
        sort_by_date_btn.setCheckable(True)
        sort_by_date_btn.setStyleSheet(sort_button_style)
        sort_by_date_btn.setMinimumWidth(60)
        sort_options_layout.addWidget(sort_by_date_btn)

        sort_by_type_btn = QPushButton("النوع")
        sort_by_type_btn.setCheckable(True)
        sort_by_type_btn.setStyleSheet(sort_button_style)
        sort_by_type_btn.setMinimumWidth(60)
        sort_options_layout.addWidget(sort_by_type_btn)

        options_layout.addWidget(sort_options_group)
        files_layout.addWidget(options_frame)

        # إنشاء جدول لعرض التفاصيل
        files_table = QTableWidget()
        files_table.setColumnCount(5)
        header_labels = ["اسم الملف", "النوع", "الحجم", "تاريخ الإضافة", "تاريخ التصدير"]
        files_table.setHorizontalHeaderLabels(header_labels)
        files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        files_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        files_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        files_table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)
        files_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 0px;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QHeaderView::section {
                background-color: #e8eaf6;
                color: #3f51b5;
                padding: 5px;
                border: 1px solid #c5cae9;
                font-weight: bold;
            }
        """)

        # إنشاء قائمة الملفات
        files_list = QListWidget()
        files_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        files_list.setIconSize(QSize(32, 32))
        files_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 40px;
            }
            QListWidget::item:selected {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #e8eaf6;
                border-radius: 4px;
            }
        """)

        # إنشاء StackedWidget لتبديل طريقة العرض
        files_stack = QStackedWidget()
        files_stack.addWidget(files_list)
        files_stack.addWidget(files_table)

        files_layout.addWidget(files_stack)

        # دالة لعرض ملفات المجلد
        def show_folder_files():
            """عرض ملفات المجلد المحدد"""
            nonlocal current_category_id
            selected_items = categories_tree.selectedItems()
            if not selected_items:
                return

            selected_item = selected_items[0]
            current_category_id = selected_item.data(0, Qt.ItemDataRole.UserRole)

            # مسح القائمة الحالية
            files_list.clear()
            files_table.setRowCount(0)

            # إذا كان المجلد المحدد هو الجذر، عرض جميع الملفات
            if current_category_id is None:
                # استخدام get_documents بدون معرف تصنيف للحصول على جميع المستندات
                documents = dialog.db.get_documents()
            else:
                # الحصول على ملفات المجلد المحدد
                documents = dialog.db.get_documents(current_category_id)

            # فرز المستندات حسب الخيار المحدد
            if sort_by_name_btn.isChecked():
                documents = sorted(documents, key=lambda x: x['title'])
            elif sort_by_date_btn.isChecked():
                documents = sorted(documents, key=lambda x: x.get('import_date', ''), reverse=True)
            elif sort_by_type_btn.isChecked():
                documents = sorted(documents, key=lambda x: x.get('file_type', ''))

            # إضافة الملفات إلى القائمة والجدول
            files_table.setRowCount(len(documents))

            for i, doc in enumerate(documents):
                # إضافة إلى القائمة
                item = QListWidgetItem(doc['title'])
                item.setIcon(QIcon(get_file_icon(doc.get('file_type', ''))))
                item.setData(Qt.ItemDataRole.UserRole, doc['id'])
                files_list.addItem(item)

                # إضافة إلى الجدول
                # اسم الملف
                title_item = QTableWidgetItem(doc['title'])
                title_item.setData(Qt.ItemDataRole.UserRole, doc['id'])
                files_table.setItem(i, 0, title_item)

                # النوع
                file_type = doc.get('file_type', '') or "غير معروف"
                type_item = QTableWidgetItem(file_type)
                files_table.setItem(i, 1, type_item)

                # الحجم
                file_size = doc.get('file_size', 0) or 0
                size_str = format_file_size(file_size)
                size_item = QTableWidgetItem(size_str)
                size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                files_table.setItem(i, 2, size_item)

                # تاريخ الإضافة
                import_date = doc.get('import_date', '') or ""
                import_date_item = QTableWidgetItem(import_date)
                import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                files_table.setItem(i, 3, import_date_item)

                # تاريخ التصدير
                export_date = doc.get('export_date', '') or ""
                export_date_item = QTableWidgetItem(export_date)
                export_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                files_table.setItem(i, 4, export_date_item)

                # تخزين معلومات الملف في الذاكرة المؤقتة
                file_info_cache[doc['id']] = {
                    'title': doc['title'],
                    'file_path': doc.get('file_path', ''),
                    'file_type': doc.get('file_type', ''),
                    'file_size': doc.get('file_size', 0),
                    'import_date': doc.get('import_date', ''),
                    'export_date': doc.get('export_date', '')
                }

            # تعديل ارتفاع الصفوف لاحتواء النص
            files_table.resizeRowsToContents()

        # دالة لتنسيق حجم الملف
        def format_file_size(size):
            """تنسيق حجم الملف بشكل مقروء"""
            if size < 1024:
                return f"{size} بايت"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} كيلوبايت"
            elif size < 1024 * 1024 * 1024:
                return f"{size / (1024 * 1024):.1f} ميجابايت"
            else:
                return f"{size / (1024 * 1024 * 1024):.1f} جيجابايت"

        # دالة للحصول على أيقونة الملف
        def get_file_icon(file_type):
            """الحصول على أيقونة الملف بناءً على نوعه"""
            file_type = file_type.lower() if file_type else ""
            if 'pdf' in file_type:
                return "resources/pdf.png"
            elif 'word' in file_type or 'doc' in file_type:
                return "resources/word.png"
            elif 'excel' in file_type or 'xls' in file_type:
                return "resources/excel.png"
            elif 'image' in file_type or 'jpg' in file_type or 'png' in file_type:
                return "resources/image.png"
            else:
                return "resources/file.png"

        # دالة لتصدير الملفات المحددة
        def export_selected_files():
            """تصدير الملفات المحددة"""
            selected_items = files_list.selectedItems()
            if not selected_items:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء تحديد ملف واحد على الأقل للتصدير")
                return

            # فتح مربع حوار لاختيار مجلد الوجهة
            export_dir = QFileDialog.getExistingDirectory(dialog, "اختر مجلد الوجهة للتصدير")
            if not export_dir:
                return

            # تصدير الملفات المحددة
            exported_count = 0
            for item in selected_items:
                doc_id = item.data(Qt.ItemDataRole.UserRole)
                if doc_id in file_info_cache:
                    file_info = file_info_cache[doc_id]
                    file_path = file_info.get('file_path', '')
                    if file_path and os.path.exists(file_path):
                        # إنشاء اسم الملف الوجهة
                        dest_file = os.path.join(export_dir, os.path.basename(file_path))
                        try:
                            # نسخ الملف
                            import shutil
                            shutil.copy2(file_path, dest_file)
                            exported_count += 1

                            # تحديث تاريخ التصدير في قاعدة البيانات
                            dialog.db.update_document_export_date(doc_id)
                        except Exception as e:
                            print(f"خطأ في تصدير الملف {file_path}: {str(e)}")

            # عرض رسالة نجاح
            QMessageBox.information(dialog, "تم التصدير", f"تم تصدير {exported_count} ملف بنجاح إلى المجلد المحدد")

        # دالة لتصدير المجلد المحدد
        def export_entire_folder():
            """تصدير المجلد المحدد مع إنشاء مجلد بنفس اسم التصنيف"""
            # التحقق من تحديد مجلد
            selected_items = categories_tree.selectedItems()
            if not selected_items:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء تحديد مجلد للتصدير")
                return

            # التحقق من أن المجلد المحدد ليس "جميع التصنيفات"
            selected_item = selected_items[0]
            if selected_item.text(0) == "جميع التصنيفات":
                QMessageBox.warning(dialog, "تنبيه", "لا يمكن تصدير 'جميع التصنيفات'. الرجاء تحديد مجلد محدد للتصدير")
                return

            # التحقق من وجود معرف للتصنيف
            if current_category_id is None:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء تحديد مجلد للتصدير")
                return

            # فتح مربع حوار لاختيار مجلد الوجهة
            export_dir = QFileDialog.getExistingDirectory(dialog, "اختر مجلد الوجهة للتصدير")
            if not export_dir:
                return

            # الحصول على معلومات المجلد المحدد
            selected_items = categories_tree.selectedItems()
            if not selected_items:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء تحديد مجلد للتصدير")
                return

            selected_item = selected_items[0]
            folder_name = selected_item.text(0)  # اسم المجلد المحدد

            # إنشاء مجلد بنفس اسم التصنيف في مجلد الوجهة
            folder_path = os.path.join(export_dir, folder_name)
            try:
                # التحقق من وجود المجلد وإنشائه إذا لم يكن موجودًا
                if not os.path.exists(folder_path):
                    os.makedirs(folder_path)
            except Exception as e:
                QMessageBox.warning(dialog, "خطأ", f"فشل في إنشاء المجلد: {str(e)}")
                return

            # تصدير ملفات المجلد المحدد فقط
            exported_count = 0
            documents = dialog.db.get_documents(current_category_id)

            for doc in documents:
                file_path = doc.get('file_path', '')
                if file_path and os.path.exists(file_path):
                    # إنشاء اسم الملف الوجهة داخل المجلد الجديد
                    dest_file = os.path.join(folder_path, os.path.basename(file_path))
                    try:
                        # نسخ الملف
                        import shutil
                        shutil.copy2(file_path, dest_file)
                        exported_count += 1

                        # تحديث تاريخ التصدير في قاعدة البيانات
                        dialog.db.update_document_export_date(doc['id'])
                    except Exception as e:
                        print(f"خطأ في تصدير الملف {file_path}: {str(e)}")

            # عرض رسالة نجاح
            QMessageBox.information(dialog, "تم التصدير", f"تم تصدير {exported_count} ملف من المجلد '{folder_name}' بنجاح")

        # دالة لتبديل طريقة العرض
        def update_view_mode():
            if view_as_list_btn.isChecked():
                files_stack.setCurrentIndex(0)  # عرض القائمة
            else:
                files_stack.setCurrentIndex(1)  # عرض التفاصيل

        # ربط أزرار طريقة العرض
        view_as_list_btn.toggled.connect(update_view_mode)
        view_as_details_btn.toggled.connect(update_view_mode)

        # ربط أزرار الفرز
        sort_by_name_btn.clicked.connect(lambda: sort_files("name"))
        sort_by_date_btn.clicked.connect(lambda: sort_files("date"))
        sort_by_type_btn.clicked.connect(lambda: sort_files("type"))

        # دالة لفرز الملفات
        def sort_files(sort_type):
            # إلغاء تحديد جميع أزرار الفرز
            sort_by_name_btn.setChecked(False)
            sort_by_date_btn.setChecked(False)
            sort_by_type_btn.setChecked(False)

            # تحديد الزر المناسب
            if sort_type == "name":
                sort_by_name_btn.setChecked(True)
            elif sort_type == "date":
                sort_by_date_btn.setChecked(True)
            elif sort_type == "type":
                sort_by_type_btn.setChecked(True)

            # إعادة عرض الملفات مع الفرز الجديد
            show_folder_files()

        # ربط النقر على التصنيفات بعرض الملفات
        categories_tree.itemClicked.connect(lambda item, column: show_folder_files())

        # دالة لمعالجة النقر المزدوج على الملف
        def on_file_double_clicked(item):
            if item:
                doc_id = item.data(Qt.ItemDataRole.UserRole)
                if doc_id in file_info_cache:
                    file_info = file_info_cache[doc_id]
                    if file_info.get('file_path') and os.path.exists(file_info['file_path']):
                        try:
                            # فتح الملف باستخدام التطبيق الافتراضي
                            os.startfile(file_info['file_path'])
                        except Exception as e:
                            QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                    else:
                        QMessageBox.warning(dialog, "خطأ", "الملف غير موجود أو تم حذفه")

        # ربط النقر المزدوج على الجدول
        files_table.cellDoubleClicked.connect(lambda row, column: on_file_double_clicked(files_table.item(row, 0)))

        # ربط النقر المزدوج على القائمة
        files_list.itemDoubleClicked.connect(on_file_double_clicked)

        # ربط أزرار التصدير
        export_selected_btn.clicked.connect(export_selected_files)
        export_folder_btn.clicked.connect(export_entire_folder)

        # تحميل التصنيفات
        load_categories()

        # عرض النافذة
        dialog.show()

    def on_export(self):
        """تصدير ملفات التصنيف إلى الجهاز المحلي (المجلدات الرئيسية فقط)"""
        # إنشاء نافذة حوار لعرض وتصدير الملفات
        dialog = QDialog(self)
        dialog.setWindowTitle("تصدير الملفات")
        dialog.setFixedWidth(950)  # زيادة عرض النافذة
        dialog.setFixedHeight(550)  # تقليل ارتفاع النافذة
        dialog.setWindowIcon(QIcon("resources/export.png"))

        # إضافة كائن قاعدة البيانات إلى نافذة الحوار
        dialog.db = self.db

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(dialog)
        main_layout.setContentsMargins(10, 5, 10, 10)  # تقليل الهوامش خاصة في الأعلى
        main_layout.setSpacing(5)  # تقليل المسافة بين العناصر

        # عنوان النافذة - تقليل المساحة العلوية
        title_label = QLabel("تصدير الملفات")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #3f51b5; margin: 0px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setMaximumHeight(25)  # تحديد ارتفاع أقصى للعنوان
        main_layout.addWidget(title_label)

        # تقسيم النافذة إلى قسمين
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter, 1)  # إعطاء وزن أكبر للمقسم في التخطيط

        # تعيين نسبة التوزيع المبدئية للمساحة (70% للملفات، 30% للتصنيفات)
        splitter.setSizes([550, 250])

        # القسم الأيمن: شجرة التصنيفات
        categories_widget = QWidget()
        categories_layout = QVBoxLayout(categories_widget)
        categories_layout.setContentsMargins(5, 0, 5, 5)  # تقليل الهوامش
        categories_layout.setSpacing(5)  # تقليل المسافة بين العناصر

        categories_label = QLabel("التصنيفات")
        categories_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 0px;")
        categories_layout.addWidget(categories_label)

        categories_tree = QTreeWidget()
        categories_tree.setHeaderHidden(True)
        categories_tree.setIconSize(QSize(24, 24))
        categories_tree.setMinimumWidth(250)  # تحديد عرض أدنى لشجرة التصنيفات
        categories_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 30px;
            }
            QTreeWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #e8eaf6;
            }
        """)
        categories_layout.addWidget(categories_tree)

        # القسم الأيسر: عرض الملفات
        files_widget = QWidget()
        files_layout = QVBoxLayout(files_widget)
        files_layout.setContentsMargins(5, 0, 5, 5)  # تقليل الهوامش
        files_layout.setSpacing(5)  # تقليل المسافة بين العناصر
        files_widget.setMinimumWidth(550)  # زيادة عرض أدنى لجدول الملفات

        # عنوان محتويات المجلد
        folder_contents_label = QLabel("محتويات المجلد")
        folder_contents_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 0px;")
        files_layout.addWidget(folder_contents_label)

        # خيارات العرض والبحث
        options_layout = QHBoxLayout()

        # خيارات العرض
        view_options_group = QWidget()
        view_options_layout = QHBoxLayout(view_options_group)
        view_options_layout.setContentsMargins(0, 0, 0, 0)

        view_mode_label = QLabel("طريقة العرض:")
        view_mode_label.setStyleSheet("font-weight: bold;")
        view_options_layout.addWidget(view_mode_label)

        view_as_list_btn = QRadioButton("قائمة")
        view_as_list_btn.setChecked(True)
        view_options_layout.addWidget(view_as_list_btn)

        view_as_details_btn = QRadioButton("تفاصيل")
        view_options_layout.addWidget(view_as_details_btn)

        options_layout.addWidget(view_options_group)
        options_layout.addStretch()

        # خيارات الفرز
        sort_options_group = QWidget()
        sort_options_layout = QHBoxLayout(sort_options_group)
        sort_options_layout.setContentsMargins(0, 0, 0, 0)

        sort_label = QLabel("فرز حسب:")
        sort_label.setStyleSheet("font-weight: bold; background-color: rgba(255, 255, 255, 0.8); padding: 2px 5px; border-radius: 3px;")
        sort_options_layout.addWidget(sort_label)

        # تنسيق مشترك لأزرار الفرز
        sort_button_style = """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.8);
                border: 1px solid #c5cae9;
                border-radius: 3px;
                padding: 2px 5px;
                margin: 0px 2px;
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                color: black; /* تغيير لون النص إلى أسود */
            }
            QPushButton:checked {
                background-color: #3f51b5;
                color: white;
                border: 1px solid #303f9f;
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            }
            QPushButton:hover {
                background-color: #e8eaf6;
            }
        """

        # تنسيق مشترك لأزرار الفرز مع تحسين الظل
        sort_by_name_btn = QPushButton("الاسم")
        sort_by_name_btn.setCheckable(True)
        sort_by_name_btn.setChecked(True)
        sort_by_name_btn.setStyleSheet(sort_button_style)
        sort_by_name_btn.setMinimumWidth(60)
        sort_by_name_btn.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        sort_options_layout.addWidget(sort_by_name_btn)

        sort_by_date_btn = QPushButton("التاريخ")
        sort_by_date_btn.setCheckable(True)
        sort_by_date_btn.setStyleSheet(sort_button_style)
        sort_by_date_btn.setMinimumWidth(60)
        sort_by_date_btn.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        sort_options_layout.addWidget(sort_by_date_btn)

        sort_by_type_btn = QPushButton("النوع")
        sort_by_type_btn.setCheckable(True)
        sort_by_type_btn.setStyleSheet(sort_button_style)
        sort_by_type_btn.setMinimumWidth(60)
        sort_by_type_btn.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        sort_options_layout.addWidget(sort_by_type_btn)

        sort_by_size_btn = QPushButton("الحجم")
        sort_by_size_btn.setCheckable(True)
        sort_by_size_btn.setStyleSheet(sort_button_style)
        sort_by_size_btn.setMinimumWidth(60)
        sort_by_size_btn.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        sort_options_layout.addWidget(sort_by_size_btn)

        options_layout.addWidget(sort_options_group)

        files_layout.addLayout(options_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: #c5cae9;")
        files_layout.addWidget(separator)

        # قائمة الملفات
        files_list = QListWidget()
        files_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        files_list.setIconSize(QSize(32, 32))
        files_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 40px;
            }
            QListWidget::item:selected {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #e8eaf6;
                border-radius: 4px;
            }
        """)

        # تمكين السحب والإفلات
        files_list.setDragEnabled(True)

        # تعريف دالة السحب والإفلات
        class FileListWidget(QListWidget):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setDragEnabled(True)
                self.file_info_cache = {}

            def set_file_info_cache(self, cache):
                self.file_info_cache = cache

            def startDrag(self, supportedActions):
                selected_items = self.selectedItems()
                if not selected_items:
                    return

                # إنشاء بيانات السحب
                mime_data = QMimeData()
                urls = []

                # إضافة الملفات المحددة إلى قائمة URLs
                for item in selected_items:
                    doc_id = item.data(Qt.ItemDataRole.UserRole)
                    if doc_id in self.file_info_cache:
                        file_path = self.file_info_cache[doc_id].get('file_path', '')
                        if file_path and os.path.exists(file_path):
                            urls.append(QUrl.fromLocalFile(file_path))

                if urls:
                    mime_data.setUrls(urls)
                    drag = QDrag(self)
                    drag.setMimeData(mime_data)

                    # تعيين صورة للسحب
                    pixmap = QPixmap("resources/file.png").scaled(32, 32)
                    drag.setPixmap(pixmap)

                    # بدء عملية السحب
                    result = drag.exec(Qt.DropAction.CopyAction)

                    # تسجيل الملفات المصدرة في قاعدة البيانات
                    if result == Qt.DropAction.CopyAction:
                        for item in selected_items:
                            doc_id = item.data(Qt.ItemDataRole.UserRole)
                            if doc_id in self.file_info_cache:
                                # تحديث تاريخ التصدير في قاعدة البيانات
                                dialog.db.update_document_export_date(doc_id)

        # استبدال قائمة الملفات العادية بالقائمة المخصصة
        files_list = FileListWidget()
        files_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        files_list.setIconSize(QSize(32, 32))

        # إضافة معالج النقر المزدوج لفتح الملفات
        def on_file_double_clicked(item):
            doc_id = item.data(Qt.ItemDataRole.UserRole)
            if doc_id in file_info_cache:
                file_path = file_info_cache[doc_id].get('file_path', '')
                if file_path and os.path.exists(file_path):
                    try:
                        # فتح الملف باستخدام التطبيق الافتراضي
                        os.startfile(file_path)

                        # تحديث تاريخ التصدير في قاعدة البيانات
                        dialog.db.update_document_export_date(doc_id)

                        # تحديث العرض لإظهار تاريخ التصدير المحدث
                        show_folder_files()
                    except Exception as e:
                        QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                        print(f"خطأ في فتح الملف: {str(e)}")

        # ربط معالج النقر المزدوج
        files_list.itemDoubleClicked.connect(on_file_double_clicked)
        files_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 40px;
            }
            QListWidget::item:selected {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #e8eaf6;
                border-radius: 4px;
            }
        """)

        # جدول تفاصيل الملفات
        files_table = QTableWidget()
        files_table.setColumnCount(5)

        # إنشاء عناوين الأعمدة مع إضافة ظل
        header_labels = ["اسم الملف", "النوع", "الحجم", "تاريخ الإضافة", "تاريخ التصدير"]
        files_table.setHorizontalHeaderLabels(header_labels)

        # تطبيق نمط الظل على كل عنوان عمود
        header = files_table.horizontalHeader()
        for i in range(len(header_labels)):
            header_item = QTableWidgetItem(header_labels[i])
            header_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            header_item.setBackground(QColor("#3f51b5"))
            header_item.setForeground(QColor("black"))  # تغيير لون النص إلى أسود بدلاً من أبيض
            header_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            files_table.setHorizontalHeaderItem(i, header_item)
        files_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        files_table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)
        files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        files_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        files_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QHeaderView::section {
                background-color: #3f51b5;
                color: black;
                padding: 8px;
                font-weight: bold;
                border: none;
                border-radius: 3px;
                margin: 1px;
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }
        """)

        # تمكين السحب والإفلات في جدول التفاصيل
        class FileTableWidget(QTableWidget):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setDragEnabled(True)
                self.file_info_cache = {}

            def set_file_info_cache(self, cache):
                self.file_info_cache = cache

            def startDrag(self, supportedActions):
                selected_rows = set()
                for item in self.selectedItems():
                    selected_rows.add(item.row())

                if not selected_rows:
                    return

                # إنشاء بيانات السحب
                mime_data = QMimeData()
                urls = []

                # إضافة الملفات المحددة إلى قائمة URLs
                for row in selected_rows:
                    item = self.item(row, 0)
                    if item:
                        doc_id = item.data(Qt.ItemDataRole.UserRole)
                        if doc_id in self.file_info_cache:
                            file_path = self.file_info_cache[doc_id].get('file_path', '')
                            if file_path and os.path.exists(file_path):
                                urls.append(QUrl.fromLocalFile(file_path))

                if urls:
                    mime_data.setUrls(urls)
                    drag = QDrag(self)
                    drag.setMimeData(mime_data)

                    # تعيين صورة للسحب
                    pixmap = QPixmap("resources/file.png").scaled(32, 32)
                    drag.setPixmap(pixmap)

                    # بدء عملية السحب
                    result = drag.exec(Qt.DropAction.CopyAction)

                    # تسجيل الملفات المصدرة في قاعدة البيانات
                    if result == Qt.DropAction.CopyAction:
                        for row in selected_rows:
                            item = self.item(row, 0)
                            if item:
                                doc_id = item.data(Qt.ItemDataRole.UserRole)
                                if doc_id in self.file_info_cache:
                                    # تحديث تاريخ التصدير في قاعدة البيانات
                                    dialog.db.update_document_export_date(doc_id)

        # استبدال جدول الملفات العادي بالجدول المخصص
        files_table = FileTableWidget()
        files_table.setColumnCount(5)

        # إنشاء عناوين الأعمدة مع إضافة ظل
        header_labels = ["اسم الملف", "النوع", "الحجم", "تاريخ الإضافة", "تاريخ التصدير"]
        files_table.setHorizontalHeaderLabels(header_labels)

        # تطبيق نمط الظل على كل عنوان عمود
        header = files_table.horizontalHeader()
        for i in range(len(header_labels)):
            header_item = QTableWidgetItem(header_labels[i])
            header_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            header_item.setBackground(QColor("#3f51b5"))
            header_item.setForeground(QColor("black"))  # تغيير لون النص إلى أسود بدلاً من أبيض
            header_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            files_table.setHorizontalHeaderItem(i, header_item)
        files_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        files_table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)
        files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        files_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        files_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

        # تطبيق نفس التنسيق على الجدول
        files_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QHeaderView::section {
                background-color: #3f51b5;
                color: black;
                padding: 8px;
                font-weight: bold;
                border: none;
                border-radius: 3px;
                margin: 1px;
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }
        """)

        # إضافة معالج النقر المزدوج لفتح الملفات
        def on_table_double_clicked(row, column):
            item = files_table.item(row, 0)
            if item:
                doc_id = item.data(Qt.ItemDataRole.UserRole)
                if doc_id in file_info_cache:
                    file_path = file_info_cache[doc_id].get('file_path', '')
                    if file_path and os.path.exists(file_path):
                        try:
                            # فتح الملف باستخدام التطبيق الافتراضي
                            os.startfile(file_path)

                            # تحديث تاريخ التصدير في قاعدة البيانات
                            dialog.db.update_document_export_date(doc_id)

                            # تحديث العرض لإظهار تاريخ التصدير المحدث
                            show_folder_files()
                        except Exception as e:
                            QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                            print(f"خطأ في فتح الملف: {str(e)}")

        # ربط معالج النقر المزدوج
        files_table.cellDoubleClicked.connect(on_table_double_clicked)

        # إضافة القائمة والجدول إلى التخطيط مع زيادة المساحة
        files_layout.addWidget(files_list, 1)  # إعطاء وزن أكبر للقائمة
        files_layout.addWidget(files_table, 1)  # إعطاء وزن أكبر للجدول

        # إخفاء الجدول في البداية
        files_table.hide()

        # إضافة الأقسام إلى المقسم
        splitter.addWidget(categories_widget)
        splitter.addWidget(files_widget)

        # ضبط أحجام الأقسام - توسيع جدول محتويات المجلد
        splitter.setSizes([280, 670])

        # أزرار التحكم - تقليل الارتفاع
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 0, 0, 0)  # تقليل الهوامش
        buttons_layout.setSpacing(5)  # تقليل المسافة بين الأزرار

        # تنسيق مشترك للأزرار
        button_style = """
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;  /* تقليل الحشو */
                font-weight: bold;
                min-height: 25px;  /* تقليل الارتفاع */
            }
            QPushButton:hover {
                background-color: #5c6bc0;
            }
            QPushButton:pressed {
                background-color: #303f9f;
            }
            QPushButton:disabled {
                background-color: #d3d3d3;
                color: #a9a9a9;
            }
        """

        export_selected_btn = QPushButton(QIcon("resources/export.png"), "تصدير المحدد")
        export_selected_btn.setStyleSheet(button_style)
        buttons_layout.addWidget(export_selected_btn)

        export_folder_btn = QPushButton(QIcon("resources/folder.png"), "تصدير المجلد كاملاً")
        export_folder_btn.setStyleSheet(button_style)
        buttons_layout.addWidget(export_folder_btn)

        buttons_layout.addStretch()

        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet(button_style)

        # استخدام reject() بدلاً من close() أو accept()
        close_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(close_btn)

        main_layout.addLayout(buttons_layout)

        # تعريف المتغيرات التي ستستخدم في الدوال الداخلية
        current_category_id = None
        file_info_cache = {}

        # تحميل التصنيفات
        def load_categories():
            # مسح الشجرة
            categories_tree.clear()

            # إضافة عنصر "جميع المستندات"
            all_docs_item = QTreeWidgetItem(categories_tree)
            all_docs_item.setText(0, "جميع المستندات")
            all_docs_item.setIcon(0, QIcon("resources/categories.png"))
            all_docs_item.setData(0, Qt.ItemDataRole.UserRole, "all")

            # الحصول على التصنيفات من قاعدة البيانات
            categories = self.db.get_categories()

            # إضافة التصنيفات إلى الشجرة
            for category in categories:
                item = QTreeWidgetItem(categories_tree)
                item.setText(0, category['name'])
                item.setIcon(0, QIcon("resources/categories.png"))
                item.setData(0, Qt.ItemDataRole.UserRole, category['id'])

        # تحديث طريقة العرض
        def update_view_mode():
            if view_as_list_btn.isChecked():
                files_list.show()
                files_table.hide()
            else:
                files_list.hide()
                files_table.show()

        # عرض محتويات المجلد
        def show_folder_files():
            nonlocal current_category_id, file_info_cache

            selected_items = categories_tree.selectedItems()
            if not selected_items:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء اختيار مجلد أولاً")
                return

            selected_item = selected_items[0]
            category_id = selected_item.data(0, Qt.ItemDataRole.UserRole)
            current_category_id = category_id

            # تحديث عنوان المحتويات
            folder_contents_label.setText(f"محتويات المجلد: {selected_item.text(0)} ")

            # تحديث قائمة الملفات
            files_list.clear()
            files_table.clearContents()
            files_table.setRowCount(0)

            # الحصول على الملفات في هذا التصنيف
            if category_id == "all":
                documents = self.db.get_documents()
            else:
                documents = self.db.get_documents(category_id)

            # تخزين معلومات الملفات في الذاكرة المؤقتة
            file_info_cache.clear()

            # تحديث الذاكرة المؤقتة في قائمة الملفات المخصصة وجدول التفاصيل
            files_list.set_file_info_cache(file_info_cache)
            files_table.set_file_info_cache(file_info_cache)

            # تطبيق الفرز
            def apply_sorting():
                nonlocal documents

                # تحديد معيار الفرز
                if sort_by_name_btn.isChecked():
                    documents = sorted(documents, key=lambda x: x.get('title', '').lower())
                elif sort_by_date_btn.isChecked():
                    documents = sorted(documents, key=lambda x: x.get('import_date', ''), reverse=True)
                elif sort_by_type_btn.isChecked():
                    documents = sorted(documents, key=lambda x: x.get('file_type', '').lower())
                elif sort_by_size_btn.isChecked():
                    documents = sorted(documents, key=lambda x: x.get('file_size', 0), reverse=True)

                # تحديث العرض
                update_files_display()

            # تحديث عرض الملفات
            def update_files_display():
                # مسح العناصر الحالية
                files_list.clear()
                files_table.clearContents()
                files_table.setRowCount(len(documents))

                # تحديث عنوان المحتويات بعدد الملفات
                folder_contents_label.setText(f"محتويات المجلد: {selected_item.text(0)} ({len(documents)} ملف)")

                for i, doc in enumerate(documents):
                    # تخزين معلومات الملف في الذاكرة المؤقتة
                    file_info_cache[doc['id']] = doc

                    # إضافة إلى القائمة
                    item = QListWidgetItem(doc['title'])
                    item.setData(Qt.ItemDataRole.UserRole, doc['id'])

                    # تعيين أيقونة حسب نوع الملف
                    file_type = doc.get('file_type', '')
                    if file_type.lower() in ['pdf']:
                        icon = QIcon("resources/pdf.png")
                    elif file_type.lower() in ['doc', 'docx', 'rtf']:
                        icon = QIcon("resources/word.png")
                    elif file_type.lower() in ['xls', 'xlsx', 'csv']:
                        icon = QIcon("resources/excel.png")
                    elif file_type.lower() in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tif', 'tiff']:
                        icon = QIcon("resources/image.png")
                    else:
                        icon = QIcon("resources/file.png")

                    item.setIcon(icon)

                    # إضافة معلومات إضافية كتلميح
                    file_size = doc.get('file_size', 0)
                    size_text = format_size(file_size)
                    import_date = doc.get('import_date', '')
                    export_date = doc.get('export_date', 'لم يتم التصدير')

                    # تعديل التلميح ليعرض فقط اسم الملف والوصف وتاريخ الاستيراد وتاريخ التصدير
                    description = doc.get('description', '')
                    item.setToolTip(f"الاسم: {doc['title']}\nالوصف: {description}\nتاريخ الاستيراد: {import_date}\nتاريخ التصدير: {export_date}")

                    files_list.addItem(item)

                    # إضافة إلى الجدول
                    title_item = QTableWidgetItem(doc['title'])
                    title_item.setData(Qt.ItemDataRole.UserRole, doc['id'])
                    files_table.setItem(i, 0, title_item)

                    type_item = QTableWidgetItem(file_type)
                    type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_table.setItem(i, 1, type_item)

                    # حجم الملف
                    size_item = QTableWidgetItem(size_text)
                    size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_table.setItem(i, 2, size_item)

                    # تاريخ الإضافة
                    import_date_item = QTableWidgetItem(import_date)
                    import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_table.setItem(i, 3, import_date_item)

                    # تاريخ التصدير
                    export_date_item = QTableWidgetItem(export_date)
                    export_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_table.setItem(i, 4, export_date_item)

                # تعديل ارتفاع الصفوف لاحتواء النص
                files_table.resizeRowsToContents()

            # تطبيق الفرز والعرض
            apply_sorting()

            # تحديث حالة العرض
            update_view_mode()

            # ربط أزرار الفرز بوظيفة الفرز
            sort_by_name_btn.clicked.connect(apply_sorting)
            sort_by_date_btn.clicked.connect(apply_sorting)
            sort_by_type_btn.clicked.connect(apply_sorting)
            sort_by_size_btn.clicked.connect(apply_sorting)

        # دالة مساعدة لتنسيق حجم الملف
        def format_size(size):
            if size is None:
                return "غير معروف"
            if size < 1024:
                return f"{size} بايت"
            elif size < 1024 * 1024:
                return f"{size / 1024:.2f} كيلوبايت"
            else:
                return f"{size / (1024 * 1024):.2f} ميجابايت"

        # تصدير الملفات المحددة
        def export_selected_files():
            # التحقق من وجود ملفات محددة
            selected_items = []
            if view_as_list_btn.isChecked():
                selected_items = files_list.selectedItems()
            else:
                selected_rows = set()
                for item in files_table.selectedItems():
                    selected_rows.add(item.row())

                for row in selected_rows:
                    item = files_table.item(row, 0)
                    if item:
                        selected_items.append(item)

            if not selected_items:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء اختيار ملف واحد على الأقل للتصدير")
                return

            # اختيار مجلد الوجهة مع إظهار المجلدات الفرعية
            export_dir = QFileDialog.getExistingDirectory(
                dialog,
                "اختر مجلد التصدير"
            )
            if not export_dir:
                return

            # تصدير الملفات المحددة
            exported_count = 0
            failed_count = 0
            exported_files = []

            # إنشاء نافذة تقدم العملية
            progress_dialog = QDialog(dialog)
            progress_dialog.setWindowTitle("جاري التصدير...")
            progress_dialog.setFixedSize(400, 150)
            progress_layout = QVBoxLayout(progress_dialog)

            progress_label = QLabel("جاري تصدير الملفات...")
            progress_layout.addWidget(progress_label)

            file_label = QLabel("")
            progress_layout.addWidget(file_label)

            progress_dialog.show()
            QApplication.processEvents()

            try:
                for i, item in enumerate(selected_items):
                    doc_id = item.data(Qt.ItemDataRole.UserRole)
                    if doc_id in file_info_cache:
                        file_info = file_info_cache[doc_id]
                        source_path = file_info.get('file_path', '')
                        file_title = file_info.get('title', '')

                        # تحديث حالة التقدم
                        file_label.setText(f"جاري تصدير: {file_title}")
                        progress_label.setText(f"تقدم العملية: {i+1} من {len(selected_items)}")
                        QApplication.processEvents()

                        if source_path and os.path.exists(source_path):
                            # إنشاء اسم الملف الوجهة
                            file_name = os.path.basename(source_path)
                            dest_path = os.path.join(export_dir, file_name)

                            # التحقق من وجود ملف بنفس الاسم
                            if os.path.exists(dest_path):
                                # إضافة رقم للملف لتجنب التكرار
                                base_name, ext = os.path.splitext(file_name)
                                counter = 1
                                while os.path.exists(os.path.join(export_dir, f"{base_name}_{counter}{ext}")):
                                    counter += 1
                                dest_path = os.path.join(export_dir, f"{base_name}_{counter}{ext}")

                            # نسخ الملف
                            try:
                                import shutil
                                shutil.copy2(source_path, dest_path)
                                exported_count += 1
                                exported_files.append(dest_path)

                                # تحديث تاريخ التصدير في قاعدة البيانات
                                dialog.db.update_document_export_date(doc_id)
                            except Exception as e:
                                failed_count += 1
                                print(f"خطأ في تصدير الملف {file_name}: {str(e)}")
            finally:
                # إغلاق نافذة التقدم
                progress_dialog.close()

            # عرض رسالة نجاح
            if exported_count > 0:
                success_msg = f"تم تصدير {exported_count} ملف بنجاح إلى:\n{export_dir}"
                if failed_count > 0:
                    success_msg += f"\n\nفشل تصدير {failed_count} ملف."

                result = QMessageBox.information(
                    dialog,
                    "نجاح",
                    success_msg,
                    QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Open
                )

                # فتح المجلد بعد التصدير إذا اختار المستخدم ذلك
                if result == QMessageBox.StandardButton.Open:
                    try:
                        os.startfile(export_dir)
                    except Exception as e:
                        print(f"خطأ في فتح المجلد: {str(e)}")

                # تحديث عرض الملفات لإظهار تواريخ التصدير المحدثة
                show_folder_files()
            else:
                QMessageBox.warning(dialog, "تنبيه", f"لم يتم تصدير أي ملف.\nفشل تصدير {failed_count} ملف.")

        # تصدير المجلد كاملاً
        def export_entire_folder():
            selected_items = categories_tree.selectedItems()
            if not selected_items:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء اختيار مجلد أولاً")
                return

            selected_item = selected_items[0]
            category_id = selected_item.data(0, Qt.ItemDataRole.UserRole)
            category_name = selected_item.text(0)

            # الحصول على الملفات في هذا التصنيف
            if category_id == "all":
                documents = self.db.get_documents()
            else:
                documents = self.db.get_documents(category_id)

            if not documents:
                QMessageBox.warning(dialog, "تنبيه", "لا توجد ملفات في هذا المجلد")
                return

            # إنشاء نافذة لعرض الملفات واختيار ما سيتم تصديره
            file_selection_dialog = QDialog(dialog)
            file_selection_dialog.setWindowTitle(f"اختيار الملفات للتصدير من {category_name}")
            file_selection_dialog.setMinimumSize(700, 500)

            # تخطيط النافذة
            layout = QVBoxLayout(file_selection_dialog)

            # إضافة تعليمات
            instructions_label = QLabel("اختر الملفات التي تريد تصديرها:")
            layout.addWidget(instructions_label)

            # إنشاء جدول الملفات
            files_table = QTableWidget()
            files_table.setColumnCount(5)

            # إنشاء عناوين الأعمدة مع إضافة ظل
            header_labels = ["", "اسم الملف", "النوع", "الحجم", "تاريخ الأرشفة"]
            files_table.setHorizontalHeaderLabels(header_labels)

            # تطبيق نمط الظل على كل عنوان عمود
            header = files_table.horizontalHeader()
            for i in range(len(header_labels)):
                header_item = QTableWidgetItem(header_labels[i])
                header_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                header_item.setBackground(QColor("#3f51b5"))
                header_item.setForeground(QColor("black"))  # تغيير لون النص إلى أسود بدلاً من أبيض
                header_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
                files_table.setHorizontalHeaderItem(i, header_item)

            files_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
            files_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)

            # إضافة الملفات إلى الجدول
            files_table.setRowCount(len(documents))

            for i, doc in enumerate(documents):
                # خانة الاختيار
                checkbox_item = QTableWidgetItem()
                checkbox_item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
                checkbox_item.setCheckState(Qt.CheckState.Checked)  # تحديد جميع الملفات افتراضياً
                files_table.setItem(i, 0, checkbox_item)

                # اسم الملف
                file_name = os.path.basename(doc.get('file_path', '')) if doc.get('file_path') else doc.get('title', '')
                name_item = QTableWidgetItem(file_name)
                name_item.setData(Qt.ItemDataRole.UserRole, doc['id'])  # تخزين معرف المستند
                files_table.setItem(i, 1, name_item)

                # نوع الملف
                type_item = QTableWidgetItem(doc.get('file_type', ''))
                files_table.setItem(i, 2, type_item)

                # حجم الملف
                size = doc.get('file_size', 0)
                size_text = ""
                if size is not None:
                    if size < 1024:
                        size_text = f"{size} بايت"
                    elif size < 1024 * 1024:
                        size_text = f"{size / 1024:.2f} كيلوبايت"
                    else:
                        size_text = f"{size / (1024 * 1024):.2f} ميجابايت"
                size_item = QTableWidgetItem(size_text)
                files_table.setItem(i, 3, size_item)

                # تاريخ الأرشفة
                date_item = QTableWidgetItem(doc.get('import_date', ''))
                files_table.setItem(i, 4, date_item)

            layout.addWidget(files_table)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            select_all_btn = QPushButton("تحديد الكل")
            select_all_btn.clicked.connect(lambda: [files_table.item(row, 0).setCheckState(Qt.CheckState.Checked) for row in range(files_table.rowCount())])
            buttons_layout.addWidget(select_all_btn)

            deselect_all_btn = QPushButton("إلغاء تحديد الكل")
            deselect_all_btn.clicked.connect(lambda: [files_table.item(row, 0).setCheckState(Qt.CheckState.Unchecked) for row in range(files_table.rowCount())])
            buttons_layout.addWidget(deselect_all_btn)

            buttons_layout.addStretch()

            export_btn = QPushButton("تصدير المحدد")
            export_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3f51b5;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5c6bc0;
                }
            """)
            buttons_layout.addWidget(export_btn)

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.clicked.connect(file_selection_dialog.reject)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)

            # تنفيذ عملية التصدير
            def do_export():
                # اختيار مجلد الوجهة
                export_dir = QFileDialog.getExistingDirectory(file_selection_dialog, "اختر مجلد التصدير")
                if not export_dir:
                    return

                # إنشاء مجلد فرعي باسم التصنيف
                category_dir = os.path.join(export_dir, category_name)
                try:
                    if not os.path.exists(category_dir):
                        os.makedirs(category_dir)
                except Exception as e:
                    QMessageBox.warning(file_selection_dialog, "خطأ", f"فشل في إنشاء المجلد: {str(e)}")
                    return

                # جمع الملفات المحددة
                selected_docs = []
                for row in range(files_table.rowCount()):
                    if files_table.item(row, 0).checkState() == Qt.CheckState.Checked:
                        doc_id = files_table.item(row, 1).data(Qt.ItemDataRole.UserRole)
                        for doc in documents:
                            if doc['id'] == doc_id:
                                selected_docs.append(doc)
                                break

                if not selected_docs:
                    QMessageBox.warning(file_selection_dialog, "تنبيه", "لم يتم تحديد أي ملفات للتصدير")
                    return

                # تصدير الملفات المحددة
                exported_count = 0
                failed_count = 0

                # إنشاء نافذة تقدم العملية
                progress_dialog = QDialog(file_selection_dialog)
                progress_dialog.setWindowTitle("جاري التصدير...")
                progress_dialog.setFixedSize(400, 150)
                progress_layout = QVBoxLayout(progress_dialog)

                progress_label = QLabel("جاري تصدير الملفات...")
                progress_layout.addWidget(progress_label)

                file_label = QLabel("")
                progress_layout.addWidget(file_label)

                progress_dialog.show()
                QApplication.processEvents()

                try:
                    for i, doc in enumerate(selected_docs):
                        source_path = doc.get('file_path', '')
                        file_title = doc.get('title', '')

                        # تحديث حالة التقدم
                        file_label.setText(f"جاري تصدير: {file_title}")
                        progress_label.setText(f"تقدم العملية: {i+1} من {len(selected_docs)}")
                        QApplication.processEvents()

                        if source_path and os.path.exists(source_path):
                            # إنشاء اسم الملف الوجهة
                            file_name = os.path.basename(source_path)
                            dest_path = os.path.join(category_dir, file_name)

                            # التحقق من وجود ملف بنفس الاسم
                            if os.path.exists(dest_path):
                                # إضافة رقم للملف لتجنب التكرار
                                base_name, ext = os.path.splitext(file_name)
                                counter = 1
                                while os.path.exists(os.path.join(category_dir, f"{base_name}_{counter}{ext}")):
                                    counter += 1
                                dest_path = os.path.join(category_dir, f"{base_name}_{counter}{ext}")

                            # نسخ الملف
                            try:
                                import shutil
                                shutil.copy2(source_path, dest_path)
                                exported_count += 1

                                # تحديث تاريخ التصدير في قاعدة البيانات
                                dialog.db.update_document_export_date(doc['id'])
                            except Exception as e:
                                failed_count += 1
                                print(f"خطأ في تصدير الملف {file_name}: {str(e)}")
                finally:
                    # إغلاق نافذة التقدم
                    progress_dialog.close()

                # عرض رسالة نجاح
                if exported_count > 0:
                    success_msg = f"تم تصدير {exported_count} ملف بنجاح إلى:\n{category_dir}"
                    if failed_count > 0:
                        success_msg += f"\n\nفشل تصدير {failed_count} ملف."

                    result = QMessageBox.information(
                        file_selection_dialog,
                        "نجاح",
                        success_msg,
                        QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Open
                    )

                    # فتح المجلد بعد التصدير إذا اختار المستخدم ذلك
                    if result == QMessageBox.StandardButton.Open:
                        try:
                            os.startfile(category_dir)
                        except Exception as e:
                            print(f"خطأ في فتح المجلد: {str(e)}")

                    # تحديث عرض الملفات لإظهار تواريخ التصدير المحدثة
                    show_folder_files()
                    file_selection_dialog.accept()
                else:
                    QMessageBox.warning(file_selection_dialog, "تنبيه", f"لم يتم تصدير أي ملف.\nفشل تصدير {failed_count} ملف.")

            export_btn.clicked.connect(do_export)

            # عرض النافذة
            file_selection_dialog.exec()

        # ربط أزرار طريقة العرض
        view_as_list_btn.toggled.connect(update_view_mode)
        view_as_details_btn.toggled.connect(update_view_mode)

        # ربط النقر على التصنيفات بعرض الملفات
        categories_tree.itemClicked.connect(lambda _item, _column: show_folder_files())

        # ربط أزرار التصدير
        export_selected_btn.clicked.connect(export_selected_files)
        export_folder_btn.clicked.connect(export_entire_folder)

        # تحميل التصنيفات
        load_categories()

        # عرض النافذة باستخدام show() بدلاً من exec()
        # هذا سيجعل النافذة غير حصرية (non-modal) مما قد يحل مشكلة الإغلاق
        dialog.show()

        def generate_time_report():
            """إنشاء التقرير الزمني"""
            report_type = report_type_combo.currentData()
            start_date = start_date_edit.date().toString("yyyy-MM-dd")
            end_date = end_date_edit.date().toString("yyyy-MM-dd")

            # الحصول على إحصائيات المستندات
            stats = self.db.get_documents_stats(report_type, start_date, end_date)

            # تحديث ملخص التقرير
            total_docs_label.setText(f"إجمالي المستندات: {stats['total_documents']}")
            total_size_label.setText(f"إجمالي الحجم: {format_size(stats['total_size'])}")

            # عرض تفاصيل الملفات
            if 'files_details' in stats and stats['files_details']:
                files_details_table.setRowCount(len(stats['files_details']))

                for i, file_info in enumerate(stats['files_details']):
                    # العدد
                    count_item = QTableWidgetItem(f"{i+1}/{len(stats['files_details'])}")
                    count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 0, count_item)

                    # اسم الملف
                    file_name = os.path.basename(file_info['file_path']) if file_info['file_path'] else file_info['title']
                    name_item = QTableWidgetItem(file_name)
                    name_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                    files_details_table.setItem(i, 1, name_item)

                    # النوع
                    file_type = file_info['file_type'] if file_info['file_type'] else "غير معروف"
                    type_item = QTableWidgetItem(file_type)
                    type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 2, type_item)

                    # التصنيف
                    category_name = file_info.get('category_name', "غير مصنف")
                    category_item = QTableWidgetItem(category_name)
                    category_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                    files_details_table.setItem(i, 3, category_item)

                    # الحجم
                    size = file_info.get('file_size', 0)
                    size_item = QTableWidgetItem(format_size(size))
                    size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 4, size_item)

                    # تاريخ الأرشفة
                    import_date = file_info.get('import_date', "")
                    import_date_item = QTableWidgetItem(import_date)
                    import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    files_details_table.setItem(i, 5, import_date_item)

                    # زر المعاينة
                    preview_item = QTableWidgetItem("فتح الملف")
                    preview_item.setForeground(QColor("#3f51b5"))
                    preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    preview_item.setData(Qt.ItemDataRole.UserRole, file_info['file_path'])
                    files_details_table.setItem(i, 6, preview_item)

                # تعديل ارتفاع الصفوف لاحتواء النص
                files_details_table.resizeRowsToContents()
            else:
                files_details_table.setRowCount(0)

        def generate_exported_docs_report():
            """عرض المستندات المصدرة"""
            start_date = export_start_date_edit.date().toString("yyyy-MM-dd")
            end_date = export_end_date_edit.date().toString("yyyy-MM-dd")

            # الحصول على المستندات المصدرة
            exported_docs = self.db.get_exported_documents(start_date, end_date)

            # عرض النتائج في الجدول
            exported_docs_table.setRowCount(len(exported_docs))

            total_size = 0

            for i, doc in enumerate(exported_docs):
                # العدد
                count_item = QTableWidgetItem(f"{i+1}/{len(exported_docs)}")
                count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 0, count_item)

                # العنوان
                title_item = QTableWidgetItem(doc['title'])
                title_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                exported_docs_table.setItem(i, 1, title_item)

                # التصنيف
                category_name = doc.get('category_name', "غير مصنف")
                category_item = QTableWidgetItem(category_name)
                category_item.setTextAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
                exported_docs_table.setItem(i, 2, category_item)

                # النوع
                file_type = doc.get('file_type', "غير معروف")
                type_item = QTableWidgetItem(file_type)
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 3, type_item)

                # تاريخ الأرشفة
                import_date_item = QTableWidgetItem(doc['import_date'])
                import_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 4, import_date_item)

                # تاريخ التصدير
                export_date_item = QTableWidgetItem(doc['export_date'])
                export_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 5, export_date_item)

                # الحجم
                size = doc.get('file_size', 0)
                total_size += size or 0
                size_item = QTableWidgetItem(format_size(size))
                size_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 6, size_item)

                # زر المعاينة
                if 'file_path' in doc and doc['file_path'] and os.path.exists(doc['file_path']):
                    preview_item = QTableWidgetItem("فتح الملف")
                    preview_item.setForeground(QColor("#3f51b5"))
                    preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    preview_item.setData(Qt.ItemDataRole.UserRole, doc['file_path'])
                else:
                    preview_item = QTableWidgetItem("غير متاح")
                    preview_item.setForeground(QColor("#999999"))
                    preview_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                exported_docs_table.setItem(i, 7, preview_item)

            # تعديل ارتفاع الصفوف لاحتواء النص
            exported_docs_table.resizeRowsToContents()

            # تحديث ملخص التقرير
            export_total_docs_label.setText(f"إجمالي المستندات المصدرة: {len(exported_docs)}")
            export_total_size_label.setText(f"إجمالي الحجم: {format_size(total_size)}")

        def export_to_excel(table, filename):
            """تصدير الجدول إلى ملف Excel"""
            try:
                # التحقق من تثبيت المكتبات المطلوبة
                try:
                    import pandas as pd
                    import openpyxl
                except ImportError:
                    QMessageBox.warning(
                        dialog,
                        "مكتبات مفقودة",
                        "يرجى تثبيت المكتبات المطلوبة باستخدام الأمر:\npip install pandas openpyxl"
                    )
                    return

                # تحويل بيانات الجدول إلى DataFrame
                data = []
                headers = []

                # الحصول على العناوين
                for j in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(j).text())

                # الحصول على البيانات
                for i in range(table.rowCount()):
                    row_data = []
                    for j in range(table.columnCount()):
                        item = table.item(i, j)
                        if item is not None:
                            row_data.append(item.text())
                        else:
                            row_data.append("")
                    data.append(row_data)

                # إنشاء DataFrame
                df = pd.DataFrame(data, columns=headers)

                # حفظ الملف
                file_path, _ = QFileDialog.getSaveFileName(
                    dialog,
                    "حفظ التقرير",
                    filename,
                    "Excel Files (*.xlsx)"
                )

                if file_path:
                    df.to_excel(file_path, index=False)
                    QMessageBox.information(dialog, "نجاح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

                    # فتح الملف بعد التصدير
                    try:
                        import os
                        os.startfile(file_path)
                    except Exception as open_error:
                        print(f"تعذر فتح الملف: {str(open_error)}")

            except Exception as e:
                QMessageBox.warning(dialog, "خطأ", f"فشل في تصدير التقرير: {str(e)}")
                # طباعة تفاصيل الخطأ للمساعدة في التشخيص
                import traceback
                traceback.print_exc()

        def export_to_pdf(table, title, filename):
            """تصدير الجدول إلى ملف PDF"""
            try:
                # التحقق من تثبيت المكتبات المطلوبة
                try:
                    import reportlab
                    import arabic_reshaper
                    import bidi.algorithm
                except ImportError:
                    QMessageBox.warning(
                        dialog,
                        "مكتبات مفقودة",
                        "يرجى تثبيت المكتبات المطلوبة باستخدام الأمر:\npip install reportlab arabic-reshaper python-bidi"
                    )
                    return

                from reportlab.lib import colors
                from reportlab.lib.pagesizes import A4, landscape
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                from reportlab.lib.enums import TA_CENTER, TA_RIGHT
                import datetime
                import os
                import sys

                # حفظ الملف
                file_path, _ = QFileDialog.getSaveFileName(
                    dialog,
                    "حفظ التقرير",
                    filename,
                    "PDF Files (*.pdf)"
                )

                if not file_path:
                    return

                # تسجيل الخط العربي
                # محاولة استخدام خط عربي من النظام إذا كان متاحاً
                arabic_font_path = None
                arabic_font_name = "Arabic"

                # قائمة بالخطوط العربية الشائعة في نظام Windows
                system_arabic_fonts = [
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'arial.ttf'), "Arial"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'tahoma.ttf'), "Tahoma"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'simpo.ttf'), "Simplified Arabic"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'arabtype.ttf'), "Arabic Typesetting"),
                    (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'segoeui.ttf'), "Segoe UI")
                ]

                # التحقق من وجود الخطوط في النظام
                for font_path, font_name in system_arabic_fonts:
                    if os.path.exists(font_path):
                        arabic_font_path = font_path
                        arabic_font_name = font_name
                        break

                # إذا لم يتم العثور على خط عربي في النظام، استخدم الخط المضمن في المشروع
                if not arabic_font_path:
                    # استخدام الخط المضمن في المشروع
                    arabic_font_path = "resources/fonts/Amiri-Regular.ttf"
                    arabic_font_name = "Amiri"

                    # التحقق من وجود الخط
                    if not os.path.exists(arabic_font_path):
                        # إذا لم يكن الخط موجوداً، استخدم الخط الافتراضي
                        QMessageBox.warning(
                            dialog,
                            "تحذير",
                            "لم يتم العثور على خط عربي. سيتم استخدام الخط الافتراضي."
                        )

                # تسجيل الخط العربي إذا كان متاحاً
                if arabic_font_path and os.path.exists(arabic_font_path):
                    try:
                        pdfmetrics.registerFont(TTFont(arabic_font_name, arabic_font_path))
                        print(f"تم تسجيل الخط العربي: {arabic_font_name}")
                    except Exception as font_error:
                        print(f"خطأ في تسجيل الخط: {str(font_error)}")

                # تحويل بيانات الجدول إلى قائمة
                data = []

                # إضافة العناوين
                headers = []
                for j in range(table.columnCount()):
                    # معالجة النص العربي
                    header_text = table.horizontalHeaderItem(j).text()
                    try:
                        # إعادة تشكيل النص العربي وتحويله إلى النص ثنائي الاتجاه
                        reshaped_text = arabic_reshaper.reshape(header_text)
                        bidi_text = bidi.algorithm.get_display(reshaped_text)
                        headers.append(bidi_text)
                    except Exception as text_error:
                        print(f"خطأ في معالجة النص: {str(text_error)}")
                        headers.append(header_text)
                data.append(headers)

                # إضافة البيانات
                for i in range(table.rowCount()):
                    row_data = []
                    for j in range(table.columnCount()):
                        item = table.item(i, j)
                        if item is not None:
                            # معالجة النص العربي
                            cell_text = item.text()
                            try:
                                # إعادة تشكيل النص العربي وتحويله إلى النص ثنائي الاتجاه
                                reshaped_text = arabic_reshaper.reshape(cell_text)
                                bidi_text = bidi.algorithm.get_display(reshaped_text)
                                row_data.append(bidi_text)
                            except Exception as text_error:
                                print(f"خطأ في معالجة النص: {str(text_error)}")
                                row_data.append(cell_text)
                        else:
                            row_data.append("")
                    data.append(row_data)

                # إنشاء ملف PDF
                doc = SimpleDocTemplate(
                    file_path,
                    pagesize=landscape(A4),
                    rightMargin=30,
                    leftMargin=30,
                    topMargin=30,
                    bottomMargin=30
                )

                # إنشاء قائمة العناصر
                elements = []

                # إنشاء أنماط النصوص
                styles = getSampleStyleSheet()

                # إنشاء نمط للعنوان باستخدام الخط العربي
                title_style = ParagraphStyle(
                    name='ArabicTitle',
                    parent=styles['Heading1'],
                    fontName=arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica-Bold',
                    alignment=TA_CENTER,
                    fontSize=18
                )

                # إنشاء نمط للنص العادي باستخدام الخط العربي
                normal_style = ParagraphStyle(
                    name='ArabicNormal',
                    parent=styles['Normal'],
                    fontName=arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica',
                    alignment=TA_CENTER,
                    fontSize=12
                )

                # إضافة التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # معالجة العنوان والتاريخ للعربية
                try:
                    reshaped_title = arabic_reshaper.reshape(title)
                    bidi_title = bidi.algorithm.get_display(reshaped_title)

                    date_text = f"تاريخ التقرير: {current_datetime}"
                    reshaped_date = arabic_reshaper.reshape(date_text)
                    bidi_date = bidi.algorithm.get_display(reshaped_date)

                    # إضافة العنوان والتاريخ
                    elements.append(Paragraph(bidi_title, title_style))
                    elements.append(Paragraph(bidi_date, normal_style))
                except Exception as text_error:
                    print(f"خطأ في معالجة النص: {str(text_error)}")
                    elements.append(Paragraph(title, title_style))
                    elements.append(Paragraph(f"تاريخ التقرير: {current_datetime}", normal_style))

                elements.append(Spacer(1, 20))

                # إنشاء جدول
                table_obj = Table(data)

                # تنسيق الجدول
                table_style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica-Bold'),
                    ('FONTNAME', (0, 1), (-1, -1), arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ])

                # تطبيق التنسيق على الجدول
                table_obj.setStyle(table_style)

                # إضافة الجدول إلى العناصر
                elements.append(table_obj)

                # بناء المستند
                doc.build(elements)

                QMessageBox.information(dialog, "نجاح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

                # فتح الملف بعد التصدير
                try:
                    os.startfile(file_path)
                except Exception as open_error:
                    print(f"تعذر فتح الملف: {str(open_error)}")

            except Exception as e:
                QMessageBox.warning(dialog, "خطأ", f"فشل في تصدير التقرير: {str(e)}")
                # طباعة تفاصيل الخطأ للمساعدة في التشخيص
                import traceback
                traceback.print_exc()

        def open_file_from_table(table, row, column):
            """فتح الملف عند النقر على زر المعاينة"""
            # التحقق من أن العمود هو عمود المعاينة
            if (table == files_details_table and column == 6) or (table == exported_docs_table and column == 7):
                item = table.item(row, column)
                if item and item.text() == "فتح الملف":
                    file_path = item.data(Qt.ItemDataRole.UserRole)
                    if file_path and os.path.exists(file_path):
                        try:
                            # فتح الملف باستخدام التطبيق الافتراضي
                            os.startfile(file_path)
                        except Exception as e:
                            QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                            print(f"خطأ في فتح الملف: {str(e)}")

# تم إزالة ربط الإشارات المتعلقة بالتقارير لأنها غير مطلوبة في نافذة التصدير

        # عرض النافذة
        dialog.exec()

    def on_files_dropped(self, file_paths):
        """معالجة الملفات المسحوبة والمفلوتة"""
        if not file_paths:
            return

        # إذا كان هناك ملف واحد فقط، افتح نافذة الاستيراد مباشرة
        if len(file_paths) == 1:
            dialog = ImportDialog(self, self.db, self.file_handler)
            dialog.file_path = file_paths[0]
            dialog.file_path_input.setText(file_paths[0])

            # استخراج معلومات الملف
            try:
                dialog.file_info = dialog.file_handler.get_file_info(file_paths[0])

                # عرض معلومات الملف
                file_name = os.path.basename(file_paths[0])
                file_size = dialog.file_info['file_size']

                if file_size < 1024:
                    size_str = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.2f} كيلوبايت"
                else:
                    size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"

                file_type = dialog.file_info['file_type']

                info_text = f"الاسم: {file_name}\nالحجم: {size_str}\nالنوع: {file_type}"
                dialog.file_info_label.setText(info_text)

                # تعيين العنوان تلقائياً
                dialog.title_input.setText(os.path.splitext(file_name)[0])

                # تفعيل زر الاستيراد
                dialog.import_btn.setEnabled(True)

                if dialog.exec() == QDialog.DialogCode.Accepted:
                    self.refresh_data()

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في قراءة معلومات الملف: {str(e)}")
        else:
            # إذا كان هناك أكثر من ملف، اسأل المستخدم إذا كان يريد استيراد جميع الملفات
            confirm = QMessageBox.question(
                self,
                "استيراد متعدد",
                f"هل تريد استيراد {len(file_paths)} ملفات؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if confirm == QMessageBox.StandardButton.Yes:
                imported_count = 0
                for file_path in file_paths:
                    try:
                        # استيراد الملف مباشرة باستخدام اسم الملف كعنوان
                        file_info = self.file_handler.get_file_info(file_path)
                        file_name = os.path.basename(file_path)
                        title = os.path.splitext(file_name)[0]

                        # استيراد الملف إلى نظام التخزين
                        imported_file_info = self.file_handler.import_file(file_path)

                        # إضافة المستند إلى قاعدة البيانات
                        document_id = self.db.add_document(
                            title=title,
                            description="",
                            file_path=imported_file_info['file_path'],
                            file_type=imported_file_info['file_type'],
                            file_size=imported_file_info['file_size'],
                            original_filename=imported_file_info['original_filename'],
                            creation_date=imported_file_info['creation_date'],
                            modification_date=imported_file_info['modification_date'],
                            keywords=""
                        )

                        # تعيين التصنيف الحالي إذا كان موجوداً
                        if self.current_category_id:
                            self.db.assign_category_to_document(document_id, self.current_category_id)

                        imported_count += 1

                    except Exception as e:
                        QMessageBox.warning(
                            self,
                            "خطأ في الاستيراد",
                            f"فشل في استيراد الملف {file_path}: {str(e)}"
                        )

                if imported_count > 0:
                    QMessageBox.information(
                        self,
                        "نجاح",
                        f"تم استيراد {imported_count} من {len(file_paths)} ملفات بنجاح"
                    )
                    self.refresh_data()

    def on_view_options(self):
        """عرض قائمة خيارات العرض"""
        view_menu = QMenu()

        # إضافة خيارات العرض المختلفة
        icon_large_action = QAction(QIcon("resources/view_large.png"), "أيقونات كبيرة", self)
        icon_large_action.triggered.connect(lambda: self.change_view_mode("icon_large"))
        view_menu.addAction(icon_large_action)

        icon_medium_action = QAction(QIcon("resources/view_medium.png"), "أيقونات متوسطة", self)
        icon_medium_action.triggered.connect(lambda: self.change_view_mode("icon_medium"))
        view_menu.addAction(icon_medium_action)

        icon_small_action = QAction(QIcon("resources/view_small.png"), "أيقونات صغيرة", self)
        icon_small_action.triggered.connect(lambda: self.change_view_mode("icon_small"))
        view_menu.addAction(icon_small_action)

        list_action = QAction(QIcon("resources/view_list.png"), "قائمة", self)
        list_action.triggered.connect(lambda: self.change_view_mode("list"))
        view_menu.addAction(list_action)

        details_action = QAction(QIcon("resources/view_details.png"), "تفاصيل", self)
        details_action.triggered.connect(lambda: self.change_view_mode("details"))
        view_menu.addAction(details_action)

        view_menu.addSeparator()

        # إضافة خيار لعرض التصنيفات
        categories_action = QAction(QIcon("resources/categories.png"), "عرض التصنيفات", self)
        categories_action.triggered.connect(self.show_categories_dialog)
        view_menu.addAction(categories_action)

        # عرض القائمة عند النقر على زر العرض
        view_menu.exec(QCursor.pos())

    def change_view_mode(self, mode):
        """تغيير نمط عرض المستندات"""
        self.current_view_mode = mode

        # تخزين العناصر المحددة حاليًا
        selected_items = self.documents_list.selectedItems()
        selected_ids = [item.data(Qt.ItemDataRole.UserRole) for item in selected_items]

        # تطبيق نمط العرض الجديد
        if mode == "icon_large":
            self.documents_list.setViewMode(QListView.ViewMode.IconMode)
            self.documents_list.setIconSize(QSize(96, 96))
            self.documents_list.setGridSize(QSize(120, 150))
            self.documents_list.setSpacing(10)
        elif mode == "icon_medium":
            self.documents_list.setViewMode(QListView.ViewMode.IconMode)
            self.documents_list.setIconSize(QSize(64, 64))
            self.documents_list.setGridSize(QSize(100, 120))
            self.documents_list.setSpacing(8)
        elif mode == "icon_small":
            self.documents_list.setViewMode(QListView.ViewMode.IconMode)
            self.documents_list.setIconSize(QSize(32, 32))
            self.documents_list.setGridSize(QSize(80, 90))
            self.documents_list.setSpacing(5)
        elif mode == "list":
            self.documents_list.setViewMode(QListView.ViewMode.ListMode)
            self.documents_list.setIconSize(QSize(32, 32))
            self.documents_list.setSpacing(0)
            self.documents_list.setGridSize(QSize())
        elif mode == "details":
            # في وضع التفاصيل، نبقى في وضع القائمة ولكن نعدل طريقة العرض
            self.documents_list.setViewMode(QListView.ViewMode.ListMode)
            self.documents_list.setIconSize(QSize(24, 24))
            self.documents_list.setSpacing(0)
            self.documents_list.setGridSize(QSize())

        # إعادة تحميل المستندات لتطبيق نمط العرض الجديد
        self.load_documents(self.current_category_id)

        # إعادة تحديد العناصر التي كانت محددة
        for i in range(self.documents_list.count()):
            item = self.documents_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) in selected_ids:
                item.setSelected(True)

    def show_categories_dialog(self):
        """عرض نافذة حوار التصنيفات"""
        dialog = QDialog(self)
        dialog.setWindowTitle("التصنيفات")
        dialog.setMinimumSize(500, 400)

        layout = QVBoxLayout(dialog)

        # إنشاء شجرة التصنيفات
        categories_tree = QTreeWidget()
        categories_tree.setHeaderLabels(["الاسم"])
        categories_tree.setHeaderHidden(True)
        categories_tree.setAnimated(True)
        categories_tree.setIconSize(QSize(24, 24))
        categories_tree.setFont(QFont("Arial", 11))

        # نسخ التصنيفات من الشجرة الرئيسية
        for i in range(self.categories_tree.topLevelItemCount()):
            top_item = self.categories_tree.topLevelItem(i)
            new_item = QTreeWidgetItem([top_item.text(0)])
            new_item.setData(0, Qt.ItemDataRole.UserRole, top_item.data(0, Qt.ItemDataRole.UserRole))
            categories_tree.addTopLevelItem(new_item)

            # نسخ العناصر الفرعية إذا كانت موجودة
            self._copy_tree_items(top_item, new_item)

        # توسيع الشجرة
        categories_tree.expandAll()

        layout.addWidget(categories_tree)

        # إضافة زر لعرض الملفات في التصنيف المحدد
        show_button = QPushButton("عرض الملفات")
        show_button.clicked.connect(lambda: self._show_category_files(categories_tree, dialog))
        layout.addWidget(show_button)

        dialog.exec()

    def _copy_tree_items(self, source_item, target_item):
        """نسخ عناصر الشجرة بشكل متكرر"""
        for i in range(source_item.childCount()):
            child = source_item.child(i)
            new_child = QTreeWidgetItem([child.text(0)])
            new_child.setData(0, Qt.ItemDataRole.UserRole, child.data(0, Qt.ItemDataRole.UserRole))
            target_item.addChild(new_child)

            # نسخ العناصر الفرعية بشكل متكرر
            self._copy_tree_items(child, new_child)

    def _show_category_files(self, tree_widget, dialog):
        """عرض ملفات التصنيف المحدد"""
        selected_items = tree_widget.selectedItems()
        if selected_items:
            selected_item = selected_items[0]
            category_id = selected_item.data(0, Qt.ItemDataRole.UserRole)

            # تحديث التصنيف الحالي في النافذة الرئيسية
            self.current_category_id = category_id

            # تحميل المستندات للتصنيف المحدد
            self.load_documents(category_id)

            # إغلاق نافذة الحوار
            dialog.accept()

    def show_view_dialog(self):
        """عرض نافذة حوار العرض"""
        from ui.view_dialog import ViewDialog
        dialog = ViewDialog(self)
        dialog.exec()

    def show_all_files_view_dialog(self):
        """عرض نافذة حوار العرض مع خيار عرض جميع الملفات"""
        try:
            from ui.view_dialog import ViewDialog
            dialog = ViewDialog(self, show_all_files_option=True)
            dialog.exec()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء فتح نافذة العرض الجديد: {str(e)}")
            print(f"خطأ في دالة show_all_files_view_dialog: {str(e)}")

    def on_activate_license(self):
        """عرض نافذة تفعيل البرنامج"""
        from ui.license_dialog import LicenseDialog
        from utils.license_manager import LicenseManager

        license_manager = LicenseManager()
        if LicenseDialog.get_license(license_manager):
            # تحديث شريط الحالة بمعلومات الترخيص الجديدة
            license_info = license_manager.get_license_info()
            if license_info:
                if license_info.get("type") == "trial":
                    days_left = license_info.get("days_left", 0)
                    self.statusBar.showMessage(f"نسخة تجريبية: متبقي {days_left} يوم")
                else:
                    self.statusBar.showMessage("نسخة مرخصة")

            QMessageBox.information(
                self,
                "تفعيل البرنامج",
                "تم تفعيل البرنامج بنجاح!"
            )

    def on_settings(self):
        """فتح نافذة الإعدادات"""
        settings_dialog = SettingsDialog(self)
        settings_dialog.exec()

    def show_view_dialog(self):
        """عرض نافذة حوار العرض"""
        dialog = QDialog(self)
        dialog.setWindowTitle("عرض المجلدات")
        dialog.setMinimumSize(900, 700)

        # إضافة كائن قاعدة البيانات إلى نافذة الحوار
        dialog.db = self.db

        # تعريف المتغيرات التي ستستخدم في الدوال الداخلية
        current_category_id = None
        file_info_cache = {}

        main_layout = QVBoxLayout(dialog)

        # القسم العلوي: اختيار المجلد والعرض
        top_layout = QHBoxLayout()

        # قسم اختيار المجلد
        folder_layout = QVBoxLayout()
        folder_label = QLabel("اختر مجلد التصنيف:")
        folder_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        folder_layout.addWidget(folder_label)

        # إنشاء شجرة التصنيفات
        categories_tree = QTreeWidget()
        categories_tree.setHeaderLabels(["الاسم"])
        categories_tree.setHeaderHidden(True)
        categories_tree.setAnimated(True)
        categories_tree.setIconSize(QSize(24, 24))
        categories_tree.setFont(QFont("Arial", 11))
        categories_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 30px;
            }
            QTreeWidget::item:selected {
                background-color: #3f51b5;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #e8eaf6;
            }
        """)

        # نسخ التصنيفات من الشجرة الرئيسية
        for i in range(self.categories_tree.topLevelItemCount()):
            top_item = self.categories_tree.topLevelItem(i)
            new_item = QTreeWidgetItem([top_item.text(0)])
            new_item.setData(0, Qt.ItemDataRole.UserRole, top_item.data(0, Qt.ItemDataRole.UserRole))
            categories_tree.addTopLevelItem(new_item)

            # نسخ العناصر الفرعية إذا كانت موجودة
            self._copy_tree_items(top_item, new_item)

        # توسيع الشجرة
        categories_tree.expandAll()
        folder_layout.addWidget(categories_tree)

        # زر عرض محتويات المجلد
        show_folder_btn = QPushButton("عرض محتويات المجلد")
        show_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #3f51b5;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #303f9f;
            }
        """)
        folder_layout.addWidget(show_folder_btn)

        top_layout.addLayout(folder_layout, 1)

        # قسم خيارات العرض
        view_options_layout = QVBoxLayout()
        view_options_label = QLabel("خيارات العرض:")
        view_options_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        view_options_layout.addWidget(view_options_label)

        # أزرار خيارات العرض
        view_buttons_layout = QGridLayout()

        # زر أيقونات كبيرة
        large_icons_btn = QPushButton("أيقونات كبيرة")
        large_icons_btn.setCheckable(True)
        large_icons_btn.setChecked(True)  # افتراضي
        view_buttons_layout.addWidget(large_icons_btn, 0, 0)

        # زر أيقونات متوسطة
        medium_icons_btn = QPushButton("أيقونات متوسطة")
        medium_icons_btn.setCheckable(True)
        view_buttons_layout.addWidget(medium_icons_btn, 0, 1)

        # زر أيقونات صغيرة
        small_icons_btn = QPushButton("أيقونات صغيرة")
        small_icons_btn.setCheckable(True)
        view_buttons_layout.addWidget(small_icons_btn, 1, 0)

        # زر تفاصيل
        details_btn = QPushButton("تفاصيل")
        details_btn.setCheckable(True)
        view_buttons_layout.addWidget(details_btn, 1, 1)

        # مجموعة الأزرار للتأكد من اختيار واحد فقط
        view_buttons = [large_icons_btn, medium_icons_btn, small_icons_btn, details_btn]

        def toggle_view_button(button):
            for btn in view_buttons:
                if btn != button:
                    btn.setChecked(False)
            button.setChecked(True)

        large_icons_btn.clicked.connect(lambda: toggle_view_button(large_icons_btn))
        medium_icons_btn.clicked.connect(lambda: toggle_view_button(medium_icons_btn))
        small_icons_btn.clicked.connect(lambda: toggle_view_button(small_icons_btn))
        details_btn.clicked.connect(lambda: toggle_view_button(details_btn))

        view_options_layout.addLayout(view_buttons_layout)

        # إضافة فاصل بين خيارات العرض وخيارات الفرز
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("background-color: #c5cae9; margin: 10px 0;")
        view_options_layout.addWidget(separator)

        # قسم خيارات الفرز
        sort_options_label = QLabel("فرز الملفات حسب:")
        sort_options_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 5px;")
        view_options_layout.addWidget(sort_options_label)

        # أزرار خيارات الفرز
        sort_buttons_layout = QGridLayout()

        # زر فرز حسب الاسم
        sort_by_name_btn = QPushButton("الاسم")
        sort_by_name_btn.setCheckable(True)
        sort_by_name_btn.setChecked(True)  # افتراضي
        sort_buttons_layout.addWidget(sort_by_name_btn, 0, 0)

        # زر فرز حسب التاريخ
        sort_by_date_btn = QPushButton("التاريخ")
        sort_by_date_btn.setCheckable(True)
        sort_buttons_layout.addWidget(sort_by_date_btn, 0, 1)

        # زر فرز حسب النوع
        sort_by_type_btn = QPushButton("النوع")
        sort_by_type_btn.setCheckable(True)
        sort_buttons_layout.addWidget(sort_by_type_btn, 1, 0)

        # زر فرز حسب الحجم
        sort_by_size_btn = QPushButton("الحجم")
        sort_by_size_btn.setCheckable(True)
        sort_buttons_layout.addWidget(sort_by_size_btn, 1, 1)

        # تنسيق أزرار الفرز
        sort_buttons = [sort_by_name_btn, sort_by_date_btn, sort_by_type_btn, sort_by_size_btn]
        for btn in sort_buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #e8eaf6;
                    color: #3f51b5;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                    border: 1px solid #c5cae9;
                }
                QPushButton:hover {
                    background-color: #c5cae9;
                }
                QPushButton:checked {
                    background-color: #3f51b5;
                    color: white;
                }
            """)

        # مجموعة الأزرار للتأكد من اختيار واحد فقط
        def toggle_sort_button(button):
            for btn in sort_buttons:
                if btn != button:
                    btn.setChecked(False)
            button.setChecked(True)
            # سيتم ربط وظيفة الفرز لاحقًا

        sort_by_name_btn.clicked.connect(lambda: toggle_sort_button(sort_by_name_btn))
        sort_by_date_btn.clicked.connect(lambda: toggle_sort_button(sort_by_date_btn))
        sort_by_type_btn.clicked.connect(lambda: toggle_sort_button(sort_by_type_btn))
        sort_by_size_btn.clicked.connect(lambda: toggle_sort_button(sort_by_size_btn))

        view_options_layout.addLayout(sort_buttons_layout)
        view_options_layout.addStretch()

        top_layout.addLayout(view_options_layout, 1)

        main_layout.addLayout(top_layout)

        # القسم السفلي: عرض الملفات
        files_label = QLabel("محتويات المجلد:")
        files_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 10px;")
        main_layout.addWidget(files_label)

        # إنشاء عرض التفاصيل (جدول) - سيتم استبداله لاحقاً
        files_table = QTableWidget()
        files_table.setColumnCount(6)
        files_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ الإنشاء", "تاريخ التعديل", "تاريخ الاستيراد"])
        files_table.horizontalHeader().setStretchLastSection(True)
        # تعيين وضع تمدد العمود الأول
        header = files_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

        # ضبط عرض الأعمدة الأخرى
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # النوع
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الحجم
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive)  # تاريخ الإنشاء
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive)  # تاريخ التعديل
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Interactive)  # تاريخ الاستيراد

        # تعيين الحد الأدنى لعرض أعمدة التاريخ
        files_table.setColumnWidth(3, 150)  # تاريخ الإنشاء
        files_table.setColumnWidth(4, 150)  # تاريخ التعديل
        files_table.setColumnWidth(5, 150)  # تاريخ الاستيراد

        # تمكين التفاف النص في الجدول
        files_table.setWordWrap(True)
        files_table.setTextElideMode(Qt.TextElideMode.ElideNone)

        # ضبط ارتفاع الصفوف لتناسب المحتوى
        files_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        files_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #3f51b5;
                color: black;
                padding: 8px;
                font-weight: bold;
                border: 1px solid #303f9f;
                white-space: normal;
                word-wrap: break-word;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f0f0f0;
                white-space: normal;
                word-wrap: break-word;
            }
            QTableWidget::item:selected {
                background-color: #e8eaf6;
                color: black;
            }
        """)
        files_table.setVisible(False)  # مخفي افتراضيًا
        main_layout.addWidget(files_table)

        # قائمة الملفات بالأيقونات - سيتم استبدالها لاحقاً
        files_list = QListWidget()
        files_list.setAlternatingRowColors(True)
        files_list.setIconSize(QSize(64, 64))  # افتراضي: أيقونات كبيرة
        files_list.setViewMode(QListView.ViewMode.IconMode)
        files_list.setGridSize(QSize(120, 150))
        files_list.setResizeMode(QListView.ResizeMode.Adjust)
        files_list.setWrapping(True)
        files_list.setWordWrap(True)
        files_list.setSpacing(10)
        files_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 12px;
            }
            QListWidget::item {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #f5f5f5;
            }
            QListWidget::item:selected {
                background-color: #e8eaf6;
                border: 2px solid #3f51b5;
            }
            QListWidget::item:hover {
                background-color: #eeeeee;
                border: 1px solid #c5cae9;
            }
        """)
        main_layout.addWidget(files_list)

        # تغيير نمط العرض عند النقر على أزرار العرض
        def change_to_large_icons():
            files_list.setVisible(True)
            files_table.setVisible(False)
            files_list.setViewMode(QListView.ViewMode.IconMode)
            files_list.setIconSize(QSize(128, 128))  # زيادة حجم الأيقونات
            files_list.setGridSize(QSize(180, 220))  # زيادة حجم الشبكة
            files_list.setSpacing(20)  # زيادة المسافة بين العناصر

            # تحديث العرض إذا كانت هناك ملفات
            if current_category_id is not None:
                show_folder_files()

        def change_to_medium_icons():
            files_list.setVisible(True)
            files_table.setVisible(False)
            files_list.setViewMode(QListView.ViewMode.IconMode)
            files_list.setIconSize(QSize(96, 96))
            files_list.setGridSize(QSize(150, 180))
            files_list.setSpacing(15)

            # تحديث العرض إذا كانت هناك ملفات
            if current_category_id is not None:
                show_folder_files()

        def change_to_small_icons():
            files_list.setVisible(True)
            files_table.setVisible(False)
            files_list.setViewMode(QListView.ViewMode.IconMode)
            files_list.setIconSize(QSize(64, 64))
            files_list.setGridSize(QSize(120, 150))
            files_list.setSpacing(10)

            # تحديث العرض إذا كانت هناك ملفات
            if current_category_id is not None:
                show_folder_files()

        def change_to_details():
            files_list.setVisible(False)
            files_table.setVisible(True)
            # إذا كان هناك ملفات معروضة، قم بعرضها في جدول التفاصيل
            if current_category_id is not None:
                show_files_in_details_view()

        large_icons_btn.clicked.connect(change_to_large_icons)
        medium_icons_btn.clicked.connect(change_to_medium_icons)
        small_icons_btn.clicked.connect(change_to_small_icons)
        details_btn.clicked.connect(change_to_details)

        # دالة لإنشاء صورة مصغرة للملف
        def create_thumbnail(file_path, file_type):
            """إنشاء صورة مصغرة للملف"""
            if not file_path or not os.path.exists(file_path):
                return get_default_icon(file_type)

            try:
                file_type_lower = file_type.lower() if file_type else ""

                # تحسين الصور المصغرة للملفات المختلفة
                if 'pdf' in file_type_lower:
                    # إنشاء أيقونة PDF محسنة
                    try:
                        # إنشاء صورة جديدة بخلفية بيضاء
                        pixmap = QPixmap(160, 160)
                        pixmap.fill(Qt.GlobalColor.transparent)

                        # رسم الأيقونة
                        painter = QPainter(pixmap)
                        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

                        # رسم ظل خفيف
                        painter.setPen(Qt.PenStyle.NoPen)
                        painter.setBrush(QColor(0, 0, 0, 30))
                        painter.drawRoundedRect(5, 5, 150, 150, 5, 5)

                        # رسم خلفية بيضاء
                        painter.setBrush(QColor(255, 255, 255))
                        painter.setPen(QPen(QColor("#dddddd"), 1))
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        # رسم أيقونة PDF
                        pdf_icon = QPixmap("resources/pdf.png")
                        if not pdf_icon.isNull():
                            pdf_icon = pdf_icon.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                            painter.drawPixmap((pixmap.width() - pdf_icon.width()) // 2, (pixmap.height() - pdf_icon.height()) // 2, pdf_icon)

                        # رسم إطار
                        painter.setPen(QPen(QColor("#aaaaaa"), 1))
                        painter.setBrush(Qt.BrushStyle.NoBrush)
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        painter.end()

                        return QIcon(pixmap)
                    except Exception as e:
                        print(f"خطأ في إنشاء أيقونة PDF: {str(e)}")
                        return QIcon("resources/pdf.png")

                elif 'word' in file_type_lower or 'doc' in file_type_lower:
                    # إنشاء أيقونة Word محسنة
                    try:
                        # إنشاء صورة جديدة بخلفية بيضاء
                        pixmap = QPixmap(160, 160)
                        pixmap.fill(Qt.GlobalColor.transparent)

                        # رسم الأيقونة
                        painter = QPainter(pixmap)
                        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

                        # رسم ظل خفيف
                        painter.setPen(Qt.PenStyle.NoPen)
                        painter.setBrush(QColor(0, 0, 0, 30))
                        painter.drawRoundedRect(5, 5, 150, 150, 5, 5)

                        # رسم خلفية بيضاء
                        painter.setBrush(QColor(255, 255, 255))
                        painter.setPen(QPen(QColor("#dddddd"), 1))
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        # رسم أيقونة Word
                        word_icon = QPixmap("resources/word.png")
                        if not word_icon.isNull():
                            word_icon = word_icon.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                            painter.drawPixmap((pixmap.width() - word_icon.width()) // 2, (pixmap.height() - word_icon.height()) // 2, word_icon)

                        # رسم إطار
                        painter.setPen(QPen(QColor("#aaaaaa"), 1))
                        painter.setBrush(Qt.BrushStyle.NoBrush)
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        painter.end()

                        return QIcon(pixmap)
                    except Exception as e:
                        print(f"خطأ في إنشاء أيقونة Word: {str(e)}")
                        return QIcon("resources/word.png")

                elif 'excel' in file_type_lower or 'xls' in file_type_lower:
                    # إنشاء أيقونة Excel محسنة
                    try:
                        # إنشاء صورة جديدة بخلفية بيضاء
                        pixmap = QPixmap(160, 160)
                        pixmap.fill(Qt.GlobalColor.transparent)

                        # رسم الأيقونة
                        painter = QPainter(pixmap)
                        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

                        # رسم ظل خفيف
                        painter.setPen(Qt.PenStyle.NoPen)
                        painter.setBrush(QColor(0, 0, 0, 30))
                        painter.drawRoundedRect(5, 5, 150, 150, 5, 5)

                        # رسم خلفية بيضاء
                        painter.setBrush(QColor(255, 255, 255))
                        painter.setPen(QPen(QColor("#dddddd"), 1))
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        # رسم أيقونة Excel
                        excel_icon = QPixmap("resources/excel.png")
                        if not excel_icon.isNull():
                            excel_icon = excel_icon.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                            painter.drawPixmap((pixmap.width() - excel_icon.width()) // 2, (pixmap.height() - excel_icon.height()) // 2, excel_icon)

                        # رسم إطار
                        painter.setPen(QPen(QColor("#aaaaaa"), 1))
                        painter.setBrush(Qt.BrushStyle.NoBrush)
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        painter.end()

                        return QIcon(pixmap)
                    except Exception as e:
                        print(f"خطأ في إنشاء أيقونة Excel: {str(e)}")
                        return QIcon("resources/excel.png")

                elif ('image' in file_type_lower or
                      'jpg' in file_type_lower or
                      'jpeg' in file_type_lower or
                      'png' in file_type_lower or
                      'gif' in file_type_lower or
                      'bmp' in file_type_lower or
                      'tiff' in file_type_lower):
                    # إنشاء صورة مصغرة للصور بجودة أعلى
                    try:
                        pixmap = QPixmap(file_path)
                        if not pixmap.isNull():
                            # إنشاء صورة مصغرة بحجم أكبر وجودة أعلى
                            pixmap = pixmap.scaled(160, 160, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)

                            # إنشاء صورة جديدة بخلفية بيضاء لإضافة إطار وظل
                            result_pixmap = QPixmap(pixmap.width() + 10, pixmap.height() + 10)
                            result_pixmap.fill(Qt.GlobalColor.transparent)

                            # رسم الصورة مع إطار وظل
                            painter = QPainter(result_pixmap)
                            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

                            # رسم ظل خفيف
                            painter.setPen(Qt.PenStyle.NoPen)
                            painter.setBrush(QColor(0, 0, 0, 30))
                            painter.drawRoundedRect(5, 5, pixmap.width(), pixmap.height(), 3, 3)

                            # رسم خلفية بيضاء
                            painter.setBrush(QColor(255, 255, 255))
                            painter.setPen(QPen(QColor("#dddddd"), 1))
                            painter.drawRoundedRect(2, 2, pixmap.width(), pixmap.height(), 3, 3)

                            # رسم الصورة
                            painter.drawPixmap(2, 2, pixmap)

                            # رسم إطار
                            painter.setPen(QPen(QColor("#aaaaaa"), 1))
                            painter.setBrush(Qt.BrushStyle.NoBrush)
                            painter.drawRoundedRect(2, 2, pixmap.width(), pixmap.height(), 3, 3)

                            painter.end()

                            return QIcon(result_pixmap)
                        else:
                            return QIcon("resources/image.png")
                    except Exception as img_error:
                        print(f"خطأ في معالجة الصورة: {str(img_error)}")
                        return QIcon("resources/image.png")

                elif 'text' in file_type_lower or 'txt' in file_type_lower:
                    # إنشاء أيقونة نص محسنة
                    try:
                        # إنشاء صورة جديدة بخلفية بيضاء
                        pixmap = QPixmap(160, 160)
                        pixmap.fill(Qt.GlobalColor.transparent)

                        # رسم الأيقونة
                        painter = QPainter(pixmap)
                        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

                        # رسم ظل خفيف
                        painter.setPen(Qt.PenStyle.NoPen)
                        painter.setBrush(QColor(0, 0, 0, 30))
                        painter.drawRoundedRect(5, 5, 150, 150, 5, 5)

                        # رسم خلفية بيضاء
                        painter.setBrush(QColor(255, 255, 255))
                        painter.setPen(QPen(QColor("#dddddd"), 1))
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        # رسم أيقونة نص
                        text_icon = QPixmap("resources/file.png")
                        if not text_icon.isNull():
                            text_icon = text_icon.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                            painter.drawPixmap((pixmap.width() - text_icon.width()) // 2, (pixmap.height() - text_icon.height()) // 2, text_icon)

                        # رسم إطار
                        painter.setPen(QPen(QColor("#aaaaaa"), 1))
                        painter.setBrush(Qt.BrushStyle.NoBrush)
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        painter.end()

                        return QIcon(pixmap)
                    except Exception as e:
                        print(f"خطأ في إنشاء أيقونة نص: {str(e)}")
                        return QIcon("resources/file.png")

                else:
                    # استخدام أيقونة افتراضية محسنة
                    try:
                        # إنشاء صورة جديدة بخلفية بيضاء
                        pixmap = QPixmap(160, 160)
                        pixmap.fill(Qt.GlobalColor.transparent)

                        # رسم الأيقونة
                        painter = QPainter(pixmap)
                        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

                        # رسم ظل خفيف
                        painter.setPen(Qt.PenStyle.NoPen)
                        painter.setBrush(QColor(0, 0, 0, 30))
                        painter.drawRoundedRect(5, 5, 150, 150, 5, 5)

                        # رسم خلفية بيضاء
                        painter.setBrush(QColor(255, 255, 255))
                        painter.setPen(QPen(QColor("#dddddd"), 1))
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        # رسم أيقونة افتراضية
                        default_icon = get_default_icon(file_type)
                        default_pixmap = default_icon.pixmap(100, 100)
                        if not default_pixmap.isNull():
                            painter.drawPixmap((pixmap.width() - default_pixmap.width()) // 2, (pixmap.height() - default_pixmap.height()) // 2, default_pixmap)

                        # رسم إطار
                        painter.setPen(QPen(QColor("#aaaaaa"), 1))
                        painter.setBrush(Qt.BrushStyle.NoBrush)
                        painter.drawRoundedRect(2, 2, 150, 150, 5, 5)

                        painter.end()

                        return QIcon(pixmap)
                    except Exception as e:
                        print(f"خطأ في إنشاء أيقونة افتراضية: {str(e)}")
                        return get_default_icon(file_type)

            except Exception as e:
                print(f"خطأ في إنشاء صورة مصغرة: {str(e)}")
                return get_default_icon(file_type)

        def get_default_icon(file_type):
            """الحصول على أيقونة افتراضية بناءً على نوع الملف"""
            file_type = file_type.lower() if file_type else ""
            if 'pdf' in file_type:
                return QIcon("resources/pdf.png")
            elif 'word' in file_type or 'doc' in file_type:
                return QIcon("resources/word.png")
            elif 'excel' in file_type or 'xls' in file_type:
                return QIcon("resources/excel.png")
            elif 'image' in file_type or 'jpg' in file_type or 'png' in file_type:
                return QIcon("resources/image.png")
            else:
                return QIcon("resources/file.png")

        def format_size(size):
            """تنسيق حجم الملف بشكل مقروء"""
            if size is None:
                return "0 بايت"

            if size < 1024:
                return f"{size} بايت"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} كيلوبايت"
            elif size < 1024 * 1024 * 1024:
                return f"{size / (1024 * 1024):.1f} ميجابايت"
            else:
                return f"{size / (1024 * 1024 * 1024):.1f} جيجابايت"

        def format_date(date_str):
            """تنسيق التاريخ بشكل مقروء"""
            if not date_str:
                return ""
            try:
                from datetime import datetime
                date_obj = datetime.fromisoformat(date_str)
                return date_obj.strftime("%Y-%m-%d %H:%M:%S")
            except:
                return date_str

        def show_files_in_details_view():
            """عرض الملفات في عرض التفاصيل"""
            files_table.setRowCount(0)

            if not file_info_cache:
                return

            for doc_id, doc in file_info_cache.items():
                row = files_table.rowCount()
                files_table.insertRow(row)

                # إنشاء عنصر للاسم مع أيقونة
                name_item = QTableWidgetItem(doc['title'])
                name_item.setData(Qt.ItemDataRole.UserRole, doc_id)
                icon = get_default_icon(doc.get('file_type', ''))
                name_item.setIcon(icon)
                files_table.setItem(row, 0, name_item)

                # نوع الملف
                type_item = QTableWidgetItem(doc.get('file_type', ''))
                files_table.setItem(row, 1, type_item)

                # حجم الملف
                size_item = QTableWidgetItem(format_size(doc.get('file_size', 0)))
                files_table.setItem(row, 2, size_item)

                # تاريخ الإنشاء
                creation_date_item = QTableWidgetItem(format_date(doc.get('creation_date', '')))
                files_table.setItem(row, 3, creation_date_item)

                # تاريخ التعديل
                modification_date_item = QTableWidgetItem(format_date(doc.get('modification_date', '')))
                files_table.setItem(row, 4, modification_date_item)

                # تاريخ الاستيراد
                import_date_item = QTableWidgetItem(format_date(doc.get('import_date', '')))
                files_table.setItem(row, 5, import_date_item)

        # فتح الملف عند النقر المزدوج
        def open_file_from_list(item):
            """فتح الملف عند النقر المزدوج على عنصر في القائمة"""
            doc_id = item.data(Qt.ItemDataRole.UserRole)
            if doc_id in file_info_cache:
                file_path = file_info_cache[doc_id].get('file_path', '')
                if file_path and os.path.exists(file_path):
                    try:
                        # فتح الملف باستخدام التطبيق الافتراضي
                        os.startfile(file_path)
                    except Exception as e:
                        QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                else:
                    QMessageBox.warning(dialog, "خطأ", "الملف غير موجود")

        def open_file_from_table(item):
            """فتح الملف عند النقر المزدوج على عنصر في الجدول"""
            if item.column() == 0:  # فقط عند النقر على عمود الاسم
                doc_id = item.data(Qt.ItemDataRole.UserRole)
                if doc_id in file_info_cache:
                    file_path = file_info_cache[doc_id].get('file_path', '')
                    if file_path and os.path.exists(file_path):
                        try:
                            # فتح الملف باستخدام التطبيق الافتراضي
                            os.startfile(file_path)
                        except Exception as e:
                            QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                            print(f"خطأ في فتح الملف: {str(e)}")

        # ربط النقر المزدوج بفتح الملف
        files_list.itemDoubleClicked.connect(open_file_from_list)
        files_table.itemDoubleClicked.connect(open_file_from_table)

        # تعريف فئات مخصصة للسحب والإفلات
        class FileListWidget(QListWidget):
            def startDrag(self, _):  # تجاهل المعلمة supportedActions لأننا سنستخدم CopyAction دائمًا
                try:
                    items = self.selectedItems()
                    if not items:
                        return

                    # إنشاء كائن QMimeData
                    mime_data = QMimeData()
                    urls = []
                    exported_doc_ids = []  # قائمة بمعرفات المستندات المصدرة

                    for item in items:
                        doc_id = item.data(Qt.ItemDataRole.UserRole)
                        if doc_id in file_info_cache:
                            file_path = file_info_cache[doc_id].get('file_path', '')
                            if file_path and os.path.exists(file_path):
                                url = QUrl.fromLocalFile(file_path)
                                urls.append(url)
                                exported_doc_ids.append(doc_id)  # إضافة معرف المستند إلى قائمة المستندات المصدرة

                    if urls:
                        mime_data.setUrls(urls)

                        # إنشاء كائن QDrag
                        drag = QDrag(self)
                        drag.setMimeData(mime_data)

                        # تعيين صورة للسحب
                        pixmap = QPixmap(32, 32)
                        pixmap.fill(Qt.GlobalColor.transparent)
                        drag.setPixmap(pixmap)

                        # بدء عملية السحب - تحديد أن العملية هي نسخ فقط
                        result = drag.exec(Qt.DropAction.CopyAction)

                        # إذا تمت عملية السحب بنجاح، قم بتسجيل عملية التصدير
                        if result == Qt.DropAction.CopyAction:
                            try:
                                # تسجيل عملية التصدير في قاعدة البيانات
                                # محاولة الوصول إلى قاعدة البيانات من خلال النافذة الرئيسية أو نافذة الحوار
                                parent_widget = self.window()

                                # البحث عن كائن قاعدة البيانات في المكان المناسب
                                db_obj = None

                                # محاولة الوصول إلى قاعدة البيانات من نافذة الحوار
                                for widget in QApplication.topLevelWidgets():
                                    if isinstance(widget, QDialog) and hasattr(widget, 'db'):
                                        db_obj = widget.db
                                        break

                                # إذا لم يتم العثور على قاعدة البيانات في نافذة الحوار، استخدم النافذة الرئيسية
                                if db_obj is None and hasattr(parent_widget, 'db'):
                                    db_obj = parent_widget.db

                                # استخدام قاعدة البيانات لتحديث تاريخ التصدير
                                if db_obj is not None:
                                    for doc_id in exported_doc_ids:
                                        db_obj.update_document_export_date(doc_id)
                                    print("تم تسجيل عملية التصدير بنجاح")
                            except Exception as e:
                                print(f"خطأ في تسجيل عملية التصدير: {str(e)}")
                except Exception as e:
                    print(f"خطأ في عملية السحب: {str(e)}")

        class FileTableWidget(QTableWidget):
            def startDrag(self, _):  # تجاهل المعلمة supportedActions لأننا سنستخدم CopyAction دائمًا
                try:
                    items = self.selectedItems()
                    if not items:
                        return

                    # إنشاء كائن QMimeData
                    mime_data = QMimeData()
                    urls = []
                    processed_docs = set()  # لتجنب تكرار الملفات
                    exported_doc_ids = []  # قائمة بمعرفات المستندات المصدرة

                    for item in items:
                        if item.column() == 0:  # فقط عمود الاسم
                            doc_id = item.data(Qt.ItemDataRole.UserRole)
                            if doc_id in file_info_cache and doc_id not in processed_docs:
                                processed_docs.add(doc_id)
                                file_path = file_info_cache[doc_id].get('file_path', '')
                                if file_path and os.path.exists(file_path):
                                    url = QUrl.fromLocalFile(file_path)
                                    urls.append(url)
                                    exported_doc_ids.append(doc_id)  # إضافة معرف المستند إلى قائمة المستندات المصدرة

                    if urls:
                        mime_data.setUrls(urls)

                        # إنشاء كائن QDrag
                        drag = QDrag(self)
                        drag.setMimeData(mime_data)

                        # تعيين صورة للسحب
                        pixmap = QPixmap(32, 32)
                        pixmap.fill(Qt.GlobalColor.transparent)
                        drag.setPixmap(pixmap)

                        # بدء عملية السحب - تحديد أن العملية هي نسخ فقط
                        result = drag.exec(Qt.DropAction.CopyAction)

                        # إذا تمت عملية السحب بنجاح، قم بتسجيل عملية التصدير
                        if result == Qt.DropAction.CopyAction:
                            try:
                                # تسجيل عملية التصدير في قاعدة البيانات
                                # محاولة الوصول إلى قاعدة البيانات من خلال النافذة الرئيسية أو نافذة الحوار
                                parent_widget = self.window()

                                # البحث عن كائن قاعدة البيانات في المكان المناسب
                                db_obj = None

                                # محاولة الوصول إلى قاعدة البيانات من نافذة الحوار
                                for widget in QApplication.topLevelWidgets():
                                    if isinstance(widget, QDialog) and hasattr(widget, 'db'):
                                        db_obj = widget.db
                                        break

                                # إذا لم يتم العثور على قاعدة البيانات في نافذة الحوار، استخدم النافذة الرئيسية
                                if db_obj is None and hasattr(parent_widget, 'db'):
                                    db_obj = parent_widget.db

                                # استخدام قاعدة البيانات لتحديث تاريخ التصدير
                                if db_obj is not None:
                                    for doc_id in exported_doc_ids:
                                        db_obj.update_document_export_date(doc_id)
                                    print("تم تسجيل عملية التصدير بنجاح")
                            except Exception as e:
                                print(f"خطأ في تسجيل عملية التصدير: {str(e)}")
                except Exception as e:
                    print(f"خطأ في عملية السحب: {str(e)}")

        # إنشاء قائمة الملفات المخصصة
        custom_files_list = FileListWidget()
        custom_files_list.setAlternatingRowColors(True)
        custom_files_list.setIconSize(QSize(128, 128))  # زيادة حجم الأيقونات بشكل أكبر
        custom_files_list.setViewMode(QListView.ViewMode.IconMode)
        custom_files_list.setGridSize(QSize(180, 220))  # زيادة حجم الشبكة لاستيعاب الصور المصغرة
        custom_files_list.setResizeMode(QListView.ResizeMode.Adjust)
        custom_files_list.setWrapping(True)
        custom_files_list.setWordWrap(True)
        custom_files_list.setSpacing(20)  # زيادة المسافة بين العناصر
        custom_files_list.setDragEnabled(True)  # تمكين السحب
        custom_files_list.setTextElideMode(Qt.TextElideMode.ElideNone)  # عدم اختصار النص
        custom_files_list.setUniformItemSizes(False)  # السماح بأحجام مختلفة للعناصر
        custom_files_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 20px;
            }
            QListWidget::item {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #f5f5f5;
                padding: 10px;
                margin-bottom: 10px;
            }
            QListWidget::item:selected {
                background-color: #e8eaf6;
                border: 2px solid #3f51b5;
            }
            QListWidget::item:hover {
                background-color: #eeeeee;
                border: 1px solid #c5cae9;
            }
        """)
        custom_files_list.itemDoubleClicked.connect(open_file_from_list)

        # إنشاء جدول الملفات المخصص
        custom_files_table = FileTableWidget()
        custom_files_table.setColumnCount(6)
        custom_files_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ الإنشاء", "تاريخ التعديل", "تاريخ الاستيراد"])
        custom_files_table.horizontalHeader().setStretchLastSection(True)
        custom_files_table.setDragEnabled(True)  # تمكين السحب

        # تعيين وضع تمدد العمود الأول
        header = custom_files_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

        # ضبط عرض الأعمدة الأخرى
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # النوع
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الحجم
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive)  # تاريخ الإنشاء
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive)  # تاريخ التعديل
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Interactive)  # تاريخ الاستيراد

        # تعيين الحد الأدنى لعرض أعمدة التاريخ
        custom_files_table.setColumnWidth(3, 150)  # تاريخ الإنشاء
        custom_files_table.setColumnWidth(4, 150)  # تاريخ التعديل
        custom_files_table.setColumnWidth(5, 150)  # تاريخ الاستيراد

        # تمكين التفاف النص في الجدول
        custom_files_table.setWordWrap(True)
        custom_files_table.setTextElideMode(Qt.TextElideMode.ElideNone)

        # ضبط ارتفاع الصفوف لتناسب المحتوى
        custom_files_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        custom_files_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #c5cae9;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #3f51b5;
                color: white;
                padding: 8px;
                font-weight: bold;
                border: 1px solid #303f9f;
                white-space: normal;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f0f0f0;
                white-space: normal;
            }
            QTableWidget::item:selected {
                background-color: #e8eaf6;
                color: black;
            }
        """)
        custom_files_table.setVisible(False)  # مخفي افتراضيًا
        custom_files_table.itemDoubleClicked.connect(open_file_from_table)

        # إزالة القائمة والجدول القديمين وإضافة الجديدين
        # إزالة القائمة القديمة
        main_layout.removeWidget(files_list)
        files_list.hide()

        # إضافة القائمة الجديدة في نفس الموضع
        main_layout.insertWidget(main_layout.indexOf(files_table), custom_files_list)

        # إزالة الجدول القديم
        main_layout.removeWidget(files_table)
        files_table.hide()

        # إضافة الجدول الجديد في نفس الموضع
        main_layout.insertWidget(main_layout.indexOf(custom_files_list) + 1, custom_files_table)

        # حذف العناصر القديمة
        files_list.deleteLater()
        files_table.deleteLater()

        # استخدام القائمة والجدول الجديدين
        files_list = custom_files_list
        files_table = custom_files_table

        # تطبيق الفرز على الملفات المعروضة
        def apply_sorting():
            nonlocal current_category_id, file_info_cache, files_list, files_table

            if current_category_id is None:
                return

            # تحديد معيار الفرز
            sort_criteria = ""
            if sort_by_name_btn.isChecked():
                sort_criteria = "name"
            elif sort_by_date_btn.isChecked():
                sort_criteria = "date"
            elif sort_by_type_btn.isChecked():
                sort_criteria = "type"
            elif sort_by_size_btn.isChecked():
                sort_criteria = "size"

            # إذا لم يتم تحديد أي معيار، استخدم الاسم كمعيار افتراضي
            if not sort_criteria:
                sort_criteria = "name"

            # تحميل الملفات من قاعدة البيانات مع الفرز المناسب
            documents = []

            if current_category_id is None:
                # جميع المستندات
                documents = self.db.get_documents()
            else:
                # مستندات فئة محددة
                documents = self.db.get_documents(current_category_id)

            # فرز المستندات حسب المعيار المحدد
            if sort_criteria == "name":
                documents = sorted(documents, key=lambda x: x.get('title', '').lower())
            elif sort_criteria == "date":
                documents = sorted(documents, key=lambda x: x.get('import_date', ''), reverse=True)
            elif sort_criteria == "type":
                documents = sorted(documents, key=lambda x: x.get('file_type', '').lower())
            elif sort_criteria == "size":
                documents = sorted(documents, key=lambda x: x.get('file_size', 0), reverse=True)

            # تخزين معلومات الملفات
            file_info_cache = {doc['id']: doc for doc in documents}

            # عرض الملفات في القائمة
            files_list.clear()

            for doc in documents:
                item = QListWidgetItem(doc['title'])
                item.setData(Qt.ItemDataRole.UserRole, doc['id'])

                # إضافة صورة مصغرة للملف
                file_path = doc.get('file_path', '')
                file_type = doc.get('file_type', '')

                # إنشاء صورة مصغرة أكبر وأكثر وضوحًا
                icon = create_thumbnail(file_path, file_type)
                item.setIcon(icon)

                # إضافة معلومات إضافية
                file_size = format_size(doc.get('file_size', 0))
                import_date = doc.get('import_date', 'غير معروف')
                export_date = doc.get('export_date', 'لم يتم التصدير')

                # إضافة المزيد من المعلومات في التلميح - تعديل ليعرض فقط اسم الملف والوصف وتاريخ الاستيراد وتاريخ التصدير
                description = doc.get('description', '')
                item.setToolTip(f"الاسم: {doc['title']}\nالوصف: {description}\nتاريخ الاستيراد: {import_date}\nتاريخ التصدير: {export_date}")

                # تعيين نص إضافي تحت الأيقونة - سيظهر هذا النص تحت الأيقونة في وضع الأيقونات
                item.setText(f"{doc['title']}\n{file_type} - {file_size}")

                # تخزين معلومات إضافية للاستخدام لاحقًا
                item.setData(Qt.ItemDataRole.UserRole + 1, f"{file_type}\n{file_size}")

                files_list.addItem(item)

            # عرض الملفات في جدول التفاصيل إذا كان مرئيًا
            if files_table.isVisible():
                show_files_in_details_view()

            # تحديث عنوان القسم إذا كان هناك عنصر محدد
            selected_items = categories_tree.selectedItems()
            if selected_items:
                folder_name = selected_items[0].text(0)
                files_label.setText(f"محتويات المجلد: {folder_name} ({len(documents)} ملف)")

        # ربط أزرار الفرز بوظيفة الفرز
        sort_by_name_btn.clicked.connect(apply_sorting)
        sort_by_date_btn.clicked.connect(apply_sorting)
        sort_by_type_btn.clicked.connect(apply_sorting)
        sort_by_size_btn.clicked.connect(apply_sorting)

        # عرض الملفات عند النقر على زر عرض محتويات المجلد
        def show_folder_files():
            nonlocal current_category_id, file_info_cache, files_list, files_table

            selected_items = categories_tree.selectedItems()
            if not selected_items:
                QMessageBox.warning(dialog, "تنبيه", "الرجاء اختيار مجلد أولاً")
                return

            selected_item = selected_items[0]
            category_id = selected_item.data(0, Qt.ItemDataRole.UserRole)
            current_category_id = category_id

            # تطبيق الفرز الحالي
            apply_sorting()

        show_folder_btn.clicked.connect(show_folder_files)

        # إضافة زر إغلاق في الأسفل
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.accept)
        main_layout.addWidget(close_btn)

        dialog.exec()
