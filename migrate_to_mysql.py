# -*- coding: utf-8 -*-
import os
import sqlite3
import mysql.connector
from db_config import DB_CONFIG

# المسار إلى قاعدة بيانات SQLite القديمة
APP_DATA_DIR = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'أرشفة')
SQLITE_DB_PATH = os.path.join(APP_DATA_DIR, "archive.db")

def migrate_data():
    """نقل البيانات من SQLite إلى MySQL"""
    print("بدء عملية نقل البيانات...")

    # 1. الاتصال بقاعدة بيانات SQLite
    try:
        sqlite_conn = sqlite3.connect(SQLITE_DB_PATH)
        sqlite_conn.row_factory = sqlite3.Row
        sqlite_cursor = sqlite_conn.cursor()
        print("تم الاتصال بقاعدة بيانات SQLite بنجاح.")
    except sqlite3.Error as e:
        print(f"خطأ في الاتصال بـ SQLite: {e}")
        return

    # 2. الاتصال بقاعدة بيانات MySQL
    try:
        mysql_conn = mysql.connector.connect(**DB_CONFIG)
        mysql_cursor = mysql_conn.cursor()
        print("تم الاتصال بقاعدة بيانات MySQL بنجاح.")
    except mysql.connector.Error as e:
        print(f"خطأ في الاتصال بـ MySQL: {e}")
        print("يرجى التأكد من صحة البيانات في ملف db_config.py وأن السيرفر يعمل.")
        sqlite_conn.close()
        return

    # 3. قائمة الجداول التي سيتم نقلها بالترتيب
    # (categories أولاً بسبب العلاقات الخارجية)
    tables_to_migrate = ['categories', 'documents', 'document_categories', 'custom_metadata']

    for table_name in tables_to_migrate:
        print(f"\n--- بدء نقل جدول: {table_name} ---")
        
        # قراءة البيانات من SQLite
        sqlite_cursor.execute(f"SELECT * FROM {table_name}")
        rows = sqlite_cursor.fetchall()
        
        if not rows:
            print(f"جدول {table_name} فارغ، تم التجاوز.")
            continue

        # تحضير استعلام الإدخال في MySQL
        columns = [desc[0] for desc in sqlite_cursor.description]
        placeholders = ', '.join(['%s'] * len(columns))
        # استخدام backticks للأسماء لتجنب التعارض مع الكلمات المحجوزة
        column_names = '`, `'.join(columns)
        insert_query = f"INSERT INTO `{table_name}` (`{column_names}`) VALUES ({placeholders})"
        
        # تحويل البيانات وإدخالها
        data_to_insert = []
        for row in rows:
            # تحويل الصف من sqlite3.Row إلى tuple
            data_to_insert.append(tuple(row))

        try:
            mysql_cursor.executemany(insert_query, data_to_insert)
            mysql_conn.commit()
            print(f"تم نقل {mysql_cursor.rowcount} سجل بنجاح إلى جدول {table_name}.")
        except mysql.connector.Error as e:
            print(f"حدث خطأ أثناء إدخال البيانات في جدول {table_name}: {e}")
            mysql_conn.rollback()

    # إغلاق الاتصالات
    sqlite_conn.close()
    mysql_conn.close()
    print("\nتم الانتهاء من عملية نقل البيانات.")

if __name__ == '__main__':
    # قبل تشغيل هذا الملف، تأكد من:
    # 1. أن سيرفر MySQL يعمل.
    # 2. أنك قمت بتعديل ملف db_config.py بمعلومات الاتصال الصحيحة.
    # 3. أن قاعدة البيانات الهدف في MySQL فارغة لتجنب تكرار البيانات.
    migrate_data()
