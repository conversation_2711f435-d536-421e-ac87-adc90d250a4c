import os
from datetime import datetime
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QTextEdit, QPushButton,
                             QFileDialog, QMessageBox, QComboBox, QFrame)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon, QFont


class ImportDialog(QDialog):
    """نافذة استيراد الملفات"""

    def __init__(self, parent, database, file_handler):
        """تهيئة نافذة الاستيراد"""
        super().__init__(parent)

        self.db = database
        self.file_handler = file_handler
        self.file_path = ""
        self.file_info = {}

        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("استيراد ملف")
        self.setMinimumWidth(650)
        self.setMinimumHeight(500)  # تصغير الارتفاع
        self.setMaximumHeight(550)  # تحديد الحد الأقصى للارتفاع
        self.setWindowIcon(QIcon("resources/import.png"))
        self.setFont(QFont("Arial", 11))  # تكبير الخط

        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel("استيراد ملف جديد")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #3f51b5; margin-bottom: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # اختيار الملف
        file_selection_frame = QFrame()
        file_selection_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border: 1px solid #c5cae9;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        file_selection_layout = QVBoxLayout(file_selection_frame)

        file_selection_label = QLabel("اختيار الملف")
        file_selection_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #3f51b5; margin-bottom: 10px;")
        file_selection_layout.addWidget(file_selection_label)

        file_path_layout = QHBoxLayout()

        self.file_path_input = QLineEdit()
        self.file_path_input.setReadOnly(True)
        self.file_path_input.setPlaceholderText("مسار الملف...")
        self.file_path_input.setStyleSheet("padding: 8px; background-color: white;")
        file_path_layout.addWidget(self.file_path_input)

        browse_btn = QPushButton(QIcon("resources/folder.png"), "استعراض...")
        browse_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                font-weight: bold;
            }
        """)
        browse_btn.clicked.connect(self.browse_file)
        file_path_layout.addWidget(browse_btn)

        file_selection_layout.addLayout(file_path_layout)

        # إضافة نص توضيحي
        hint_label = QLabel("اختر ملفاً من جهازك لاستيراده إلى الأرشيف")
        hint_label.setStyleSheet("color: #666; font-style: italic; margin-top: 5px;")
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        file_selection_layout.addWidget(hint_label)

        layout.addWidget(file_selection_frame)

        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: #c5cae9; margin: 10px 0;")
        layout.addWidget(separator)

        # نموذج البيانات - تقسيم إلى قسمين أفقيين
        metadata_layout = QHBoxLayout()
        layout.addLayout(metadata_layout)

        # القسم الأيمن: العنوان والوصف
        right_metadata_frame = QFrame()
        right_metadata_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #c5cae9;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        right_metadata_layout = QVBoxLayout(right_metadata_frame)

        right_metadata_title = QLabel("بيانات المستند")
        right_metadata_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #3f51b5;")
        right_metadata_layout.addWidget(right_metadata_title)

        right_form_layout = QFormLayout()
        right_form_layout.setVerticalSpacing(8)
        right_form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)

        # تحسين شكل العناوين
        label_style = "font-weight: bold; color: #3f51b5;"

        # العنوان
        title_label = QLabel("العنوان:")
        title_label.setStyleSheet(label_style)
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("أدخل عنوان المستند...")
        right_form_layout.addRow(title_label, self.title_input)

        # الوصف
        desc_label = QLabel("الوصف:")
        desc_label.setStyleSheet(label_style)
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("أدخل وصف المستند...")
        self.description_input.setMaximumHeight(60)  # تقليل الارتفاع
        right_form_layout.addRow(desc_label, self.description_input)

        right_metadata_layout.addLayout(right_form_layout)
        metadata_layout.addWidget(right_metadata_frame)

        # القسم الأيسر: الكلمات المفتاحية والتصنيف
        left_metadata_frame = QFrame()
        left_metadata_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #c5cae9;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        left_metadata_layout = QVBoxLayout(left_metadata_frame)

        left_metadata_title = QLabel("تصنيف المستند")
        left_metadata_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #3f51b5;")
        left_metadata_layout.addWidget(left_metadata_title)

        left_form_layout = QFormLayout()
        left_form_layout.setVerticalSpacing(8)
        left_form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)

        # الكلمات المفتاحية
        keywords_label = QLabel("الكلمات المفتاحية:")
        keywords_label.setStyleSheet(label_style)
        self.keywords_input = QLineEdit()
        self.keywords_input.setPlaceholderText("كلمات مفتاحية مفصولة بفواصل")
        left_form_layout.addRow(keywords_label, self.keywords_input)

        # اختيار التصنيف
        category_label = QLabel("التصنيف:")
        category_label.setStyleSheet(label_style)
        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("padding: 5px;")
        self.category_combo.addItem("بدون تصنيف", None)

        # إضافة التصنيفات بما في ذلك التصنيفات الفرعية
        categories = self.db.get_all_categories()
        for category in categories:
            # إذا كان التصنيف فرعياً، أضف اسم التصنيف الرئيسي قبل اسمه
            if 'parent_name' in category and category['parent_name']:
                display_name = f"{category['parent_name']} / {category['name']}"
            else:
                display_name = category['name']

            self.category_combo.addItem(display_name, category['id'])

        left_form_layout.addRow(category_label, self.category_combo)

        left_metadata_layout.addLayout(left_form_layout)
        metadata_layout.addWidget(left_metadata_frame)

        # معلومات الملف وأزرار التحكم في صف واحد
        bottom_layout = QHBoxLayout()
        layout.addLayout(bottom_layout)

        # معلومات الملف
        file_info_frame = QFrame()
        file_info_frame.setStyleSheet("""
            QFrame {
                background-color: #e8eaf6;
                border: 1px solid #c5cae9;
                border-radius: 8px;
                padding: 8px;
            }
        """)
        file_info_layout = QVBoxLayout(file_info_frame)
        file_info_layout.setContentsMargins(8, 8, 8, 8)
        file_info_layout.setSpacing(5)

        file_info_title = QLabel("معلومات الملف")
        file_info_title.setStyleSheet("font-weight: bold; font-size: 12px; color: #3f51b5;")
        file_info_layout.addWidget(file_info_title)

        self.file_info_label = QLabel("لم يتم اختيار ملف بعد")
        self.file_info_label.setStyleSheet("padding: 5px; font-size: 11px;")
        self.file_info_label.setWordWrap(True)
        self.file_info_label.setMaximumHeight(60)  # تحديد الارتفاع الأقصى
        file_info_layout.addWidget(self.file_info_label)

        bottom_layout.addWidget(file_info_frame)

        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                border: none;
                padding: 5px;
            }
        """)
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.import_btn = QPushButton(QIcon("resources/import.png"), "استيراد")
        self.import_btn.setToolTip("استيراد الملف")
        self.import_btn.clicked.connect(self.import_file)
        self.import_btn.setEnabled(False)
        self.import_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 20px;
                font-weight: bold;
                min-width: 120px;
            }
        """)
        buttons_layout.addWidget(self.import_btn)

        cancel_btn = QPushButton(QIcon("resources/cancel.png"), "إلغاء")
        cancel_btn.setToolTip("إلغاء الاستيراد")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 20px;
                min-width: 120px;
            }
        """)
        buttons_layout.addWidget(cancel_btn)

        bottom_layout.addWidget(buttons_frame)

# تم إزالة دالة on_file_dropped لأننا أزلنا خاصية السحب والإفلات من نافذة الاستيراد

    def browse_file(self):
        """اختيار ملف للاستيراد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملفاً للاستيراد",
            "",
            "جميع الملفات (*.*)"
        )

        if not file_path:
            return

        self.process_file(file_path)

    def process_file(self, file_path):
        """معالجة الملف المختار"""
        self.file_path = file_path
        self.file_path_input.setText(file_path)

        # استخراج معلومات الملف
        try:
            self.file_info = self.file_handler.get_file_info(file_path)

            # عرض معلومات الملف
            file_name = os.path.basename(file_path)
            file_size = self.file_info['file_size']

            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.2f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"

            file_type = self.file_info['file_type']
            creation_date = self.file_info.get('creation_date', '')
            modification_date = self.file_info.get('modification_date', '')

            info_text = f"""
            <b>اسم الملف:</b> {file_name}
            <b>الحجم:</b> {size_str}
            <b>النوع:</b> {file_type}
            <b>تاريخ الإنشاء:</b> {creation_date}
            <b>تاريخ التعديل:</b> {modification_date}
            """
            self.file_info_label.setText(info_text)

            # تعيين العنوان تلقائياً
            self.title_input.setText(os.path.splitext(file_name)[0])

            # تفعيل زر الاستيراد
            self.import_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في قراءة معلومات الملف: {str(e)}")
            self.file_info = {}
            self.import_btn.setEnabled(False)

    def import_file(self):
        """استيراد الملف مباشرة إلى التصنيف المحدد"""
        if not self.file_path or not os.path.exists(self.file_path):
            QMessageBox.warning(self, "خطأ", "الرجاء اختيار ملف صالح")
            return

        title = self.title_input.text().strip()
        if not title:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال عنوان للملف")
            return

        description = self.description_input.toPlainText().strip()
        keywords = self.keywords_input.text().strip()
        category_id = self.category_combo.currentData()

        try:
            # التحقق من توافق نوع الملف مع التصنيف إذا تم اختيار تصنيف
            if category_id:
                category = self.db.get_category(category_id)
                if category:
                    allowed_file_types = category.get('allowed_file_types')

                    if allowed_file_types and self.file_info['file_type']:
                        allowed_types_list = [t.strip() for t in allowed_file_types.split(',')]
                        if self.file_info['file_type'] not in allowed_types_list:
                            QMessageBox.warning(self, "خطأ في الاستيراد",
                                                f"نوع الملف '{self.file_info['file_type']}' غير مسموح به في هذا التصنيف.\n"
                                                f"الأنواع المسموح بها: {allowed_file_types}")
                            return

                    # استيراد الملف إلى نظام التخزين
                    file_info = self.file_handler.import_file(self.file_path)

                    # إضافة المستند إلى قاعدة البيانات
                    document_id = self.db.add_document(
                        title=title,
                        description=description,
                        file_path=file_info['file_path'],
                        file_type=file_info['file_type'],
                        file_size=file_info['file_size'],
                        original_filename=file_info['original_filename'],
                        creation_date=file_info['creation_date'],
                        modification_date=file_info['modification_date'],
                        keywords=keywords
                    )

                    # تعيين التصنيف
                    self.db.assign_category_to_document(document_id, category_id)

                    # إضافة البيانات الوصفية المستخرجة من الملف
                    if 'metadata' in file_info:
                        for key, value in file_info['metadata'].items():
                            if key and value:
                                self.db.add_custom_metadata(document_id, key, value)

                    QMessageBox.information(self, "نجاح", "تم استيراد الملف بنجاح")
                    self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في استيراد الملف: {str(e)}")
