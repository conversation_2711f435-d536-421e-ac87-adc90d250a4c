@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ===== Starting Build Process =====
echo.

:: Set paths
set "PYTHON=python"
set "PYINSTALLER=pyinstaller"
set "APP_NAME=Archiver"
set "APP_VERSION=1.0"
set "MAIN_SCRIPT=main.py"
set "SPEC_FILE=archiver.spec"
set "ISS_FILE=installer_fixed.iss"
set "OUTPUT_DIR=Output"

:: Clean previous builds
echo ===== Cleaning Previous Builds =====
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "%OUTPUT_DIR%" (
    rmdir /s /q "%OUTPUT_DIR%"
    mkdir "%OUTPUT_DIR%"
)

:: Build executable
echo ===== Building Executable =====
%PYTHON% -m PyInstaller --clean --noconfirm --windowed --icon=resources/logo.png.ico --name "%APP_NAME%" "%MAIN_SCRIPT%"

if %ERRORLEVEL% NEQ 0 (
    echo ! ERROR: Failed to build executable.
    pause
    exit /b 1
)

:: Check Inno Setup
echo ===== Checking Inno Setup =====
set "INNO_PATH="

if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
) else (
    echo ! ERROR: Inno Setup is not installed.
    echo Please download and install Inno Setup from:
    echo https://jrsoftware.org/isdl.php
    pause
    exit /b 1
)

echo Found Inno Setup at: %INNO_PATH%

:: Build installer
echo ===== Building Installer =====
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
"%INNO_PATH%" /q "%ISS_FILE%"

if %ERRORLEVEL% NEQ 0 (
    echo ! ERROR: Failed to build installer.
    pause
    exit /b 1
)

echo ===== Build Completed Successfully =====
echo.
echo Opening output folder...
start "" "%OUTPUT_DIR%"

pause
