from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QMessageBox, QWidget,
                             QRadioButton, QButtonGroup)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont


class LicenseDialog(QDialog):
    """نافذة إدخال مفتاح الترخيص أو بدء الفترة التجريبية"""

    def __init__(self, license_manager, parent=None):
        super().__init__(parent)
        self.license_manager = license_manager
        self.setWindowTitle("تنشيط البرنامج")
        self.setMinimumWidth(550)
        self.setMinimumHeight(300)
        self.setModal(True)

        # رسالة ترحيب كبيرة
        welcome_label = QLabel("مرحباً بك في برنامج الأرشفة")
        welcome_font = QFont()
        welcome_font.setPointSize(16)
        welcome_font.setBold(True)
        welcome_label.setFont(welcome_font)
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # شرح قصير
        description_label = QLabel("يمكنك تنشيط البرنامج باستخدام مفتاح تسلسلي أو بدء الفترة التجريبية لمدة 10 أيام")
        description_label.setWordWrap(True)
        description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # إنشاء خيارات التنشيط
        options_layout = QVBoxLayout()

        # خيار المفتاح التسلسلي
        self.license_radio = QRadioButton("تنشيط باستخدام مفتاح تسلسلي")
        self.license_radio.setChecked(True)

        # خيار الفترة التجريبية
        self.trial_radio = QRadioButton("بدء الفترة التجريبية (10 أيام)")

        # التحقق من إمكانية استخدام الفترة التجريبية
        if self.license_manager.has_trial_been_used():
            self.trial_radio.setText("الفترة التجريبية (تم استخدامها من قبل)")
            self.trial_radio.setEnabled(False)
            self.trial_radio.setToolTip("لقد تم استخدام الفترة التجريبية من قبل على هذا الجهاز")
        elif self.license_manager.is_trial_valid():
            trial_info = self.license_manager.get_trial_info()
            days_left = trial_info.get("days_left", 0)
            self.trial_radio.setText(f"الفترة التجريبية (متبقي {days_left} يوم)")
            self.trial_radio.setEnabled(False)
            self.trial_radio.setToolTip("هناك فترة تجريبية نشطة بالفعل")

        # إضافة الخيارات إلى مجموعة أزرار لاختيار واحد فقط
        self.option_group = QButtonGroup()
        self.option_group.addButton(self.license_radio)
        self.option_group.addButton(self.trial_radio)

        options_layout.addWidget(self.license_radio)
        options_layout.addWidget(self.trial_radio)

        # مكون إدخال المفتاح
        self.key_widget = QWidget()
        key_layout = QVBoxLayout()

        key_label = QLabel("أدخل المفتاح التسلسلي:")
        self.key_edit = QLineEdit()
        self.key_edit.setPlaceholderText("مثال: ARC-123-456-ABC")
        self.key_edit.setMinimumHeight(30)

        # تنظيم حقل الإدخال بتنسيق XXX-XXX-XXX-XXX
        self.key_edit.textChanged.connect(self._format_key_input)

        key_layout.addWidget(key_label)
        key_layout.addWidget(self.key_edit)
        self.key_widget.setLayout(key_layout)

        # ربط تبديل عناصر الواجهة بناءً على الخيار المحدد
        self.license_radio.toggled.connect(self._toggle_key_input)

        # أزرار التأكيد والإلغاء
        self.activate_button = QPushButton("تنشيط")
        self.activate_button.clicked.connect(self._activate)

        self.exit_button = QPushButton("إلغاء")
        self.exit_button.clicked.connect(self.reject)

        # تنسيق أزرار أسفل النافذة
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.activate_button)
        button_layout.addWidget(self.exit_button)

        # تنسيق كامل النافذة
        layout = QVBoxLayout()
        layout.addWidget(welcome_label)
        layout.addSpacing(10)
        layout.addWidget(description_label)
        layout.addSpacing(20)
        layout.addLayout(options_layout)
        layout.addSpacing(20)
        layout.addWidget(self.key_widget)
        layout.addSpacing(20)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def _toggle_key_input(self, checked):
        """تبديل عرض حقل إدخال المفتاح بناءً على الخيار المحدد"""
        self.key_widget.setVisible(checked)

    def _format_key_input(self, text):
        """تنسيق إدخال المفتاح تلقائياً بتنسيق XXX-XXX-XXX-XXX"""
        # إزالة الواصلات الموجودة
        text = text.replace('-', '')

        # لا تسمح بأكثر من 12 حرف
        if len(text) > 12:
            text = text[:12]

        # إضافة الواصلات في المواقع الصحيحة
        formatted_text = ""
        for i, char in enumerate(text):
            if i > 0 and i % 3 == 0 and i < 12:
                formatted_text += '-'
            formatted_text += char

        # تحديث النص إذا تم تغييره
        if formatted_text != self.key_edit.text():
            self.key_edit.setText(formatted_text)
            # وضع المؤشر في نهاية النص
            self.key_edit.setCursorPosition(len(formatted_text))

    def _activate(self):
        """معالجة التنشيط (بالمفتاح أو الفترة التجريبية)"""
        if self.license_radio.isChecked():
            self._validate_license()
        else:
            self._start_trial()

    def _validate_license(self):
        """التحقق من صحة مفتاح الترخيص المدخل"""
        license_key = self.key_edit.text()

        if not license_key:
            QMessageBox.warning(
                self,
                "مفتاح فارغ",
                "يرجى إدخال المفتاح التسلسلي أولاً."
            )
            return

        if self.license_manager.validate_key(license_key):
            # حفظ المفتاح إذا كان صحيحاً
            if self.license_manager.save_license(license_key):
                QMessageBox.information(
                    self,
                    "تم التنشيط بنجاح",
                    "تم تنشيط البرنامج بنجاح! شكراً لاستخدامك برنامج الأرشفة."
                )
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    "خطأ في الحفظ",
                    "المفتاح صحيح ولكن حدث خطأ أثناء حفظ معلومات الترخيص. يرجى المحاولة مرة أخرى."
                )
        else:
            QMessageBox.critical(
                self,
                "مفتاح غير صحيح",
                "الرقم التسلسلي الذي أدخلته غير صحيح. يرجى التحقق من المفتاح وإعادة المحاولة."
            )

    def _start_trial(self):
        """بدء الفترة التجريبية"""
        # التحقق من الأسباب المختلفة لعدم إمكانية بدء الفترة التجريبية
        if self.license_manager.is_licensed_permanent():
            QMessageBox.warning(
                self,
                "ترخيص مفعل بالفعل",
                "البرنامج مرخص بالفعل بمفتاح دائم. لا حاجة للفترة التجريبية."
            )
            return

        if self.license_manager.is_trial_valid():
            trial_info = self.license_manager.get_trial_info()
            days_left = trial_info.get("days_left", 0)
            QMessageBox.warning(
                self,
                "فترة تجريبية نشطة",
                f"هناك فترة تجريبية نشطة بالفعل.\nالأيام المتبقية: {days_left} يوم"
            )
            return

        if self.license_manager.has_trial_been_used():
            QMessageBox.critical(
                self,
                "تم استخدام الفترة التجريبية",
                "لقد تم استخدام الفترة التجريبية من قبل على هذا الجهاز.\n"
                "لا يمكن الحصول على فترة تجريبية أخرى.\n"
                "يرجى شراء ترخيص للاستمرار في استخدام البرنامج."
            )
            return

        # محاولة بدء الفترة التجريبية
        if self.license_manager.start_trial():
            trial_info = self.license_manager.get_trial_info()
            end_date = trial_info.get("end_date", "")

            QMessageBox.information(
                self,
                "تم بدء الفترة التجريبية",
                f"تم تنشيط الفترة التجريبية بنجاح!\n"
                f"الفترة التجريبية ستنتهي في: {end_date}\n"
                f"استمتع باستخدام برنامج الأرشفة.\n\n"
                f"ملاحظة: يمكن استخدام الفترة التجريبية مرة واحدة فقط على كل جهاز."
            )
            self.accept()
        else:
            QMessageBox.warning(
                self,
                "خطأ في بدء الفترة التجريبية",
                "حدث خطأ غير متوقع أثناء محاولة بدء الفترة التجريبية."
            )

    @classmethod
    def get_license(cls, license_manager, parent=None):
        """عرض نافذة الترخيص والحصول على نتيجة الإجراء"""
        dialog = cls(license_manager, parent)
        result = dialog.exec()
        return result == QDialog.DialogCode.Accepted
