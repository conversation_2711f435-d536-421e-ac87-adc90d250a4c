@echo off
echo ===== بدء عملية إنشاء حزمة التثبيت =====

echo.
echo 1. إنشاء مجلد الإخراج إذا لم يكن موجوداً
if not exist "Output" mkdir "Output"

echo.
echo 2. التحقق من وجود ملف EXE
if not exist "dist\أرشفة.exe" (
    echo ملف أرشفة.exe غير موجود في مجلد dist.
    echo سيتم محاولة البحث عن الملف...
    
    dir "dist" /b > temp_files.txt
    findstr /i ".exe" temp_files.txt > exe_files.txt
    set /p EXE_FILE=<exe_files.txt
    del temp_files.txt
    del exe_files.txt
    
    if "!EXE_FILE!"=="" (
        echo لم يتم العثور على أي ملف EXE في مجلد dist.
        echo يرجى تشغيل build_onefile.bat أولاً لإنشاء الملف التنفيذي.
        pause
        exit /b 1
    ) else (
        echo تم العثور على الملف التنفيذي: !EXE_FILE!
        set EXE_NAME=!EXE_FILE!
    )
) else (
    echo تم العثور على الملف التنفيذي: أرشفة.exe
    set EXE_NAME=أرشفة.exe
)

echo.
echo 3. إنشاء مجلد حزمة التثبيت
if exist "Output\أرشفة_تثبيت" rmdir /s /q "Output\أرشفة_تثبيت"
mkdir "Output\أرشفة_تثبيت"

echo.
echo 4. نسخ الملفات اللازمة
copy "dist\%EXE_NAME%" "Output\أرشفة_تثبيت\أرشفة.exe" > nul
if exist "resources\logo.png.ico" copy "resources\logo.png.ico" "Output\أرشفة_تثبيت\logo.ico" > nul

echo.
echo 5. إنشاء ملف التثبيت
echo @echo off > "Output\أرشفة_تثبيت\setup.bat"
echo echo ===== تثبيت برنامج أرشفة ===== >> "Output\أرشفة_تثبيت\setup.bat"
echo echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo 1. إنشاء مجلد البرنامج >> "Output\أرشفة_تثبيت\setup.bat"
echo if not exist "%%ProgramFiles%%\أرشفة" mkdir "%%ProgramFiles%%\أرشفة" >> "Output\أرشفة_تثبيت\setup.bat"
echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo 2. نسخ ملفات البرنامج >> "Output\أرشفة_تثبيت\setup.bat"
echo copy "%%~dp0أرشفة.exe" "%%ProgramFiles%%\أرشفة\أرشفة.exe" >> "Output\أرشفة_تثبيت\setup.bat"
echo if exist "%%~dp0logo.ico" copy "%%~dp0logo.ico" "%%ProgramFiles%%\أرشفة\logo.ico" >> "Output\أرشفة_تثبيت\setup.bat"
echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo 3. إنشاء مجلد البيانات >> "Output\أرشفة_تثبيت\setup.bat"
echo if not exist "%%LOCALAPPDATA%%\أرشفة" mkdir "%%LOCALAPPDATA%%\أرشفة" >> "Output\أرشفة_تثبيت\setup.bat"
echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo 4. إنشاء اختصار على سطح المكتب >> "Output\أرشفة_تثبيت\setup.bat"
echo echo Set oWS = WScript.CreateObject^("WScript.Shell"^) ^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo sLinkFile = oWS.SpecialFolders^("Desktop"^) ^& "\أرشفة.lnk" ^>^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo Set oLink = oWS.CreateShortcut^(sLinkFile^) ^>^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.TargetPath = "%%ProgramFiles%%\أرشفة\أرشفة.exe" ^>^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.WorkingDirectory = "%%ProgramFiles%%\أرشفة" ^>^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.Description = "برنامج أرشفة المستندات" ^>^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo if exist "%%ProgramFiles%%\أرشفة\logo.ico" echo oLink.IconLocation = "%%ProgramFiles%%\أرشفة\logo.ico" ^>^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.Save ^>^> "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo cscript //nologo "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo del "%%TEMP%%\shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo 5. إنشاء اختصار في قائمة البرامج >> "Output\أرشفة_تثبيت\setup.bat"
echo if not exist "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة" mkdir "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo Set oWS = WScript.CreateObject^("WScript.Shell"^) ^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo sLinkFile = "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة\أرشفة.lnk" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo Set oLink = oWS.CreateShortcut^(sLinkFile^) ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.TargetPath = "%%ProgramFiles%%\أرشفة\أرشفة.exe" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.WorkingDirectory = "%%ProgramFiles%%\أرشفة" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.Description = "برنامج أرشفة المستندات" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo if exist "%%ProgramFiles%%\أرشفة\logo.ico" echo oLink.IconLocation = "%%ProgramFiles%%\أرشفة\logo.ico" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo oLink.Save ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo cscript //nologo "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo del "%%TEMP%%\menu_shortcut.vbs" >> "Output\أرشفة_تثبيت\setup.bat"
echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo 6. إنشاء ملف إلغاء التثبيت >> "Output\أرشفة_تثبيت\setup.bat"
echo echo @echo off ^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo echo ===== إلغاء تثبيت برنامج أرشفة ===== ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo echo. ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo echo 1. حذف اختصار سطح المكتب ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo del "%%USERPROFILE%%\Desktop\أرشفة.lnk" ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo. ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo echo 2. حذف اختصار قائمة البرامج ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo rmdir /s /q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة" ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo. ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo echo 3. حذف ملفات البرنامج ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo cd /d "%%~dp0.." ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo rmdir /s /q "%%ProgramFiles%%\أرشفة" ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo. ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo echo ===== تم إلغاء تثبيت البرنامج بنجاح ===== ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo echo. ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo echo pause ^>^> "%%ProgramFiles%%\أرشفة\uninstall.bat" >> "Output\أرشفة_تثبيت\setup.bat"
echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo ===== تم تثبيت البرنامج بنجاح ===== >> "Output\أرشفة_تثبيت\setup.bat"
echo echo يمكنك الآن تشغيل البرنامج من سطح المكتب أو من قائمة البرامج. >> "Output\أرشفة_تثبيت\setup.bat"
echo echo. >> "Output\أرشفة_تثبيت\setup.bat"
echo pause >> "Output\أرشفة_تثبيت\setup.bat"

echo.
echo 6. إنشاء ملف readme
echo # برنامج أرشفة > "Output\أرشفة_تثبيت\readme.txt"
echo. >> "Output\أرشفة_تثبيت\readme.txt"
echo ## تعليمات التثبيت >> "Output\أرشفة_تثبيت\readme.txt"
echo. >> "Output\أرشفة_تثبيت\readme.txt"
echo 1. قم بتشغيل ملف setup.bat لتثبيت البرنامج >> "Output\أرشفة_تثبيت\readme.txt"
echo 2. سيتم إنشاء اختصار على سطح المكتب وفي قائمة البرامج >> "Output\أرشفة_تثبيت\readme.txt"
echo 3. لإلغاء تثبيت البرنامج، قم بتشغيل ملف uninstall.bat الموجود في مجلد التثبيت >> "Output\أرشفة_تثبيت\readme.txt"
echo    (C:\Program Files\أرشفة\uninstall.bat) >> "Output\أرشفة_تثبيت\readme.txt"

echo.
echo ===== تم إنشاء حزمة التثبيت بنجاح =====
echo يمكنك العثور على حزمة التثبيت في: %CD%\Output\أرشفة_تثبيت
echo قم بنسخ المجلد بأكمله إلى الجهاز المستهدف وتشغيل ملف setup.bat للتثبيت.
echo.

pause
