@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ===== Starting Build Process =====
echo.

:: Set paths
set "PYTHON=python"
set "PIP=pip"
set "PYINSTALLER=pyinstaller"
set "SCRIPT_DIR=%~dp0"
set "OUTPUT_DIR=%SCRIPT_DIR%Output"
set "DIST_DIR=%SCRIPT_DIR%dist"
set "BUILD_DIR=%SCRIPT_DIR%build"
set "APP_NAME=أرشيف الدائرة البشرية"
set "APP_VERSION=1.0"
set "INSTALLER_NAME=%APP_NAME%_%APP_VERSION%_Setup"
set "PYTHON_SCRIPT=main.py"
set "SPEC_FILE=archiver.spec"
set "ISS_FILE=installer_fixed.iss"
set "REQUIREMENTS=requirements.txt"

:: Clean previous builds
echo.
echo ===== Cleaning Previous Builds =====
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
if exist "%OUTPUT_DIR%" (
    del /q "%OUTPUT_DIR%\*.*" >nul 2>&1
    rmdir /s /q "%OUTPUT_DIR%"
)

:: Install requirements
echo.
echo ===== Installing Requirements =====
%PIP% install --upgrade pip
if exist "%REQUIREMENTS%" (
    %PIP% install -r "%REQUIREMENTS%"
) else (
    echo ! WARNING: requirements.txt not found. Installing default requirements.
    %PIP% install pyinstaller PyQt6 reportlab pandas openpyxl arabic-reshaper python-bidi
)

:: Build executable
echo.
echo ===== Building Executable =====
if exist "%SPEC_FILE%" (
    %PYINSTALLER% --clean --noconfirm "%SPEC_FILE%"
) else (
    %PYINSTALLER% --noconfirm --onefile --windowed --icon=resources/logo.png.ico --name "%APP_NAME%" "%PYTHON_SCRIPT%"
)

if %ERRORLEVEL% NEQ 0 (
    echo ! ERROR: Failed to build executable.
    pause
    exit /b 1
)

:: Check Inno Setup
echo.
echo ===== Checking Inno Setup =====
set "INNO_PATH="

if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
) else (
    echo ! ERROR: Inno Setup is not installed.
    echo Please download and install Inno Setup from:
    echo https://jrsoftware.org/isdl.php
    start "" "https://jrsoftware.org/isdl.php"
    pause
    exit /b 1
)

echo Found Inno Setup at: %INNO_PATH%

:: Build installer
echo.
echo ===== Building Installer =====
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
"%INNO_PATH%" "/q" "/O%OUTPUT_DIR%" "/F%INSTALLER_NAME%" "%ISS_FILE%"

if %ERRORLEVEL% NEQ 0 (
    echo ! ERROR: Failed to build installer.
    pause
    exit /b 1
)

:: Verify installer
echo.
echo ===== Verifying Installer =====
set "INSTALLER_PATH=%OUTPUT_DIR%\%INSTALLER_NAME%.exe"

if exist "%INSTALLER_PATH%" (
    echo.
    echo ===========================================
    echo ===== Installer Built Successfully! =====
    echo ===========================================
    echo.
    echo Installer Information:
    echo   - Name: %INSTALLER_NAME%.exe
    echo   - Path: %INSTALLER_PATH%
    echo   - Size: 
    for /f "tokens=1-3" %%a in ('dir /a-d /os /s "%INSTALLER_PATH%" ^| find "%INSTALLER_NAME%.exe"') do echo     %%a %%b %%c
    echo.
    
    echo - Opening output folder...
    start "" "%OUTPUT_DIR%"
) else (
    echo ! ERROR: Failed to create installer.
    pause
    exit /b 1
)

echo.
echo ===== Build Completed Successfully! =====
echo.

pause
