from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QComboBox,
                             QDateEdit, QGroupBox, QCheckBox, QTableWidget,
                             QTableWidgetItem, QHeaderView)
from PyQt6.QtCore import Qt, QDate

class SearchPanel(QDialog):
    """لوحة البحث المتقدم"""

    def __init__(self, parent, search_engine, database):
        """تهيئة لوحة البحث"""
        super().__init__(parent)

        self.search_engine = search_engine
        self.db = database
        self.search_results = []

        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("بحث متقدم")
        self.setMinimumSize(800, 600)

        layout = QVBoxLayout(self)

        # معايير البحث
        criteria_group = QGroupBox("معايير البحث")
        criteria_layout = QFormLayout(criteria_group)

        self.text_input = QLineEdit()
        criteria_layout.addRow("نص البحث:", self.text_input)

        # التصنيف
        self.category_combo = QComboBox()
        self.category_combo.addItem("جميع التصنيفات", None)

        # إضافة التصنيفات (جميع التصنيفات بما في ذلك الفرعية)
        categories = self.db.get_all_categories()
        for category in categories:
            # تنسيق اسم التصنيف لعرض التسلسل الهرمي
            if category.get('parent_name'):
                display_name = f"{category['parent_name']} ← {category['name']}"
            else:
                display_name = category['name']
            self.category_combo.addItem(display_name, category['id'])

        criteria_layout.addRow("التصنيف:", self.category_combo)

        # نوع الملف
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItem("جميع الأنواع", None)
        self.file_type_combo.addItem("مستند", "document")
        self.file_type_combo.addItem("صورة", "image")
        self.file_type_combo.addItem("فيديو", "video")
        self.file_type_combo.addItem("صوت", "audio")
        self.file_type_combo.addItem("أخرى", "other")

        criteria_layout.addRow("نوع الملف:", self.file_type_combo)

        # التاريخ
        date_layout = QHBoxLayout()

        self.use_date_range = QCheckBox("تحديد نطاق تاريخ")
        date_layout.addWidget(self.use_date_range)

        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_from.setCalendarPopup(True)
        self.date_from.setEnabled(False)
        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.date_from)

        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        self.date_to.setEnabled(False)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.date_to)

        self.use_date_range.toggled.connect(self.toggle_date_range)

        criteria_layout.addRow("التاريخ:", date_layout)

        # الكلمات المفتاحية
        self.keywords_input = QLineEdit()
        self.keywords_input.setPlaceholderText("كلمات مفتاحية مفصولة بفواصل")
        criteria_layout.addRow("الكلمات المفتاحية:", self.keywords_input)

        layout.addWidget(criteria_group)

        # أزرار البحث
        search_buttons_layout = QHBoxLayout()

        clear_btn = QPushButton("مسح")
        clear_btn.clicked.connect(self.clear_search)
        search_buttons_layout.addWidget(clear_btn)

        search_btn = QPushButton("بحث")
        search_btn.clicked.connect(self.perform_search)
        search_buttons_layout.addWidget(search_btn)

        layout.addLayout(search_buttons_layout)

        # نتائج البحث
        results_group = QGroupBox("نتائج البحث")
        results_layout = QVBoxLayout(results_group)

        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["العنوان", "النوع", "الحجم", "تاريخ الاستيراد", "الكلمات المفتاحية"])
        self.results_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.results_table.doubleClicked.connect(self.on_result_double_clicked)

        results_layout.addWidget(self.results_table)

        layout.addWidget(results_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(close_btn)

        select_btn = QPushButton("اختيار")
        select_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(select_btn)

        layout.addLayout(buttons_layout)

    def toggle_date_range(self, checked):
        """تفعيل/تعطيل نطاق التاريخ"""
        self.date_from.setEnabled(checked)
        self.date_to.setEnabled(checked)

    def clear_search(self):
        """مسح معايير البحث"""
        self.text_input.clear()
        self.category_combo.setCurrentIndex(0)
        self.file_type_combo.setCurrentIndex(0)
        self.use_date_range.setChecked(False)
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_to.setDate(QDate.currentDate())
        self.keywords_input.clear()

        self.results_table.setRowCount(0)
        self.search_results = []

    def perform_search(self):
        """تنفيذ البحث"""
        # جمع معايير البحث
        search_text = self.text_input.text().strip()
        category_id = self.category_combo.currentData()
        file_type = self.file_type_combo.currentData()

        date_from = None
        date_to = None

        if self.use_date_range.isChecked():
            date_from = self.date_from.date().toString("yyyy-MM-dd")
            date_to = self.date_to.date().toString("yyyy-MM-dd")

        keywords = self.keywords_input.text().strip()
        keywords_list = [k.strip() for k in keywords.split(',') if k.strip()]

        # تنفيذ البحث
        if keywords_list:
            # البحث بالكلمات المفتاحية
            self.search_results = self.search_engine.search_by_keywords(
                keywords_list,
                operator="OR",
                category_id=category_id,
                file_type=file_type,
                date_from=date_from,
                date_to=date_to
            )
        else:
            # البحث العام
            self.search_results = self.search_engine.search(
                search_text,
                category_id=category_id,
                date_from=date_from,
                date_to=date_to,
                file_type=file_type
            )

        # عرض النتائج
        self.display_results()

    def display_results(self):
        """عرض نتائج البحث"""
        self.results_table.setRowCount(0)

        for i, doc in enumerate(self.search_results):
            self.results_table.insertRow(i)

            # العنوان
            title_item = QTableWidgetItem(doc['title'])
            title_item.setData(Qt.ItemDataRole.UserRole, doc['id'])
            self.results_table.setItem(i, 0, title_item)

            # النوع
            self.results_table.setItem(i, 1, QTableWidgetItem(doc['file_type'] or ""))

            # الحجم
            file_size = doc['file_size'] or 0
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.2f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"

            self.results_table.setItem(i, 2, QTableWidgetItem(size_str))

            # تاريخ الاستيراد
            self.results_table.setItem(i, 3, QTableWidgetItem(doc['import_date'] or ""))

            # الكلمات المفتاحية
            self.results_table.setItem(i, 4, QTableWidgetItem(doc['keywords'] or ""))

    def on_result_double_clicked(self, index):
        """معالج النقر المزدوج على نتيجة"""
        row = index.row()
        document_id = self.results_table.item(row, 0).data(Qt.ItemDataRole.UserRole)

        # عرض تفاصيل المستند في النافذة الرئيسية
        self.parent().show_document_details(document_id)
        self.accept()
