@echo off
echo ===== بدء عملية إنشاء ملف التثبيت باستخدام NSIS =====

echo.
echo 1. إنشاء مجلد الإخراج إذا لم يكن موجوداً
if not exist "Output" mkdir "Output"

echo.
echo 2. التحقق من وجود ملف EXE
dir "dist" /b > temp_files.txt
findstr /i ".exe" temp_files.txt > exe_files.txt
set /p EXE_FILE=<exe_files.txt
del temp_files.txt
del exe_files.txt

if "%EXE_FILE%"=="" (
    echo ملف EXE غير موجود. يرجى تشغيل build_onefile.bat أولاً لإنشاء الملف التنفيذي.
    pause
    exit /b 1
) else (
    echo تم العثور على الملف التنفيذي: %EXE_FILE%
)

echo.
echo 3. التحقق من تثبيت NSIS
set "NSIS_PATH=%ProgramFiles(x86)%\NSIS\makensis.exe"
if not exist "%NSIS_PATH%" (
    set "NSIS_PATH=%ProgramFiles%\NSIS\makensis.exe"
    if not exist "%NSIS_PATH%" (
        echo NSIS غير مثبت. سيتم تنزيله الآن...
        
        echo تنزيل NSIS...
        powershell -Command "& {Invoke-WebRequest -Uri 'https://sourceforge.net/projects/nsis/files/NSIS%203/3.08/nsis-3.08-setup.exe/download' -OutFile 'nsis-setup.exe'}"
        
        echo تثبيت NSIS...
        echo يرجى اتباع خطوات التثبيت في النافذة التي ستظهر.
        start /wait nsis-setup.exe
        
        echo التحقق من التثبيت...
        if exist "%ProgramFiles(x86)%\NSIS\makensis.exe" (
            set "NSIS_PATH=%ProgramFiles(x86)%\NSIS\makensis.exe"
        ) else if exist "%ProgramFiles%\NSIS\makensis.exe" (
            set "NSIS_PATH=%ProgramFiles%\NSIS\makensis.exe"
        ) else (
            echo فشل تثبيت NSIS. يرجى تثبيته يدوياً من الموقع الرسمي:
            echo https://nsis.sourceforge.io/Download
            pause
            exit /b 1
        )
        
        echo تم تثبيت NSIS بنجاح!
        del nsis-setup.exe
    )
)

echo.
echo 4. إنشاء ملف التثبيت باستخدام NSIS
echo جاري إنشاء ملف التثبيت...
"%NSIS_PATH%" /DEXE_FILE="%EXE_FILE%" installer.nsi

echo.
echo 5. التحقق من نجاح عملية البناء
if exist "Output\أرشفة_تثبيت.exe" (
    echo تم إنشاء ملف التثبيت بنجاح!
    echo يمكنك العثور على ملف التثبيت في: %CD%\Output\أرشفة_تثبيت.exe
) else (
    echo حدث خطأ أثناء إنشاء ملف التثبيت.
    echo يرجى التحقق من الأخطاء المذكورة أعلاه.
)

echo.
echo ===== اكتملت عملية إنشاء ملف التثبيت =====
echo.

pause
