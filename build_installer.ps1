# Build Script for Archiver Application
# Version: 1.0
# Date: 2024-05-15

# File Settings
$Script:ScriptDir = $PSScriptRoot
$OutputDir = Join-Path -Path $ScriptDir -ChildPath "Output"
$DistDir = Join-Path -Path $ScriptDir -ChildPath "dist"
$BuildDir = Join-Path -Path $ScriptDir -ChildPath "build"
$AppName = "Archiver"
$AppVersion = "1.0"
$InstallerName = "${AppName}_${AppVersion}_Setup"
$PythonScript = "main.py"
$SpecFile = "archiver.spec"
$IssFile = "installer_fixed.iss"
$RequirementsFile = "requirements.txt"

# وظائف مساعدة
function Write-Header {
    param([string]$Title)
    
    $windowWidth = $Host.UI.RawUI.WindowSize.Width
    $separator = '=' * $windowWidth
    
    Write-Host "`n$separator" -ForegroundColor Cyan
    Write-Host $Title -ForegroundColor Cyan
    Write-Host $separator`n -ForegroundColor Cyan
}

function Test-CommandExists {
    param([string]$command)
    
    try {
        $null = Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

function Get-FileSize {
    param([string]$path)
    
    $file = Get-Item -Path $path -ErrorAction SilentlyContinue
    if ($file) {
        $size = $file.Length
        
        $suffix = "Bytes", "KB", "MB", "GB", "TB"
        $i = 0
        
        while ($size -gt 1KB -and $i -lt $suffix.Length - 1) {
            $size = $size / 1KB
            $i++
        }
        
        return "{0:N2} {1}" -f $size, $suffix[$i]
    }
    
    return "N/A"
}

# التحقق من صلاحيات المدير
$isAdmin = ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

# بدء عملية البناء
Clear-Host
Write-Host "`n`n`n"
Write-Host "    Welcome to $AppName Installer Builder" -ForegroundColor Green
Write-Host "    Version: $AppVersion" -ForegroundColor Yellow
Write-Host "    Date: $(Get-Date -Format 'yyyy/MM/dd HH:mm:ss')`n" -ForegroundColor Gray

# التحقق من تثبيت Python
Write-Header "التحقق من تثبيت Python"

$pythonCmd = "python"
$pythonVersion = & $pythonCmd --version 2>&1

if ($LASTEXITCODE -ne 0) {
    Write-Host "! خطأ: Python غير مثبت أو غير مضاف إلى متغيرات النظام." -ForegroundColor Red
    Write-Host "  يرجى تثبيت Python 3.6 أو أحدث من الموقع الرسمي." -ForegroundColor Yellow
    
    $installPython = Read-Host "Do you want to open Python download page now? (Y/N)"
    if ($installPython -match "^[yY]$") {
        Start-Process "https://www.python.org/downloads/"
    }
    
    exit 1
}

# التحقق من إصدار Python
$pythonVer = [version]($pythonVersion -replace '^Python (\d+\.\d+)', '$1')
$minPythonVer = [version]"3.6"

if ($pythonVer -lt $minPythonVer) {
    Write-Host "! خطأ: يتطلب التطبيق Python 3.6 أو أحدث." -ForegroundColor Red
    Write-Host "  الإصدار المثبت: Python $pythonVer" -ForegroundColor Yellow
    exit 1
}

Write-Host "- تم العثور على Python $pythonVer" -ForegroundColor Green

# تثبيت المتطلبات
Write-Header "تثبيت المتطلبات"

$pipCmd = "pip"

# ترقية pip
Write-Host "- ترقية pip..." -ForegroundColor Cyan
& $pythonCmd -m pip install --upgrade pip

# تثبيت المتطلبات
if (Test-Path -Path $RequirementsFile) {
    Write-Host "- تثبيت المتطلبات من $RequirementsFile..." -ForegroundColor Cyan
    & $pipCmd install -r $RequirementsFile
} else {
    Write-Host "! تحذير: ملف المتطلبات غير موجود. سيتم تثبيت المتطلبات الافتراضية." -ForegroundColor Yellow
    & $pipCmd install pyinstaller PyQt6 reportlab pandas openpyxl arabic-reshaper python-bidi
}

# تثبيت PyInstaller إذا لم يكن مثبتًا
if (-not (Test-CommandExists "pyinstaller")) {
    Write-Host "- تثبيت PyInstaller..." -ForegroundColor Cyan
    & $pipCmd install pyinstaller
}

# تنظيف عمليات البناء السابقة
Write-Header "تنظيف عمليات البناء السابقة"

if (Test-Path -Path $DistDir) {
    Write-Host "- حذف مجلد التوزيعات السابقة..." -ForegroundColor Cyan
    Remove-Item -Path $DistDir -Recurse -Force -ErrorAction SilentlyContinue
}

if (Test-Path -Path $BuildDir) {
    Write-Host "- حذف مجلد البناء السابق..." -ForegroundColor Cyan
    Remove-Item -Path $BuildDir -Recurse -Force -ErrorAction SilentlyContinue
}

if (Test-Path -Path $OutputDir) {
    Write-Host "- تنظيف مجلد الإخراج..." -ForegroundColor Cyan
    Remove-Item -Path "$OutputDir\*" -Recurse -Force -ErrorAction SilentlyContinue
} else {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# بناء الملف التنفيذي
Write-Header "بناء الملف التنفيذي"

if (Test-Path -Path $SpecFile) {
    Write-Host "- بناء باستخدام ملف المواصفات $SpecFile..." -ForegroundColor Cyan
    & pyinstaller --clean --noconfirm $SpecFile
} else {
    Write-Host "- بناء بدون ملف مواصفات..." -ForegroundColor Cyan
    & pyinstaller --noconfirm --onefile --windowed --icon=resources/logo.png.ico --name "$AppName" "$PythonScript"
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "! خطأ: فشل بناء الملف التنفيذي." -ForegroundColor Red
    exit 1
}

# التحقق من تثبيت Inno Setup
Write-Header "التحقق من تثبيت Inno Setup"

$innoSetupPaths = @(
    "${env:ProgramFiles(x86)}\Inno Setup 6\ISCC.exe",
    "${env:ProgramFiles}\Inno Setup 6\ISCC.exe",
    "${env:ProgramFiles(x86)}\Inno Setup\ISCC.exe",
    "${env:ProgramFiles}\Inno Setup\ISCC.exe"
)

$innoSetup = $innoSetupPaths | Where-Object { Test-Path -Path $_ } | Select-Object -First 1

if (-not $innoSetup) {
    Write-Host "! خطأ: Inno Setup غير مثبت." -ForegroundColor Red
    Write-Host "  يرجى تثبيت Inno Setup من الموقع الرسمي:" -ForegroundColor Yellow
    Write-Host "  https://jrsoftware.org/isdl.php" -ForegroundColor Blue
    
    $installInno = Read-Host "Do you want to open Inno Setup download page now? (Y/N)"
    if ($installInno -match "^[yY]$") {
        Start-Process "https://jrsoftware.org/isdl.php"
    }
    
    exit 1
}

Write-Host "- تم العثور على Inno Setup في: $innoSetup" -ForegroundColor Green

# بناء حزمة التثبيت
Write-Header "بناء حزمة التثبيت"

Write-Host "- إنشاء حزمة التثبيت باستخدام Inno Setup..." -ForegroundColor Cyan
& "$innoSetup" "/q" "/O$OutputDir" "/F$InstallerName" "$IssFile"

if ($LASTEXITCODE -ne 0) {
    Write-Host "! خطأ: فشل بناء حزمة التثبيت." -ForegroundColor Red
    exit 1
}

# التحقق من حزمة التثبيت
$installerPath = Join-Path -Path $OutputDir -ChildPath "${InstallerName}.exe"

if (Test-Path -Path $installerPath) {
    $fileSize = Get-FileSize -Path $installerPath
    
    Write-Header "اكتمل بناء حزمة التثبيت بنجاح!"
    
    $installerInfo = @{
        "Package Name" = "${InstallerName}.exe"
        "Path" = $installerPath
        "Size" = $fileSize
        "Created Date" = (Get-Date -Format "yyyy/MM/dd HH:mm:ss")
    }
    
    $installerInfo.GetEnumerator() | ForEach-Object {
        Write-Host ("{0,-15}: {1}" -f $_.Key, $_.Value) -ForegroundColor Green
    }
    
    # فحص الفيروسات إذا كان المستخدم مدير
    if ($isAdmin) {
        Write-Host "`n- فحص الفيروسات..." -ForegroundColor Cyan
        $mpCmdRun = Join-Path -Path ${env:ProgramFiles} -ChildPath "Windows Defender\MpCmdRun.exe"
        
        if (Test-Path -Path $mpCmdRun) {
            & $mpCmdRun -Scan -ScanType 3 -File "$installerPath"
            Write-Host "اكتمل فحص الفيروسات." -ForegroundColor Green
        } else {
            Write-Host "! تحذير: تعذر العثور على أداة فحص الفيروسات." -ForegroundColor Yellow
        }
    }
    
    # فتح مجلد الإخراج
    $openFolder = Read-Host "`nDo you want to open output folder now? (Y/N)"
    if ($openFolder -match "^(نعم|ن|y|Y)$") {
        Start-Process "$OutputDir"
    }
    
    Write-Host "`nتم إنشاء حزمة التثبيت بنجاح!" -ForegroundColor Green
} else {
    Write-Host "! خطأ: فشل إنشاء حزمة التثبيت." -ForegroundColor Red
    exit 1
}

# نهاية البرنامج النصي
Write-Host "`n`n"
Write-Host "    Thank you for using $AppName Installer Builder" -ForegroundColor Green
Write-Host "    Completed at: $(Get-Date -Format 'yyyy/MM/dd HH:mm:ss')`n" -ForegroundColor Gray

# إبقاء نافذة PowerShell مفتوحة للقراءة
if ($Host.Name -eq "ConsoleHost") {
    Write-Host "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey('NoEcho,IncludeKeyDown')
}
