class Category:
    """نموذج التصنيف في نظام الأرشفة"""

    def __init__(self, id=None, name="", description="", parent_id=None, allowed_file_types=None):
        """تهيئة التصنيف"""
        self.id = id
        self.name = name
        self.description = description
        self.parent_id = parent_id
        self.allowed_file_types = allowed_file_types
        self.children = []

    @classmethod
    def from_dict(cls, data):
        """إنشاء كائن تصنيف من قاموس"""
        return cls(
            id=data.get('id'),
            name=data.get('name', ''),
            description=data.get('description', ''),
            parent_id=data.get('parent_id'),
            allowed_file_types=data.get('allowed_file_types')
        )

    def to_dict(self):
        """تحويل التصنيف إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'parent_id': self.parent_id,
            'allowed_file_types': self.allowed_file_types
        }

    def add_child(self, child):
        """إضافة تصنيف فرعي"""
        self.children.append(child)

    def __str__(self):
        """تمثيل التصنيف كنص"""
        return self.name
