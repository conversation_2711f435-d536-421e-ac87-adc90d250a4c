# نظام الأرشفة الرقمية

نظام متكامل لأرشفة وتنظيم وإدارة الملفات الرقمية مع إمكانيات بحث متقدمة.

## المميزات الرئيسية

- **استيراد الملفات**: إمكانية استيراد الملفات الرقمية من الكمبيوتر
- **البيانات الوصفية**: إضافة معلومات وصفية لكل مستند أو صورة
- **التصنيف**: تنظيم المستندات ضمن تصنيفات وفئات
- **قاعدة بيانات**: تخزين البيانات الوصفية ومعلومات الفهرسة
- **نظام ملفات محلي**: تخزين الملفات على جهاز الكمبيوتر المحلي
- **محرك بحث قوي**: بحث سريع ودقيق باستخدام البيانات الوصفية

## متطلبات النظام

- Python 3.6 أو أحدث
- PyQt6
- Pillow

## التثبيت

1. قم بتثبيت المتطلبات:

```
pip install -r requirements.txt
```

2. قم بتشغيل التطبيق:

```
python main.py
```

## كيفية الاستخدام

### استيراد ملف

1. انقر على زر "استيراد" في شريط الأدوات
2. اختر الملف من جهاز الكمبيوتر
3. أدخل العنوان والوصف والكلمات المفتاحية
4. اختر التصنيف المناسب
5. انقر على "استيراد"

### البحث عن ملفات

1. استخدم شريط البحث في الأعلى للبحث السريع
2. انقر على "بحث متقدم" لخيارات بحث أكثر تفصيلاً
3. يمكنك البحث حسب:
   - النص
   - التصنيف
   - نوع الملف
   - نطاق التاريخ
   - الكلمات المفتاحية

### إدارة التصنيفات

1. استخدم لوحة التصنيفات على اليسار لإضافة أو تعديل أو حذف التصنيفات
2. يمكن إنشاء تصنيفات فرعية ضمن التصنيفات الرئيسية

### تعديل المستندات

1. حدد المستند من القائمة
2. انقر على زر "تعديل"
3. قم بتحديث البيانات الوصفية أو التصنيفات
4. أضف حقول بيانات وصفية مخصصة حسب الحاجة

## هيكل المشروع

- `main.py`: نقطة الدخول الرئيسية للتطبيق
- `database.py`: إدارة قاعدة البيانات
- `ui/`: مجلد واجهة المستخدم
  - `main_window.py`: النافذة الرئيسية
  - `import_dialog.py`: نافذة استيراد الملفات
  - `metadata_editor.py`: محرر البيانات الوصفية
  - `search_panel.py`: لوحة البحث المتقدم
- `models/`: مجلد النماذج
  - `document.py`: نموذج المستند
  - `category.py`: نموذج التصنيف
- `utils/`: مجلد الأدوات المساعدة
  - `file_handler.py`: معالج الملفات
  - `search_engine.py`: محرك البحث
- `storage/`: مجلد تخزين الملفات المستوردة

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT.
