#define MyAppName "Archive Department"
#define MyAppNameArabic "أرشيف الدائرة البشرية"
#define MyAppVersion "1.0"
#define MyAppPublisher "Archive Department"
#define MyAppURL ""
#define MyAppExeName "Archiver.exe"
#define MyAppSourceExe "dist\Archiver\Archiver.exe"
#define MyAppOutputDir "Output"
#define MyAppOutputName "ArchiveDepartment_1.0"

[Setup]
; Basic application information
AppId={{5DCDCCD9-D77A-49AE-A1BA-32E0DD12FE16}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppVerName={#MyAppName} {#MyAppVersion}

; Installation settings
DefaultDirName={autopf}\{#MyAppNameArabic}
DefaultGroupName={#MyAppNameArabic}
DisableProgramGroupPage=no
DisableDirPage=no
DisableReadyPage=no
DisableStartupPrompt=no
DisableWelcomePage=no

; License information
LicenseFile=LICENSE

; Output information
OutputBaseFilename={#MyAppOutputName}
OutputDir={#MyAppOutputDir}
SetupIconFile=resources\logo.png.ico
UninstallDisplayIcon={app}\{#MyAppExeName}

; Compression settings
Compression=lzma2/ultra64
SolidCompression=yes
LZMAUseSeparateProcess=yes
LZMANumBlockThreads=4
LZMANumFastBytes=273
LZMAAlgorithm=1

; Interface settings
WizardStyle=modern
WizardSizePercent=120,150
ShowLanguageDialog=no

; Installation privileges
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog

; Create AppData folder with full permissions for users
[Dirs]
Name: "{userappdata}\{#MyAppNameArabic}"; Permissions: users-full

; Supported languages
[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "Create a desktop icon"; GroupDescription: "Additional icons"; Flags: unchecked
Name: "quicklaunchicon"; Description: "Create a Quick Launch icon"; GroupDescription: "Additional icons"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; Main executable file
Source: "{#MyAppSourceExe}"; DestDir: "{app}"; Flags: ignoreversion

; Libraries and additional files
Source: "dist\Archiver\_internal\*.*"; DestDir: "{app}\_internal"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "dist\Archiver\*.*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; Application icon
Source: "resources\*.*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
; Start menu
Name: "{group}\{#MyAppNameArabic}"; Filename: "{app}\{#MyAppExeName}"
Name: "{group}\Uninstall {#MyAppName}"; Filename: "{uninstallexe}"

; Desktop
Name: "{autodesktop}\{#MyAppNameArabic}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

; Quick Launch toolbar
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppNameArabic}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
; Run the application after installation
Filename: "{app}\{#MyAppExeName}"; Description: "Launch the application"; Flags: nowait postinstall skipifsilent

[Code]
// Function to check .NET Framework version
function IsDotNetDetected(version: string; service: Cardinal): boolean;
var
    key: string;
    install, serviceCount: Cardinal;
    success: boolean;
begin
    // Original code to check .NET Framework version
    key := 'SOFTWARE\Microsoft\NET Framework Setup\NDP\' + version;
    
    // 32-bit and 64-bit specific code
    if not IsWin64 then
        success := RegQueryDWordValue(HKLM, key, 'Install', install)
    else
        success := RegQueryDWordValue(HKLM, 'SOFTWARE\Wow6432Node\Microsoft\NET Framework Setup\NDP\' + version, 'Install', install);
    
    // Check if installation exists
    if not success or (install <> 1) then
    begin
        success := false;
    end
    else if (service <> 0) then
    begin
        // Check service version if required
        if RegQueryDWordValue(HKLM, key, 'Servicing', serviceCount) then
            success := (serviceCount >= service)
        else
            success := false;
    end;
    
    Result := success;
end;

// Function to check requirements before installation
function InitializeSetup(): Boolean;
var
    ErrorCode: Integer;
begin
    Result := True;
    
    // Check Windows version
    if not IsWin64 then
    begin
        MsgBox('This application requires a 64-bit Windows operating system.', mbError, MB_OK);
        Result := False;
        Exit;
    end;
    
    // Check for .NET Framework 4.8 or newer
    if not IsDotNetDetected('v4\Full', 0) then
    begin
        if MsgBox('This application requires .NET Framework 4.8 or newer.' + #13#10 +
                  'Would you like to download it now?', mbConfirmation, MB_YESNO) = IDYES then
        begin
            ShellExec('open',
                'https://dotnet.microsoft.com/download/dotnet-framework/thank-you/net48-web-installer',
                '', '', SW_SHOW, ewNoWait, ErrorCode);
        end;
        Result := False;
    end;
    
    Result := True;
end;
