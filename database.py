import os
import sqlite3
from datetime import datetime

class Database:
    """إدارة قاعدة البيانات لبرنامج الأرشفة"""

    def __init__(self, db_path=None):
        """تهيئة قاعدة البيانات"""
        if db_path is None:
            # استخدام مجلد AppData لتخزين قاعدة البيانات
            app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'أرشفة')
            if not os.path.exists(app_data_dir):
                os.makedirs(app_data_dir)
            self.db_path = os.path.join(app_data_dir, "archive.db")
        else:
            self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()

    def connect(self):
        """الاتصال بقاعدة البيانات"""
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row
        self.cursor = self.conn.cursor()

    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.conn:
            self.conn.close()

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات إذا لم تكن موجودة"""
        # جدول التصنيفات
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            parent_id INTEGER,
            allowed_file_types TEXT,
            display_order INTEGER DEFAULT 0,
            FOREIGN KEY (parent_id) REFERENCES categories (id)
        )
        ''')

        # التحقق من وجود عمود display_order وإضافته إذا لم يكن موجودًا
        self.cursor.execute("PRAGMA table_info(categories)")
        columns = [column[1] for column in self.cursor.fetchall()]

        if "display_order" not in columns:
            self.cursor.execute("ALTER TABLE categories ADD COLUMN display_order INTEGER DEFAULT 0")

            # تحديث قيم display_order للتصنيفات الحالية
            self.cursor.execute("SELECT id, parent_id FROM categories ORDER BY id")
            categories = self.cursor.fetchall()

            # تجميع التصنيفات حسب المستوى الأبوي
            parent_groups = {}
            for cat_id, parent_id in categories:
                if parent_id not in parent_groups:
                    parent_groups[parent_id] = []
                parent_groups[parent_id].append(cat_id)

            # تعيين قيم display_order لكل مجموعة
            for parent_id, cat_ids in parent_groups.items():
                for i, cat_id in enumerate(cat_ids):
                    self.cursor.execute("UPDATE categories SET display_order = ? WHERE id = ?", (i, cat_id))

            self.conn.commit()

        # جدول المستندات
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS documents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            original_filename TEXT,
            import_date TEXT,
            creation_date TEXT,
            modification_date TEXT,
            keywords TEXT
        )
        ''')

        # جدول العلاقة بين المستندات والتصنيفات
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS document_categories (
            document_id INTEGER,
            category_id INTEGER,
            PRIMARY KEY (document_id, category_id),
            FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
        )
        ''')

        # جدول البيانات الوصفية المخصصة
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS custom_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            document_id INTEGER,
            field_name TEXT NOT NULL,
            field_value TEXT,
            FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
        )
        ''')

        self.conn.commit()

    def add_document(self, title, file_path, description="", file_type="", file_size=0,
                    original_filename="", creation_date=None, modification_date=None, keywords=""):
        """إضافة مستند جديد إلى قاعدة البيانات"""
        import_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if not creation_date:
            creation_date = import_date

        if not modification_date:
            modification_date = import_date

        self.cursor.execute('''
        INSERT INTO documents (
            title, description, file_path, file_type, file_size,
            original_filename, import_date, creation_date, modification_date, keywords
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            title, description, file_path, file_type, file_size,
            original_filename, import_date, creation_date, modification_date, keywords
        ))

        self.conn.commit()
        return self.cursor.lastrowid

    def add_category(self, name, description="", parent_id=None, allowed_file_types=None):
        """إضافة تصنيف جديد"""
        try:
            # الحصول على أعلى ترتيب للتصنيفات الحالية في نفس المستوى
            if parent_id is None:
                self.cursor.execute('''
                SELECT MAX(display_order) FROM categories WHERE parent_id IS NULL
                ''')
            else:
                self.cursor.execute('''
                SELECT MAX(display_order) FROM categories WHERE parent_id = ?
                ''', (parent_id,))

            max_order = self.cursor.fetchone()[0]
            new_order = 0 if max_order is None else max_order + 1

            self.cursor.execute('''
            INSERT INTO categories (name, description, parent_id, allowed_file_types, display_order)
            VALUES (?, ?, ?, ?, ?)
            ''', (name, description, parent_id, allowed_file_types, new_order))

            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.IntegrityError:
            # التصنيف موجود بالفعل
            return None

    def assign_category_to_document(self, document_id, category_id):
        """ربط مستند بتصنيف"""
        try:
            self.cursor.execute('''
            INSERT INTO document_categories (document_id, category_id)
            VALUES (?, ?)
            ''', (document_id, category_id))

            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            # العلاقة موجودة بالفعل
            return False

    def add_custom_metadata(self, document_id, field_name, field_value):
        """إضافة بيانات وصفية مخصصة للمستند"""
        self.cursor.execute('''
        INSERT INTO custom_metadata (document_id, field_name, field_value)
        VALUES (?, ?, ?)
        ''', (document_id, field_name, field_value))

        self.conn.commit()
        return self.cursor.lastrowid

    def get_document(self, document_id):
        """استرجاع مستند بواسطة المعرف"""
        self.cursor.execute('''
        SELECT * FROM documents WHERE id = ?
        ''', (document_id,))

        return dict(self.cursor.fetchone() or {})

    def get_documents(self, category_id=None, search_term=None, limit=100, offset=0):
        """استرجاع المستندات مع إمكانية التصفية حسب التصنيف أو البحث"""
        query = "SELECT DISTINCT d.* FROM documents d"
        params = []

        if category_id:
            query += " JOIN document_categories dc ON d.id = dc.document_id WHERE dc.category_id = ?"
            params.append(category_id)

        if search_term:
            if category_id:
                query += " AND"
            else:
                query += " WHERE"

            query += " (d.title LIKE ? OR d.description LIKE ? OR d.keywords LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])

        query += " ORDER BY d.import_date DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        self.cursor.execute(query, params)
        return [dict(row) for row in self.cursor.fetchall()]

    def get_categories(self, parent_id=None):
        """استرجاع التصنيفات"""
        if parent_id is None:
            self.cursor.execute('''
            SELECT * FROM categories WHERE parent_id IS NULL
            ORDER BY display_order, name
            ''')
        else:
            self.cursor.execute('''
            SELECT * FROM categories WHERE parent_id = ?
            ORDER BY display_order, name
            ''', (parent_id,))

        return [dict(row) for row in self.cursor.fetchall()]

    def get_document_categories(self, document_id):
        """استرجاع تصنيفات مستند معين"""
        self.cursor.execute('''
        SELECT c.* FROM categories c
        JOIN document_categories dc ON c.id = dc.category_id
        WHERE dc.document_id = ?
        ORDER BY c.name
        ''', (document_id,))

        return [dict(row) for row in self.cursor.fetchall()]

    def get_custom_metadata(self, document_id):
        """استرجاع البيانات الوصفية المخصصة لمستند معين"""
        self.cursor.execute('''
        SELECT field_name, field_value FROM custom_metadata
        WHERE document_id = ?
        ''', (document_id,))

        return {row['field_name']: row['field_value'] for row in self.cursor.fetchall()}

    def search_documents(self, query, category_id=None, limit=100, offset=0):
        """بحث متقدم في المستندات"""
        sql_query = """
        SELECT DISTINCT d.* FROM documents d
        LEFT JOIN document_categories dc ON d.id = dc.document_id
        LEFT JOIN custom_metadata cm ON d.id = cm.document_id
        WHERE (
            d.title LIKE ? OR
            d.description LIKE ? OR
            d.keywords LIKE ? OR
            d.file_path LIKE ? OR
            cm.field_value LIKE ?
        )
        """

        params = [f"%{query}%"] * 5

        if category_id:
            sql_query += " AND dc.category_id = ?"
            params.append(category_id)

        sql_query += " ORDER BY d.import_date DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        self.cursor.execute(sql_query, params)
        return [dict(row) for row in self.cursor.fetchall()]

    def update_document(self, document_id, **kwargs):
        """تحديث بيانات مستند"""
        if not kwargs:
            return False

        set_clause = ", ".join([f"{key} = ?" for key in kwargs.keys()])
        params = list(kwargs.values())
        params.append(document_id)

        self.cursor.execute(f"""
        UPDATE documents SET {set_clause}
        WHERE id = ?
        """, params)

        self.conn.commit()
        return self.cursor.rowcount > 0

    def delete_document(self, document_id):
        """حذف مستند من قاعدة البيانات"""
        self.cursor.execute("DELETE FROM documents WHERE id = ?", (document_id,))
        self.conn.commit()
        return self.cursor.rowcount > 0

    def delete_category(self, category_id):
        """حذف تصنيف"""
        self.cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
        self.conn.commit()
        return self.cursor.rowcount > 0

    def get_category(self, category_id):
        """استرجاع تصنيف بواسطة المعرف"""
        self.cursor.execute('''
        SELECT * FROM categories WHERE id = ?
        ''', (category_id,))

        return dict(self.cursor.fetchone() or {})

    def update_category(self, category_id, **kwargs):
        """تحديث بيانات تصنيف"""
        if not kwargs:
            return False

        set_clause = ", ".join([f"{key} = ?" for key in kwargs.keys()])
        params = list(kwargs.values())
        params.append(category_id)

        self.cursor.execute(f"""
        UPDATE categories SET {set_clause}
        WHERE id = ?
        """, params)

        self.conn.commit()
        return self.cursor.rowcount > 0

    def update_category_order(self, category_id, new_order):
        """تحديث ترتيب التصنيف"""
        # الحصول على معلومات التصنيف الحالي
        self.cursor.execute('SELECT parent_id, display_order FROM categories WHERE id = ?', (category_id,))
        result = self.cursor.fetchone()
        if not result:
            return False

        parent_id, current_order = result

        # تحديث ترتيب التصنيفات الأخرى في نفس المستوى
        if new_order > current_order:
            # تحريك لأسفل - تقليل ترتيب التصنيفات بين الترتيب القديم والجديد
            if parent_id is None:
                self.cursor.execute('''
                UPDATE categories
                SET display_order = display_order - 1
                WHERE parent_id IS NULL AND display_order > ? AND display_order <= ?
                ''', (current_order, new_order))
            else:
                self.cursor.execute('''
                UPDATE categories
                SET display_order = display_order - 1
                WHERE parent_id = ? AND display_order > ? AND display_order <= ?
                ''', (parent_id, current_order, new_order))
        elif new_order < current_order:
            # تحريك لأعلى - زيادة ترتيب التصنيفات بين الترتيب الجديد والقديم
            if parent_id is None:
                self.cursor.execute('''
                UPDATE categories
                SET display_order = display_order + 1
                WHERE parent_id IS NULL AND display_order >= ? AND display_order < ?
                ''', (new_order, current_order))
            else:
                self.cursor.execute('''
                UPDATE categories
                SET display_order = display_order + 1
                WHERE parent_id = ? AND display_order >= ? AND display_order < ?
                ''', (parent_id, new_order, current_order))

        # تحديث ترتيب التصنيف المحدد
        self.cursor.execute('''
        UPDATE categories
        SET display_order = ?
        WHERE id = ?
        ''', (new_order, category_id))

        self.conn.commit()
        return True

    def is_file_type_allowed_in_category(self, category_id, file_type):
        """التحقق من توافق نوع الملف مع التصنيف"""
        if not category_id or not file_type:
            return True  # إذا لم يتم تحديد تصنيف أو نوع ملف

        category = self.get_category(category_id)
        allowed_types = category.get('allowed_file_types')

        if not allowed_types:  # إذا لم يتم تحديد أنواع ملفات مسموحة
            return True

        allowed_types_list = [t.strip() for t in allowed_types.split(',')]
        return file_type in allowed_types_list

    def get_all_categories(self):
        """استرجاع جميع التصنيفات بما في ذلك التصنيفات الفرعية"""
        self.cursor.execute('''
        SELECT c.*, p.name as parent_name
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        ORDER BY CASE WHEN c.parent_id IS NULL THEN 0 ELSE 1 END, p.name, c.name
        ''')

        return [dict(row) for row in self.cursor.fetchall()]

    def get_documents_by_date_range(self, start_date, end_date, category_id=None):
        """استرجاع المستندات في نطاق تاريخي محدد

        Args:
            start_date: تاريخ البداية (YYYY-MM-DD)
            end_date: تاريخ النهاية (YYYY-MM-DD)
            category_id: معرف التصنيف (اختياري)

        Returns:
            list: قائمة بالمستندات
        """
        query = """
        SELECT d.*, c.name as category_name
        FROM documents d
        LEFT JOIN document_categories dc ON d.id = dc.document_id
        LEFT JOIN categories c ON dc.category_id = c.id
        WHERE d.import_date BETWEEN ? AND ?
        """

        params = [f"{start_date} 00:00:00", f"{end_date} 23:59:59"]

        if category_id:
            query += " AND dc.category_id = ?"
            params.append(category_id)

        query += " ORDER BY d.import_date DESC"

        self.cursor.execute(query, params)
        return [dict(row) for row in self.cursor.fetchall()]

    def get_exported_documents(self, start_date=None, end_date=None):
        """استرجاع المستندات التي تم تصديرها

        Args:
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)

        Returns:
            list: قائمة بالمستندات المصدرة
        """
        # ملاحظة: نحتاج إلى إضافة عمود export_date في جدول documents
        # سنقوم بإنشاء الجدول إذا لم يكن موجوداً

        # التحقق من وجود عمود export_date
        self.cursor.execute("PRAGMA table_info(documents)")
        columns = [column[1] for column in self.cursor.fetchall()]

        if "export_date" not in columns:
            # إضافة عمود export_date إلى جدول documents
            self.cursor.execute("ALTER TABLE documents ADD COLUMN export_date TEXT")
            self.conn.commit()

        query = """
        SELECT
            d.id, d.title, d.file_path, d.file_type, d.file_size,
            d.import_date, d.export_date, d.original_filename,
            c.name as category_name, c.id as category_id
        FROM documents d
        LEFT JOIN document_categories dc ON d.id = dc.document_id
        LEFT JOIN categories c ON dc.category_id = c.id
        WHERE d.export_date IS NOT NULL
        """

        params = []

        if start_date and end_date:
            query += " AND d.export_date BETWEEN ? AND ?"
            params.extend([f"{start_date} 00:00:00", f"{end_date} 23:59:59"])

        query += " ORDER BY d.export_date DESC"

        self.cursor.execute(query, params)
        return [dict(row) for row in self.cursor.fetchall()]

    def update_document_export_date(self, document_id):
        """تحديث تاريخ تصدير المستند

        Args:
            document_id: معرف المستند

        Returns:
            bool: نجاح العملية
        """
        export_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.cursor.execute(
            "UPDATE documents SET export_date = ? WHERE id = ?",
            (export_date, document_id)
        )

        self.conn.commit()
        return self.cursor.rowcount > 0

    def get_documents_stats(self, period_type, start_date=None, end_date=None):
        """الحصول على إحصائيات المستندات

        Args:
            period_type: نوع الفترة (daily, weekly, monthly, yearly)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)

        Returns:
            dict: إحصائيات المستندات
        """
        # تحديد صيغة التاريخ حسب نوع الفترة
        if period_type == 'daily':
            date_format = '%Y-%m-%d'
        elif period_type == 'weekly':
            date_format = '%Y-%W'  # السنة-رقم الأسبوع
        elif period_type == 'monthly':
            date_format = '%Y-%m'
        elif period_type == 'yearly':
            date_format = '%Y'
        else:
            date_format = '%Y-%m-%d'

        # بناء الاستعلام
        query = """
        SELECT
            strftime(?, import_date) as period,
            COUNT(*) as document_count,
            SUM(file_size) as total_size
        FROM documents
        """

        params = [date_format]

        # إضافة شرط النطاق الزمني إذا تم تحديده
        if start_date and end_date:
            query += " WHERE import_date BETWEEN ? AND ?"
            params.extend([f"{start_date} 00:00:00", f"{end_date} 23:59:59"])

        query += " GROUP BY period ORDER BY period"

        self.cursor.execute(query, params)

        # تجميع النتائج
        stats = {
            'periods': [],
            'document_counts': [],
            'total_sizes': [],
            'total_documents': 0,
            'total_size': 0,
            'files_details': []  # إضافة تفاصيل الملفات
        }

        for row in self.cursor.fetchall():
            stats['periods'].append(row['period'])
            stats['document_counts'].append(row['document_count'])
            stats['total_sizes'].append(row['total_size'])
            stats['total_documents'] += row['document_count']
            stats['total_size'] += row['total_size'] or 0

        # الحصول على تفاصيل الملفات في النطاق الزمني المحدد
        if start_date and end_date:
            files_query = """
            SELECT
                d.id, d.title, d.file_path, d.file_type, d.file_size,
                d.import_date, d.original_filename,
                c.name as category_name
            FROM documents d
            LEFT JOIN document_categories dc ON d.id = dc.document_id
            LEFT JOIN categories c ON dc.category_id = c.id
            WHERE d.import_date BETWEEN ? AND ?
            ORDER BY d.import_date DESC
            """

            self.cursor.execute(files_query, [f"{start_date} 00:00:00", f"{end_date} 23:59:59"])
            stats['files_details'] = [dict(row) for row in self.cursor.fetchall()]

        return stats
