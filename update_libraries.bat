@echo off
echo تحديث مكتبات برنامج أرشيف الدائرة البشرية
echo ===================================

echo هذا الملف مخصص لتحديث المكتبات المستخدمة في البرنامج.
echo يجب تشغيل هذا الملف فقط إذا كنت تواجه مشاكل في البرنامج
echo أو إذا تم إصدار تحديثات هامة للمكتبات.
echo.

set /p confirm=هل تريد المتابعة بتحديث المكتبات؟ (Y/N): 
if /i not "%confirm%"=="Y" goto :cancel

echo التحقق من وجود Python...
where python > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير موجود في متغير PATH.
    echo يرجى تثبيت Python أولاً.
    goto :error
)

echo التحقق من وجود pip...
python -m pip --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: pip غير مثبت.
    echo جاري تثبيت pip...
    python -m ensurepip --default-pip
    if %errorlevel% neq 0 (
        echo فشل في تثبيت pip.
        goto :error
    )
)

echo تحديث pip...
python -m pip install --upgrade pip

echo تثبيت/تحديث المكتبات المطلوبة...
python -m pip install --upgrade PyQt6==6.5.0 pillow==9.5.0 reportlab==4.0.4 pandas==2.0.3 openpyxl==3.1.2 arabic-reshaper==3.0.0 python-bidi==0.4.2

if %errorlevel% neq 0 (
    echo فشل في تحديث بعض المكتبات.
    goto :error
)

echo.
echo تم تحديث المكتبات بنجاح!
echo.
goto :end

:cancel
echo.
echo تم إلغاء عملية تحديث المكتبات.
echo.
goto :end

:error
echo.
echo فشل في تحديث المكتبات!
echo.

:end
pause
