import sys
import os
from PyQt6.QtWidgets import QA<PERSON>lication, QStyleFactory, QMessageBox
from PyQt6.QtCore import QDir, Qt
from PyQt6.QtGui import QFont, QPalette, QColor, QIcon

# Ensure the current directory is in the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_mysql import Database  # تم التعديل لاستخدام MySQL
from utils.file_handler import FileHandler
from utils.search_engine import SearchEngine
from utils.license_manager import LicenseManager
from ui.main_window import MainWindow
from ui.license_dialog import LicenseDialog
from ui.login_dialog import LoginDialog

def main():
    """نقطة الدخول الرئيسية للتطبيق"""
    # إنشاء مجلد التخزين في مجلد AppData
    app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'أر<PERSON>يف الدائرة البشرية')
    storage_dir = os.path.join(app_data_dir, "storage")
    if not os.path.exists(app_data_dir):
        os.makedirs(app_data_dir)
    if not os.path.exists(storage_dir):
        os.makedirs(storage_dir)

    # تهيئة التطبيق
    app = QApplication(sys.argv)

    # تعيين أيقونة التطبيق
    app.setWindowIcon(QIcon("resources/logo.png"))

    # تطبيق نمط حديث
    app.setStyle(QStyleFactory.create("Fusion"))

    # ضبط اتجاه النص من اليمين إلى اليسار للدعم العربي
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # إضافة أنماط CSS للتطبيق
    app.setStyleSheet("""
        QMainWindow, QDialog {
            background-color: #f5f5f5;
        }
        QToolBar {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 10px;
            spacing: 10px;
        }
        QToolBar QToolButton {
            color: white;
            background-color: transparent;
            border: none;
            border-radius: 4px;
            padding: 8px;
            margin: 2px;
            font-size: 13px;
        }
        QToolBar QToolButton:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        QToolBar QToolButton:pressed {
            background-color: rgba(255, 255, 255, 0.3);
        }
        QPushButton {
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            min-width: 80px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5c6bc0;
        }
        QPushButton:pressed {
            background-color: #303f9f;
        }
        QPushButton:disabled {
            background-color: #d3d3d3;
            color: #a9a9a9;
        }
        QLineEdit, QTextEdit, QComboBox {
            border: 1px solid #c5cae9;
            border-radius: 4px;
            padding: 8px;
            background-color: white;
            selection-background-color: #3f51b5;
        }
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border: 2px solid #3f51b5;
        }
        QTreeWidget, QListWidget, QTableWidget {
            border: 1px solid #c5cae9;
            border-radius: 4px;
            background-color: white;
            alternate-background-color: #f5f5f5;
            selection-background-color: #3f51b5;
        }
        QTreeWidget::item, QListWidget::item, QTableWidget::item {
            padding: 4px;
            border-bottom: 1px solid #f0f0f0;
        }
        QTreeWidget::item:selected, QListWidget::item:selected, QTableWidget::item:selected {
            background-color: #3f51b5;
            color: white;
        }
        QTreeWidget::item:hover, QListWidget::item:hover, QTableWidget::item:hover {
            background-color: #e8eaf6;
        }
        QTabWidget::pane {
            border: 1px solid #c5cae9;
            border-radius: 4px;
            top: -1px;
        }
        QTabBar::tab {
            background-color: #e8eaf6;
            border: 1px solid #c5cae9;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            padding: 8px 16px;
            margin-right: 2px;
            font-weight: bold;
        }
        QTabBar::tab:selected {
            background-color: #3f51b5;
            color: white;
        }
        QTabBar::tab:hover:!selected {
            background-color: #c5cae9;
        }
        QStatusBar {
            background-color: #3f51b5;
            color: white;
            padding: 5px;
        }
        QLabel {
            color: #333;
        }
        QGroupBox {
            border: 1px solid #c5cae9;
            border-radius: 4px;
            margin-top: 16px;
            font-weight: bold;
            color: #3f51b5;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            subcontrol-position: top center;
            padding: 0 8px;
        }
        QSplitter::handle {
            background-color: #c5cae9;
        }
        QScrollBar:vertical {
            border: none;
            background-color: #f5f5f5;
            width: 10px;
            margin: 0px;
        }
        QScrollBar::handle:vertical {
            background-color: #c5cae9;
            min-height: 20px;
            border-radius: 5px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #3f51b5;
        }
        QScrollBar:horizontal {
            border: none;
            background-color: #f5f5f5;
            height: 10px;
            margin: 0px;
        }
        QScrollBar::handle:horizontal {
            background-color: #c5cae9;
            min-width: 20px;
            border-radius: 5px;
        }
        QScrollBar::handle:horizontal:hover {
            background-color: #3f51b5;
        }
        QMenu {
            background-color: white;
            border: 1px solid #c5cae9;
            border-radius: 4px;
        }
        QMenu::item {
            padding: 6px 25px 6px 25px;
        }
        QMenu::item:selected {
            background-color: #e8eaf6;
            color: #3f51b5;
        }
        QHeaderView::section {
            background-color: #3f51b5;
            color: black;
            padding: 5px;
            border: none;
        }
        QToolTip {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 5px;
        }
    """)

    # عرض نافذة تسجيل الدخول
    login_dialog = LoginDialog()
    if not login_dialog.exec():
        # إذا تم إغلاق نافذة تسجيل الدخول بدون تسجيل دخول ناجح
        sys.exit(0)

    # التحقق من الترخيص
    license_manager = LicenseManager()

    # التحقق من وجود ترخيص مسبق
    if not license_manager.is_licensed():
        # عرض نافذة الترخيص
        if not LicenseDialog.get_license(license_manager):
            # إذا تم إغلاق نافذة الترخيص بدون إدخال مفتاح صحيح أو تفعيل النسخة التجريبية
            QMessageBox.critical(
                None,
                "خطأ في التنشيط",
                "لم يتم تنشيط البرنامج. البرنامج سيغلق الآن."
            )
            sys.exit(0)

    # تهيئة قاعدة البيانات
    try:
        db = Database()
        if db.conn is None:
            raise ConnectionError("فشل الاتصال بقاعدة البيانات.")
    except Exception as e:
        QMessageBox.critical(
            None,
            "خطأ في الاتصال بقاعدة البيانات",
            f"فشل الاتصال بقاعدة بيانات MySQL.\n\n"
            f"يرجى التأكد من أن خدمة MySQL تعمل بشكل صحيح وأن بيانات الاتصال في ملف 'db_config.py' صحيحة.\n\n"
            f"تفاصيل الخطأ: {e}"
        )
        sys.exit(1)

    # تهيئة معالج الملفات
    file_handler = FileHandler(storage_dir)

    # تهيئة محرك البحث
    search_engine = SearchEngine(db)

    # إنشاء النافذة الرئيسية
    main_window = MainWindow(db, file_handler, search_engine)

    # إضافة معلومات الترخيص إلى شريط الحالة
    license_info = license_manager.get_license_info()
    if license_info:
        if license_info.get("type") == "trial":
            days_left = license_info.get("days_left", 0)
            main_window.statusBar.showMessage(f"نسخة تجريبية: متبقي {days_left} يوم")
        else:
            main_window.statusBar.showMessage("نسخة مرخصة")

    main_window.show()

    # تنفيذ حلقة الأحداث
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
