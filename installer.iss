#define MyAppName "أرشفة"
#define MyAppVersion "1.0"
#define MyAppPublisher "أرشفة"
#define MyAppURL ""
#define MyAppExeName "أرشفة.exe"
#define MyAppSourceExe "dist\أرشفة\أرشفة.exe"
#define MyAppOutputDir "Output"
#define MyAppOutputName "أرشفة_إصدار_1.0"

[Setup]
; معلومات التطبيق الأساسية
AppId={{5DCDCCD9-D77A-49AE-A1BA-32E0DD12FE16}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppVerName={#MyAppName} {#MyAppVersion}

; إعدادات التثبيت
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
DisableProgramGroupPage=no
DisableDirPage=no
DisableReadyPage=no
DisableStartupPrompt=no
DisableWelcomePage=no

; معلومات الترخيص
LicenseFile=LICENSE

; معلومات الإخراج
OutputBaseFilename={#MyAppOutputName}
OutputDir={#MyAppOutputDir}
SetupIconFile=resources\logo.png.ico
UninstallDisplayIcon={app}\{#MyAppExeName}

; إعدادات الضغط
Compression=lzma2/ultra64
SolidCompression=yes
LZMAUseSeparateProcess=yes
LZMANumBlockThreads=4
LZMANumFastBytes=273
LZMAAlgorithm=1

; إعدادات الواجهة
WizardStyle=modern
WizardSizePercent=120,150

; صلاحيات التثبيت
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog

; اللغات المدعومة
[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; الملف التنفيذي الرئيسي
Source: "{#MyAppSourceExe}"; DestDir: "{app}"; Flags: ignoreversion

; مكتبات وملفات إضافية
Source: "dist\أرشفة\_internal\*.*"; DestDir: "{app}\_internal"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "dist\أرشفة\*.*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; أيقونة التطبيق
Source: "resources\*.*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
; قائمة ابدأ
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"

; سطح المكتب
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

; شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
; تشغيل التطبيق بعد الانتهاء من التثبيت
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[Code]
// دالة للتحقق من إصدار .NET Framework
function IsDotNetDetected(version: string; service: Cardinal): boolean;
var
    key: string;
    install, serviceCount: Cardinal;
    success: boolean;
begin
    // الكود الأصلي للتحقق من إصدار .NET Framework
    key := 'SOFTWARE\Microsoft\NET Framework Setup\NDP\' + version;
    
    // 32-bit and 64-bit specific code
    if not IsWin64 then
        success := RegQueryDWordValue(HKLM, key, 'Install', install)
    else
        success := RegQueryDWordValue(HKLM, 'SOFTWARE\Wow6432Node\Microsoft\NET Framework Setup\NDP\' + version, 'Install', install);
    
    // التحقق من وجود التثبيت
    if not success or (install <> 1) then
    begin
        success := false;
    end
    else if (service <> 0) then
    begin
        // التحقق من إصدار الخدمة إذا كان مطلوباً
        if RegQueryDWordValue(HKLM, key, 'Servicing', serviceCount) then
            success := (serviceCount >= service)
        else
            success := false;
    end;
    
    Result := success;
end;

// دالة لفحص المتطلبات قبل التثبيت
function InitializeSetup(): Boolean;
var
    ErrorCode: Integer;
begin
    Result := True;
    
    // التحقق من إصدار Windows
    if not IsWin64 then
    begin
        MsgBox('هذا التطبيق يتطلب نظام تشغيل Windows 64 بت.', mbError, MB_OK);
        Result := False;
        Exit;
    end;
    
    // التحقق من .NET Framework 4.8 أو أحدث
    if not IsDotNetDetected('v4\Full', 0) then
    begin
        if MsgBox('هذا التطبيق يتطلب .NET Framework 4.8 أو أحدث.' + #13#10 +
                  'هل ترغب في تنزيله الآن؟', mbConfirmation, MB_YESNO) = IDYES then
        begin
            ShellExec('open',
                'https://dotnet.microsoft.com/download/dotnet-framework/thank-you/net48-web-installer',
                '', '', SW_SHOW, ewNoWait, ErrorCode);
        end;
        Result := False;
    end;
    
    Result := True;
end;
