import sys
import os
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel, QLineEdit, QHBoxLayout
from PyQt6.QtGui import QIcon
from PyQt6.QtCore import Qt

class IconTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار الأيقونات وحقل كلمة المرور")
        self.setGeometry(100, 100, 400, 300)

        layout = QVBoxLayout(self)

        # إضافة تسمية
        label = QLabel("اختبار تحميل الأيقونات وتبديل وضع كلمة المرور")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)

        # إضافة زر لكل أيقونة
        icons = ["eye_closed.png", "eye_half.png", "eye_open.png"]

        for icon_name in icons:
            icon_path = os.path.join("resources", icon_name)
            btn = QPushButton(f"أيقونة: {icon_name}")

            # طباعة مسار الأيقونة للتأكد من وجودها
            print(f"مسار الأيقونة: {icon_path}")
            print(f"الأيقونة موجودة: {os.path.exists(icon_path)}")

            icon = QIcon(icon_path)
            btn.setIcon(icon)
            btn.setIconSize(Qt.QSize(24, 24))
            layout.addWidget(btn)

        # إضافة حقل كلمة المرور مع زر تبديل الوضع
        password_layout = QHBoxLayout()

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")

        self.toggle_btn = QPushButton()
        self.toggle_btn.setIcon(QIcon("resources/eye_closed.png"))
        self.toggle_btn.setFixedSize(30, 30)
        self.toggle_btn.clicked.connect(self.toggle_password_mode)

        password_layout.addWidget(self.password_input)
        password_layout.addWidget(self.toggle_btn)

        layout.addLayout(password_layout)

        # إضافة تسمية لعرض الوضع الحالي
        self.mode_label = QLabel("الوضع الحالي: إخفاء كلمة المرور")
        layout.addWidget(self.mode_label)

        self.password_mode = 0  # 0: مخفي، 1: إظهار الحرف الأخير، 2: ظاهر بالكامل

        self.show()

    def toggle_password_mode(self):
        """تبديل وضع عرض كلمة المرور"""
        self.password_mode = (self.password_mode + 1) % 3

        if self.password_mode == 0:
            # إخفاء كلمة المرور
            self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.toggle_btn.setIcon(QIcon("resources/eye_closed.png"))
            self.mode_label.setText("الوضع الحالي: إخفاء كلمة المرور")
        elif self.password_mode == 1:
            # إظهار الحرف الأخير
            self.password_input.setEchoMode(QLineEdit.EchoMode.PasswordEchoOnEdit)
            self.toggle_btn.setIcon(QIcon("resources/eye_half.png"))
            self.mode_label.setText("الوضع الحالي: إظهار الحرف الأخير")
        else:
            # إظهار كلمة المرور بالكامل
            self.password_input.setEchoMode(QLineEdit.EchoMode.Normal)
            self.toggle_btn.setIcon(QIcon("resources/eye_open.png"))
            self.mode_label.setText("الوضع الحالي: إظهار كلمة المرور بالكامل")

if __name__ == "__main__":
    try:
        print("بدء تشغيل البرنامج...")
        app = QApplication(sys.argv)
        print("تم إنشاء التطبيق")
        window = IconTest()
        print("تم إنشاء النافذة")
        sys.exit(app.exec())
    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
