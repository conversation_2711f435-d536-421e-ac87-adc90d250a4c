@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

set "PYTHON=python"
set "PIP=pip"
set "PYINSTALLER=pyinstaller"
set "SCRIPT_DIR=%~dp0"
set "OUTPUT_DIR=%SCRIPT_DIR%Output"
set "DIST_DIR=%SCRIPT_DIR%dist"
set "BUILD_DIR=%SCRIPT_DIR%build"
set "APP_NAME=أرشفة"
set "APP_VERSION=1.0"
set "INSTALLER_NAME=%APP_NAME%_%APP_VERSION%_Setup"
set "PYTHON_SCRIPT=main.py"
set "SPEC_FILE=archiver.spec"
set "ISS_FILE=installer.iss"
set "REQUIREMENTS=requirements.txt"

title أداة بناء حزمة تثبيت %APP_NAME%

:check_admin
:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %ERRORLEVEL% == 0 (
    set IS_ADMIN=1
) else (
    set IS_ADMIN=0
)

:check_python
echo.
echo ===== التحقق من تثبيت Python =====
%PYTHON% --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ! خطأ: Python غير مثبت أو غير مضاف إلى متغيرات النظام.
    echo يرجى تثبيت Python 3.6 أو أحدث من الموقع الرسمي.
    start "" "https://www.python.org/downloads/"
    pause
    exit /b 1
)
%PYTHON% -c "import sys; exit(0 if sys.version_info >= (3, 6) else 1)" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ! خطأ: يتطلب التطبيق Python 3.6 أو أحدث.
    pause
    exit /b 1
)
%PYTHON% --version

:check_requirements
echo.
echo ===== تثبيت المتطلبات =====
%PIP% install --upgrade pip
if exist "%REQUIREMENTS%" (
    %PIP% install -r "%REQUIREMENTS%"
) else (
    echo ! تحذير: ملف المتطلبات غير موجود. سيتم تثبيت المتطلبات الافتراضية.
    %PIP% install pyinstaller PyQt6 reportlab pandas openpyxl arabic-reshaper python-bidi
)
%PIP% install pyinstaller

:clean_previous_builds
echo.
echo ===== تنظيف عمليات البناء السابقة =====
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
if exist "%OUTPUT_DIR%" (
    del /q "%OUTPUT_DIR%\*.*" >nul 2>&1
    rmdir /s /q "%OUTPUT_DIR%"
)

:build_executable
echo.
echo ===== بناء الملف التنفيذي =====
if exist "%SPEC_FILE%" (
    %PYINSTALLER% --clean --noconfirm "%SPEC_FILE%"
) else (
    %PYINSTALLER% --noconfirm --onefile --windowed --icon=resources/logo.png.ico --name "%APP_NAME%" "%PYTHON_SCRIPT%"
)

if %ERRORLEVEL% NEQ 0 (
    echo ! خطأ: فشل بناء الملف التنفيذي.
    pause
    exit /b 1
)

:check_inno_setup
echo.
echo ===== التحقق من تثبيت Inno Setup =====
set "INNO_PATH="

:: البحث عن مسار Inno Setup
if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
) else (
    echo ! خطأ: Inno Setup غير مثبت.
    echo يرجى تثبيت Inno Setup من الرابط التالي:
    echo https://jrsoftware.org/isdl.php
    start "" "https://jrsoftware.org/isdl.php"
    pause
    exit /b 1
)

echo تم العثور على Inno Setup في: %INNO_PATH%

:build_installer
echo.
echo ===== بناء حزمة التثبيت =====
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
"%INNO_PATH%" "/q" "/O%OUTPUT_DIR%" "/F%INSTALLER_NAME%" "%ISS_FILE%"

if %ERRORLEVEL% NEQ 0 (
    echo ! خطأ: فشل بناء حزمة التثبيت.
    pause
    exit /b 1
)

:verify_installer
echo.
echo ===== التحقق من حزمة التثبيت =====
set "INSTALLER_PATH=%OUTPUT_DIR%\%INSTALLER_NAME%.exe"

if exist "%INSTALLER_PATH%" (
    echo.
    echo ===================================================
    echo ===== تم إنشاء حزمة التثبيت بنجاح! =====
    echo ===================================================
    echo.
    echo معلومات الحزمة:
    echo   - الاسم: %INSTALLER_NAME%.exe
    echo   - المسار: %INSTALLER_PATH%
    echo   - الحجم: 
    for /f "tokens=1-3" %%a in ('dir /a-d /os /s "%INSTALLER_PATH%" ^| find "%INSTALLER_NAME%.exe"') do echo     %%a %%b %%c
    echo.
    
    if %IS_ADMIN% == 1 (
        echo - تشغيل فاحص الفيروسات...
        "%ProgramFiles%\Windows Defender\MpCmdRun.exe" -Scan -ScanType 3 -File "%INSTALLER_PATH%"
    )
    
    echo.
    echo - فتح مجلد الإخراج...
    start "" "%OUTPUT_DIR%"
) else (
    echo ! خطأ: فشل إنشاء حزمة التثبيت.
    pause
    exit /b 1
)

:complete
echo.
echo ===== اكتملت عملية البناء بنجاح! =====
echo.

pause
