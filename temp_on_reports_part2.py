    def export_to_excel(table, filename):
        """تصدير الجدول إلى ملف Excel"""
        try:
            # التحقق من تثبيت المكتبات المطلوبة
            try:
                import pandas as pd
                import openpyxl
            except ImportError:
                QMessageBox.warning(
                    dialog,
                    "مكتبات مفقودة",
                    "يرجى تثبيت المكتبات المطلوبة باستخدام الأمر:\npip install pandas openpyxl"
                )
                return

            # تحويل بيانات الجدول إلى DataFrame
            data = []
            headers = []

            # الحصول على العناوين
            for j in range(table.columnCount()):
                headers.append(table.horizontalHeaderItem(j).text())

            # الحصول على البيانات
            for i in range(table.rowCount()):
                row_data = []
                for j in range(table.columnCount()):
                    item = table.item(i, j)
                    if item is not None:
                        row_data.append(item.text())
                    else:
                        row_data.append("")
                data.append(row_data)

            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=headers)

            # حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                dialog,
                "حفظ التقرير",
                filename,
                "Excel Files (*.xlsx)"
            )

            if file_path:
                df.to_excel(file_path, index=False)
                QMessageBox.information(dialog, "نجاح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

                # فتح الملف بعد التصدير
                try:
                    import os
                    os.startfile(file_path)
                except Exception as open_error:
                    print(f"تعذر فتح الملف: {str(open_error)}")

        except Exception as e:
            QMessageBox.warning(dialog, "خطأ", f"فشل في تصدير التقرير: {str(e)}")
            # طباعة تفاصيل الخطأ للمساعدة في التشخيص
            import traceback
            traceback.print_exc()

    def export_to_pdf(table, title, filename):
        """تصدير الجدول إلى ملف PDF"""
        try:
            # التحقق من تثبيت المكتبات المطلوبة
            try:
                import reportlab
                import arabic_reshaper
                import bidi.algorithm
            except ImportError:
                QMessageBox.warning(
                    dialog,
                    "مكتبات مفقودة",
                    "يرجى تثبيت المكتبات المطلوبة باستخدام الأمر:\npip install reportlab arabic-reshaper python-bidi"
                )
                return

            from reportlab.lib import colors
            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.lib.enums import TA_CENTER, TA_RIGHT
            import datetime
            import os
            import sys

            # حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                dialog,
                "حفظ التقرير",
                filename,
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return

            # تسجيل الخط العربي
            # محاولة استخدام خط عربي من النظام إذا كان متاحاً
            arabic_font_path = None
            arabic_font_name = "Arabic"

            # قائمة بالخطوط العربية الشائعة في نظام Windows
            system_arabic_fonts = [
                (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'arial.ttf'), "Arial"),
                (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'tahoma.ttf'), "Tahoma"),
                (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'simpo.ttf'), "Simplified Arabic"),
                (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'arabtype.ttf'), "Arabic Typesetting"),
                (os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'segoeui.ttf'), "Segoe UI")
            ]

            # التحقق من وجود الخطوط في النظام
            for font_path, font_name in system_arabic_fonts:
                if os.path.exists(font_path):
                    arabic_font_path = font_path
                    arabic_font_name = font_name
                    break

            # إذا لم يتم العثور على خط عربي في النظام، استخدم الخط المضمن في المشروع
            if not arabic_font_path:
                # استخدام الخط المضمن في المشروع
                arabic_font_path = "resources/fonts/Amiri-Regular.ttf"
                arabic_font_name = "Amiri"

                # التحقق من وجود الخط
                if not os.path.exists(arabic_font_path):
                    # إذا لم يكن الخط موجوداً، استخدم الخط الافتراضي
                    QMessageBox.warning(
                        dialog,
                        "تحذير",
                        "لم يتم العثور على خط عربي. سيتم استخدام الخط الافتراضي."
                    )

            # تسجيل الخط العربي إذا كان متاحاً
            if arabic_font_path and os.path.exists(arabic_font_path):
                try:
                    pdfmetrics.registerFont(TTFont(arabic_font_name, arabic_font_path))
                    print(f"تم تسجيل الخط العربي: {arabic_font_name}")
                except Exception as font_error:
                    print(f"خطأ في تسجيل الخط: {str(font_error)}")

            # تحويل بيانات الجدول إلى قائمة
            data = []

            # إضافة العناوين
            headers = []
            for j in range(table.columnCount()):
                # معالجة النص العربي
                header_text = table.horizontalHeaderItem(j).text()
                try:
                    # إعادة تشكيل النص العربي وتحويله إلى النص ثنائي الاتجاه
                    reshaped_text = arabic_reshaper.reshape(header_text)
                    bidi_text = bidi.algorithm.get_display(reshaped_text)
                    headers.append(bidi_text)
                except Exception as text_error:
                    print(f"خطأ في معالجة النص: {str(text_error)}")
                    headers.append(header_text)
            data.append(headers)

            # إضافة البيانات
            for i in range(table.rowCount()):
                row_data = []
                for j in range(table.columnCount()):
                    item = table.item(i, j)
                    if item is not None:
                        # معالجة النص العربي
                        cell_text = item.text()
                        try:
                            # إعادة تشكيل النص العربي وتحويله إلى النص ثنائي الاتجاه
                            reshaped_text = arabic_reshaper.reshape(cell_text)
                            bidi_text = bidi.algorithm.get_display(reshaped_text)
                            row_data.append(bidi_text)
                        except Exception as text_error:
                            print(f"خطأ في معالجة النص: {str(text_error)}")
                            row_data.append(cell_text)
                    else:
                        row_data.append("")
                data.append(row_data)

            # إنشاء ملف PDF
            doc = SimpleDocTemplate(
                file_path,
                pagesize=landscape(A4),
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=30
            )

            # إنشاء قائمة العناصر
            elements = []

            # إنشاء أنماط النصوص
            styles = getSampleStyleSheet()

            # إنشاء نمط للعنوان باستخدام الخط العربي
            title_style = ParagraphStyle(
                name='ArabicTitle',
                parent=styles['Heading1'],
                fontName=arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica-Bold',
                alignment=TA_CENTER,
                fontSize=18
            )

            # إنشاء نمط للنص العادي باستخدام الخط العربي
            normal_style = ParagraphStyle(
                name='ArabicNormal',
                parent=styles['Normal'],
                fontName=arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica',
                alignment=TA_CENTER,
                fontSize=12
            )

            # إضافة التاريخ والوقت
            current_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # معالجة العنوان والتاريخ للعربية
            try:
                reshaped_title = arabic_reshaper.reshape(title)
                bidi_title = bidi.algorithm.get_display(reshaped_title)

                date_text = f"تاريخ التقرير: {current_datetime}"
                reshaped_date = arabic_reshaper.reshape(date_text)
                bidi_date = bidi.algorithm.get_display(reshaped_date)

                # إضافة العنوان والتاريخ
                elements.append(Paragraph(bidi_title, title_style))
                elements.append(Paragraph(bidi_date, normal_style))
            except Exception as text_error:
                print(f"خطأ في معالجة النص: {str(text_error)}")
                elements.append(Paragraph(title, title_style))
                elements.append(Paragraph(f"تاريخ التقرير: {current_datetime}", normal_style))

            elements.append(Spacer(1, 20))

            # إنشاء جدول
            table_obj = Table(data)

            # تنسيق الجدول
            table_style = TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), arabic_font_name if arabic_font_path and os.path.exists(arabic_font_path) else 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ])

            # تطبيق التنسيق على الجدول
            table_obj.setStyle(table_style)

            # إضافة الجدول إلى العناصر
            elements.append(table_obj)

            # بناء المستند
            doc.build(elements)

            QMessageBox.information(dialog, "نجاح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

            # فتح الملف بعد التصدير
            try:
                os.startfile(file_path)
            except Exception as open_error:
                print(f"تعذر فتح الملف: {str(open_error)}")

        except Exception as e:
            QMessageBox.warning(dialog, "خطأ", f"فشل في تصدير التقرير: {str(e)}")
            # طباعة تفاصيل الخطأ للمساعدة في التشخيص
            import traceback
            traceback.print_exc()

    def open_file_from_table(table, row, column):
        """فتح الملف عند النقر على زر المعاينة"""
        # التحقق من أن العمود هو عمود المعاينة
        if (table == files_details_table and column == 6) or (table == exported_docs_table and column == 7):
            item = table.item(row, column)
            if item and item.text() == "فتح الملف":
                file_path = item.data(Qt.ItemDataRole.UserRole)
                if file_path and os.path.exists(file_path):
                    try:
                        # فتح الملف باستخدام التطبيق الافتراضي
                        os.startfile(file_path)
                    except Exception as e:
                        QMessageBox.warning(dialog, "خطأ", f"فشل في فتح الملف: {str(e)}")
                        print(f"خطأ في فتح الملف: {str(e)}")

    # ربط الإشارات
    generate_btn.clicked.connect(generate_time_report)
    export_generate_btn.clicked.connect(generate_exported_docs_report)

    # ربط النقر على الجداول لفتح الملفات
    files_details_table.cellClicked.connect(lambda row, column: open_file_from_table(files_details_table, row, column))
    exported_docs_table.cellClicked.connect(lambda row, column: open_file_from_table(exported_docs_table, row, column))

    export_excel_btn.clicked.connect(lambda: export_to_excel(files_details_table, "تقرير_زمني.xlsx"))
    export_pdf_btn.clicked.connect(lambda: export_to_pdf(files_details_table, "تقرير زمني للمستندات المؤرشفة", "تقرير_زمني.pdf"))

    export_report_excel_btn.clicked.connect(lambda: export_to_excel(exported_docs_table, "تقرير_المستندات_المصدرة.xlsx"))
    export_report_pdf_btn.clicked.connect(lambda: export_to_pdf(exported_docs_table, "تقرير المستندات المصدرة", "تقرير_المستندات_المصدرة.pdf"))

    # عرض النافذة
    dialog.exec()
