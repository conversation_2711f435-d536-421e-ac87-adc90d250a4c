import os
import sys
import subprocess
from datetime import datetime
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QFileDialog, QMessageBox, QComboBox, QFormLayout, QLineEdit,
                             QSpinBox, QCheckBox, QGroupBox, QTextEdit)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QImage

class ScanDialog(QDialog):
    """نافذة المسح الضوئي"""

    def __init__(self, parent, file_handler, database=None):
        """تهيئة نافذة المسح الضوئي"""
        super().__init__(parent)

        self.file_handler = file_handler
        self.db = database  # إضافة قاعدة البيانات
        self.scanned_file_path = None
        self.imported_document_id = None  # معرف المستند المستورد

        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("المسح الضوئي")
        self.setMinimumSize(500, 400)

        layout = QVBoxLayout(self)

        # تعليمات
        instructions_label = QLabel("قم بتوصيل الماسح الضوئي واضغط على زر 'مسح' لبدء المسح الضوئي.")
        instructions_label.setWordWrap(True)
        layout.addWidget(instructions_label)

        # إعدادات المسح الضوئي
        settings_group = QGroupBox("إعدادات المسح الضوئي")
        settings_layout = QFormLayout(settings_group)

        # نوع المستند
        self.document_type_combo = QComboBox()
        self.document_type_combo.addItems(["مستند", "صورة", "بطاقة هوية"])
        settings_layout.addRow("نوع المستند:", self.document_type_combo)

        # الدقة
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["150 DPI", "300 DPI", "600 DPI"])
        self.resolution_combo.setCurrentIndex(1)  # 300 DPI افتراضياً
        settings_layout.addRow("الدقة:", self.resolution_combo)

        # اللون
        self.color_mode_combo = QComboBox()
        self.color_mode_combo.addItems(["أبيض وأسود", "تدرج رمادي", "ملون"])
        self.color_mode_combo.setCurrentIndex(2)  # ملون افتراضياً
        settings_layout.addRow("وضع اللون:", self.color_mode_combo)

        # صيغة الملف
        self.file_format_combo = QComboBox()
        self.file_format_combo.addItems(["PDF", "JPEG", "PNG", "TIFF"])
        settings_layout.addRow("صيغة الملف:", self.file_format_combo)

        # اسم الملف
        self.filename_input = QLineEdit()
        self.filename_input.setText(f"scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        settings_layout.addRow("اسم الملف:", self.filename_input)

        # خيارات إضافية
        self.duplex_check = QCheckBox("مسح على الوجهين (إذا كان مدعوماً)")
        settings_layout.addRow("", self.duplex_check)

        self.ocr_check = QCheckBox("تمكين التعرف الضوئي على الحروف (OCR)")
        self.ocr_check.setChecked(True)
        settings_layout.addRow("", self.ocr_check)

        layout.addWidget(settings_group)

        # معاينة المسح
        preview_group = QGroupBox("معاينة")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_label = QLabel("لم يتم إجراء مسح بعد")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setMinimumHeight(200)
        preview_layout.addWidget(self.preview_label)

        layout.addWidget(preview_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        scan_btn = QPushButton("مسح")
        scan_btn.clicked.connect(self.start_scan)
        buttons_layout.addWidget(scan_btn)

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_scanned_file)
        save_btn.setEnabled(False)
        self.save_btn = save_btn
        buttons_layout.addWidget(save_btn)

        # إضافة زر استيراد مباشرة
        import_btn = QPushButton("استيراد مباشرة")
        import_btn.clicked.connect(self.import_scanned_file)
        import_btn.setEnabled(False)
        self.import_btn = import_btn
        buttons_layout.addWidget(import_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def start_scan(self):
        """بدء عملية المسح الضوئي"""
        try:
            # الحصول على إعدادات المسح
            document_type = self.document_type_combo.currentText()
            resolution = self.resolution_combo.currentText().split()[0]  # استخراج الرقم فقط
            color_mode = self.color_mode_combo.currentText()
            file_format = self.file_format_combo.currentText().lower()
            duplex = self.duplex_check.isChecked()
            ocr = self.ocr_check.isChecked()

            # عرض رسالة للمستخدم
            QMessageBox.information(
                self,
                "جارٍ المسح",
                f"سيتم بدء المسح الضوئي بالإعدادات التالية:\n\n" \
                f"نوع المستند: {document_type}\n" \
                f"الدقة: {resolution} DPI\n" \
                f"وضع اللون: {color_mode}\n" \
                f"صيغة الملف: {file_format.upper()}\n" \
                f"مسح على الوجهين: {'نعم' if duplex else 'لا'}\n" \
                f"OCR: {'نعم' if ocr else 'لا'}\n\n" \
                "سيتم فتح أداة المسح الضوئي الافتراضية. بعد الانتهاء من المسح، احفظ الملف وعد إلى هنا."
            )

            # استدعاء أداة المسح الضوئي الافتراضية (Windows Fax and Scan)
            scan_command = "wiaacmgr"
            subprocess.Popen(scan_command, shell=True)

            # في التطبيق الحقيقي، يمكن استخدام مكتبة متخصصة للمسح الضوئي مثل twain
            # لكن لأغراض العرض، سنقوم بمحاكاة المسح الضوئي

            # تفعيل زر الحفظ والاستيراد المباشر
            self.save_btn.setEnabled(True)
            self.import_btn.setEnabled(True)

            # تحديث المعاينة (في التطبيق الحقيقي، سيتم عرض الصورة الممسوحة)
            self.preview_label.setText("تم المسح الضوئي بنجاح. انقر على زر 'حفظ' لحفظ الملف أو 'استيراد مباشرة' لاستيراده للبرنامج.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في المسح الضوئي: {str(e)}")

    def save_scanned_file(self):
        """حفظ الملف الممسوح ضوئيًا"""
        try:
            # الحصول على اسم الملف وصيغته
            filename = self.filename_input.text().strip()
            if not filename:
                filename = f"scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            file_format = self.file_format_combo.currentText().lower()

            # السماح للمستخدم باختيار موقع الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الملف الممسوح ضوئيًا",
                f"{filename}.{file_format}",
                f"{file_format.upper()} Files (*.{file_format});;All Files (*.*)"
            )

            if file_path:
                # في التطبيق الحقيقي، سيتم حفظ الملف الممسوح ضوئيًا هنا
                # لكن لأغراض العرض، سنقوم بإنشاء ملف فارغ
                with open(file_path, 'w') as f:
                    f.write("This is a simulated scanned file.")

                self.scanned_file_path = file_path
                QMessageBox.information(self, "نجاح", f"تم حفظ الملف بنجاح في:\n{file_path}")
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الملف: {str(e)}")

    def import_scanned_file(self):
        """استيراد الملف الممسوح ضوئيًا مباشرة إلى البرنامج"""
        try:
            # التحقق من وجود قاعدة البيانات
            if not self.db:
                QMessageBox.warning(self, "خطأ", "لا يمكن استيراد الملف: قاعدة البيانات غير متوفرة")
                return

            # الحصول على اسم الملف وصيغته
            filename = self.filename_input.text().strip()
            if not filename:
                filename = f"scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            file_format = self.file_format_combo.currentText().lower()

            # إنشاء مسار مؤقت للملف
            import tempfile
            import os

            temp_dir = os.path.join("storage", "temp")
            os.makedirs(temp_dir, exist_ok=True)

            temp_file_path = os.path.join(temp_dir, f"{filename}.{file_format}")

            # في التطبيق الحقيقي، سيتم حفظ الملف الممسوح ضوئيًا هنا
            # لكن لأغراض العرض، سنقوم بإنشاء ملف فارغ
            with open(temp_file_path, 'w') as f:
                f.write("This is a simulated scanned file.")

            # استيراد الملف إلى نظام التخزين
            file_info = self.file_handler.import_file(temp_file_path)

            # إنشاء نافذة حوار لإدخال معلومات المستند
            dialog = QDialog(self)
            dialog.setWindowTitle("استيراد ملف ممسوح ضوئيًا")
            dialog.setMinimumWidth(400)

            layout = QVBoxLayout(dialog)

            form_layout = QFormLayout()

            # العنوان
            title_input = QLineEdit(filename)
            form_layout.addRow("العنوان:", title_input)

            # الوصف
            description_input = QTextEdit()
            description_input.setMaximumHeight(80)
            form_layout.addRow("الوصف:", description_input)

            # الكلمات المفتاحية
            keywords_input = QLineEdit()
            form_layout.addRow("الكلمات المفتاحية:", keywords_input)

            # اختيار التصنيف
            category_combo = QComboBox()
            category_combo.addItem("بدون تصنيف", None)

            # إضافة التصنيفات
            categories = self.db.get_all_categories()
            for category in categories:
                # إذا كان التصنيف فرعياً، أضف اسم التصنيف الرئيسي قبل اسمه
                if 'parent_name' in category and category['parent_name']:
                    display_name = f"{category['parent_name']} / {category['name']}"
                else:
                    display_name = category['name']

                category_combo.addItem(display_name, category['id'])

            form_layout.addRow("التصنيف:", category_combo)

            layout.addLayout(form_layout)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            buttons_layout.addWidget(cancel_btn)

            save_btn = QPushButton("استيراد")
            save_btn.clicked.connect(dialog.accept)
            buttons_layout.addWidget(save_btn)

            layout.addLayout(buttons_layout)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                title = title_input.text().strip()
                description = description_input.toPlainText().strip()
                keywords = keywords_input.text().strip()
                category_id = category_combo.currentData()

                # إضافة المستند إلى قاعدة البيانات
                document_id = self.db.add_document(
                    title=title,
                    description=description,
                    file_path=file_info['file_path'],
                    file_type=file_info['file_type'],
                    file_size=file_info['file_size'],
                    original_filename=file_info['original_filename'],
                    creation_date=file_info['creation_date'],
                    modification_date=file_info['modification_date'],
                    keywords=keywords
                )

                # تعيين التصنيف
                if category_id:
                    self.db.assign_category_to_document(document_id, category_id)

                # حفظ معرف المستند المستورد
                self.imported_document_id = document_id
                self.scanned_file_path = file_info['file_path']

                QMessageBox.information(self, "نجاح", "تم استيراد الملف الممسوح ضوئيًا بنجاح")
                self.accept()
            else:
                # حذف الملف المؤقت إذا تم إلغاء الاستيراد
                try:
                    os.remove(temp_file_path)
                except:
                    pass

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في استيراد الملف: {str(e)}")