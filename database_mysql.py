# -*- coding: utf-8 -*-
import pymysql
from datetime import datetime
from db_config import DB_CONFIG

class Database:
    """إدارة قاعدة البيانات لبرنامج الأرشفة باستخدام PyMySQL"""

    def __init__(self):
        """تهيئة الاتصال بقاعدة البيانات"""
        self.db_config = DB_CONFIG
        self.conn = None
        self.cursor = None
        self._connect_and_create_db()
        if self.conn:
            self.create_tables()

    def _connect_and_create_db(self):
        """الاتصال بالسيرفر وإنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            db_name = self.db_config['database']
            # الاتصال بالسيرفر بدون تحديد قاعدة بيانات
            temp_config = self.db_config.copy()
            temp_config.pop('database', None)
            # Ensure port is an integer
            if 'port' in temp_config:
                temp_config['port'] = int(temp_config['port'])

            temp_conn = pymysql.connect(**temp_config)
            
            with temp_conn.cursor() as temp_cursor:
                temp_cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            
            temp_conn.close()

            # الآن الاتصال بقاعدة البيانات المحددة
            # Ensure port is an integer for the main connection as well
            if 'port' in self.db_config:
                self.db_config['port'] = int(self.db_config['port'])
            self.conn = pymysql.connect(**self.db_config, cursorclass=pymysql.cursors.DictCursor)
            self.cursor = self.conn.cursor()
            print("تم الاتصال بقاعدة بيانات MySQL بنجاح.")
        except pymysql.Error as err:
            print(f"خطأ فادح في الاتصال بـ MySQL: {err}")
            self.conn = None
            self.cursor = None

    def connect(self):
        """إعادة الاتصال إذا انقطع الاتصال"""
        if not self.conn or not self.conn.open:
            try:
                # Ensure port is an integer
                if 'port' in self.db_config:
                    self.db_config['port'] = int(self.db_config['port'])
                self.conn = pymysql.connect(**self.db_config, cursorclass=pymysql.cursors.DictCursor)
                self.cursor = self.conn.cursor()
            except pymysql.Error as err:
                print(f"فشل إعادة الاتصال: {err}")
                self.conn = None
                self.cursor = None
        return self.conn is not None

    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.cursor:
            self.cursor.close()
        if self.conn and self.conn.open:
            self.conn.close()

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات إذا لم تكن موجودة (بصيغة MySQL)"""
        tables = {}
        tables['categories'] = ("""
        CREATE TABLE IF NOT EXISTS `categories` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `name` VARCHAR(255) NOT NULL UNIQUE,
            `description` TEXT,
            `parent_id` INT,
            `allowed_file_types` TEXT,
            `display_order` INT DEFAULT 0,
            FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""")
        tables['documents'] = ("""
        CREATE TABLE IF NOT EXISTS `documents` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `title` VARCHAR(255) NOT NULL,
            `description` TEXT,
            `file_path` VARCHAR(1024) NOT NULL,
            `file_type` VARCHAR(100),
            `file_size` BIGINT,
            `original_filename` VARCHAR(255),
            `import_date` DATETIME,
            `creation_date` DATETIME,
            `modification_date` DATETIME,
            `export_date` DATETIME,
            `keywords` TEXT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""")
        tables['document_categories'] = ("""
        CREATE TABLE IF NOT EXISTS `document_categories` (
            `document_id` INT,
            `category_id` INT,
            PRIMARY KEY (`document_id`, `category_id`),
            FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE,
            FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""")
        tables['custom_metadata'] = ("""
        CREATE TABLE IF NOT EXISTS `custom_metadata` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `document_id` INT,
            `field_name` VARCHAR(255) NOT NULL,
            `field_value` TEXT,
            FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""")
        try:
            for table_name, table_description in tables.items():
                self.cursor.execute(table_description)
            self.conn.commit()
        except pymysql.Error as err:
            print(f"فشل في إنشاء الجداول: {err}")
            self.conn.rollback()

    def add_document(self, title, file_path, description="", file_type="", file_size=0,
                    original_filename="", creation_date=None, modification_date=None, keywords=""):
        import_date = datetime.now()
        query = ("INSERT INTO documents "
                 "(title, description, file_path, file_type, file_size, original_filename, import_date, creation_date, modification_date, keywords) "
                 "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)")
        data = (title, description, file_path, file_type, file_size, original_filename, import_date, creation_date, modification_date, keywords)
        self.cursor.execute(query, data)
        self.conn.commit()
        return self.cursor.lastrowid

    def add_category(self, name, description="", parent_id=None, allowed_file_types=None):
        if not parent_id or str(parent_id).strip() == '0':
            parent_id = None
        query_order = "SELECT MAX(display_order) as max_order FROM categories WHERE parent_id <=> %s"
        self.cursor.execute(query_order, (parent_id,))
        max_order_result = self.cursor.fetchone()
        max_order = max_order_result['max_order'] if max_order_result else None
        new_order = (max_order or -1) + 1
        try:
            query = ("INSERT INTO categories (name, description, parent_id, allowed_file_types, display_order) "
                     "VALUES (%s, %s, %s, %s, %s)")
            data = (name, description, parent_id, allowed_file_types, new_order)
            self.cursor.execute(query, data)
            self.conn.commit()
            return self.cursor.lastrowid
        except pymysql.IntegrityError:
            self.conn.rollback()
            return None

    def assign_category_to_document(self, document_id, category_id):
        try:
            query = "INSERT INTO document_categories (document_id, category_id) VALUES (%s, %s)"
            self.cursor.execute(query, (document_id, category_id))
            self.conn.commit()
            return True
        except pymysql.IntegrityError:
            self.conn.rollback()
            return False

    def get_document(self, document_id):
        self.cursor.execute("SELECT * FROM documents WHERE id = %s", (document_id,))
        return self.cursor.fetchone()

    def get_documents(self, category_id=None, search_term=None, limit=100, offset=0):
        if not self.connect(): return []
        base_query = "SELECT * FROM documents"
        where_clauses = []
        params = []
        if category_id:
            base_query = "SELECT d.* FROM documents d JOIN document_categories dc ON d.id = dc.document_id"
            where_clauses.append("dc.category_id = %s")
            params.append(category_id)
        if search_term:
            where_clauses.append("(title LIKE %s OR description LIKE %s OR keywords LIKE %s)")
            params.extend([f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"])
        if where_clauses:
            base_query += " WHERE " + " AND ".join(where_clauses)
        
        # -- تعديل: ترتيب المستندات بحيث يظهر الأحدث أولاً --
        base_query += " ORDER BY id DESC"

        base_query += " LIMIT %s OFFSET %s"
        params.extend([limit, offset])
        self.cursor.execute(base_query, tuple(params))
        return self.cursor.fetchall()

    def get_categories(self, parent_id=None):
        self.cursor.execute("SELECT * FROM categories WHERE parent_id <=> %s ORDER BY display_order, name", (parent_id,))
        return self.cursor.fetchall()

    def get_all_categories(self):
        self.cursor.execute("SELECT * FROM categories ORDER BY parent_id, display_order, name")
        return self.cursor.fetchall()

    def update_document(self, document_id, **kwargs):
        if not kwargs:
            return False
        set_clause = ", ".join([f"`{key}` = %s" for key in kwargs])
        params = list(kwargs.values())
        params.append(document_id)
        query = f"UPDATE documents SET {set_clause} WHERE id = %s"
        self.cursor.execute(query, params)
        self.conn.commit()
        return self.cursor.rowcount > 0

    def delete_document(self, document_id):
        self.cursor.execute("DELETE FROM documents WHERE id = %s", (document_id,))
        self.conn.commit()
        return self.cursor.rowcount > 0
        
    def delete_category(self, category_id):
        self.cursor.execute("DELETE FROM categories WHERE id = %s", (category_id,))
        self.conn.commit()
        return self.cursor.rowcount > 0

    def get_category(self, category_id):
        self.cursor.execute("SELECT * FROM categories WHERE id = %s", (category_id,))
        return self.cursor.fetchone()

    def update_category(self, category_id, **kwargs):
        if not kwargs:
            return False
        set_clause = ", ".join([f"`{key}` = %s" for key in kwargs])
        params = list(kwargs.values())
        params.append(category_id)
        query = f"UPDATE categories SET {set_clause} WHERE id = %s"
        self.cursor.execute(query, params)
        self.conn.commit()
        return self.cursor.rowcount > 0

    def get_document_categories(self, document_id):
        query = ("SELECT c.* FROM categories c "
                 "JOIN document_categories dc ON c.id = dc.category_id "
                 "WHERE dc.document_id = %s")
        self.cursor.execute(query, (document_id,))
        return self.cursor.fetchall()

    def get_custom_metadata(self, document_id):
        query = "SELECT field_name, field_value FROM custom_metadata WHERE document_id = %s"
        self.cursor.execute(query, (document_id,))
        return self.cursor.fetchall()

    def add_custom_metadata(self, document_id, field_name, field_value):
        query = "INSERT INTO custom_metadata (document_id, field_name, field_value) VALUES (%s, %s, %s)"
        self.cursor.execute(query, (document_id, field_name, field_value))
        self.conn.commit()
        return self.cursor.lastrowid

    def get_documents_stats(self, period_type, start_date=None, end_date=None):
        if period_type == 'daily': date_format = '%Y-%m-%d'
        elif period_type == 'weekly': date_format = '%Y-%U'
        elif period_type == 'monthly': date_format = '%Y-%m'
        elif period_type == 'yearly': date_format = '%Y'
        else: return {}

        query = f"SELECT DATE_FORMAT(import_date, '{date_format}') as period, COUNT(*) as document_count, SUM(file_size) as total_size FROM documents"
        params = []
        where_clauses = []
        if start_date and end_date:
            where_clauses.append("import_date BETWEEN %s AND %s")
            params.extend([f"{start_date} 00:00:00", f"{end_date} 23:59:59"])
        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)
        query += " GROUP BY period ORDER BY period"
        self.cursor.execute(query, tuple(params))
        results = self.cursor.fetchall()

        stats = {
            'periods': [row['period'] for row in results],
            'document_counts': [row['document_count'] for row in results],
            'total_sizes': [row['total_size'] for row in results],
            'total_documents': sum(row['document_count'] for row in results),
            'total_size': sum(row['total_size'] or 0 for row in results),
            'files_details': []
        }

        if start_date and end_date:
            files_query = ("""
            SELECT d.id, d.title, d.file_path, d.file_type, d.file_size, d.import_date, d.original_filename, c.name as category_name
            FROM documents d
            LEFT JOIN document_categories dc ON d.id = dc.document_id
            LEFT JOIN categories c ON dc.category_id = c.id
            WHERE d.import_date BETWEEN %s AND %s ORDER BY d.import_date DESC""")
            self.cursor.execute(files_query, (f"{start_date} 00:00:00", f"{end_date} 23:59:59"))
            stats['files_details'] = self.cursor.fetchall()

        return stats
