"""
أداة لإنشاء أيقونات العرض اللازمة للبرنامج
"""

import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QIcon, QPixmap, QPainter, QColor, QPen, QBrush
from PyQt6.QtCore import Qt, QSize, QRect, QPoint

def create_view_icon():
    """إنشاء أيقونة العرض الرئيسية"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار
    painter.setPen(QPen(QColor("#3f51b5"), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
    painter.drawRoundedRect(5, 5, 54, 54, 10, 10)
    
    # رسم أيقونات صغيرة تمثل العرض
    painter.setPen(QPen(QColor("#3f51b5"), 2))
    
    # رسم مربعات تمثل الأيقونات
    painter.draw<PERSON>ect(12, 12, 16, 16)
    painter.drawRect(36, 12, 16, 16)
    painter.drawRect(12, 36, 16, 16)
    painter.drawRect(36, 36, 16, 16)
    
    painter.end()
    return pixmap

def create_view_large_icon():
    """إنشاء أيقونة العرض بأيقونات كبيرة"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار
    painter.setPen(QPen(QColor("#3f51b5"), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
    painter.drawRoundedRect(5, 5, 54, 54, 10, 10)
    
    # رسم أيقونات كبيرة
    painter.setPen(QPen(QColor("#3f51b5"), 2))
    painter.drawRect(12, 12, 40, 40)
    
    painter.end()
    return pixmap

def create_view_medium_icon():
    """إنشاء أيقونة العرض بأيقونات متوسطة"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار
    painter.setPen(QPen(QColor("#3f51b5"), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
    painter.drawRoundedRect(5, 5, 54, 54, 10, 10)
    
    # رسم أيقونات متوسطة
    painter.setPen(QPen(QColor("#3f51b5"), 2))
    painter.drawRect(12, 12, 20, 20)
    painter.drawRect(36, 12, 20, 20)
    painter.drawRect(12, 36, 20, 20)
    
    painter.end()
    return pixmap

def create_view_small_icon():
    """إنشاء أيقونة العرض بأيقونات صغيرة"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار
    painter.setPen(QPen(QColor("#3f51b5"), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
    painter.drawRoundedRect(5, 5, 54, 54, 10, 10)
    
    # رسم أيقونات صغيرة
    painter.setPen(QPen(QColor("#3f51b5"), 2))
    painter.drawRect(10, 10, 12, 12)
    painter.drawRect(26, 10, 12, 12)
    painter.drawRect(42, 10, 12, 12)
    painter.drawRect(10, 26, 12, 12)
    painter.drawRect(26, 26, 12, 12)
    painter.drawRect(42, 26, 12, 12)
    
    painter.end()
    return pixmap

def create_view_list_icon():
    """إنشاء أيقونة العرض كقائمة"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار
    painter.setPen(QPen(QColor("#3f51b5"), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
    painter.drawRoundedRect(5, 5, 54, 54, 10, 10)
    
    # رسم خطوط تمثل القائمة
    painter.setPen(QPen(QColor("#3f51b5"), 2))
    painter.drawLine(12, 15, 52, 15)
    painter.drawLine(12, 25, 52, 25)
    painter.drawLine(12, 35, 52, 35)
    painter.drawLine(12, 45, 52, 45)
    
    painter.end()
    return pixmap

def create_view_details_icon():
    """إنشاء أيقونة العرض بالتفاصيل"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار
    painter.setPen(QPen(QColor("#3f51b5"), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
    painter.drawRoundedRect(5, 5, 54, 54, 10, 10)
    
    # رسم خطوط تمثل القائمة مع تفاصيل
    painter.setPen(QPen(QColor("#3f51b5"), 2))
    
    # الصف الأول
    painter.drawRect(12, 12, 10, 10)
    painter.drawLine(26, 17, 52, 17)
    
    # الصف الثاني
    painter.drawRect(12, 27, 10, 10)
    painter.drawLine(26, 32, 52, 32)
    
    # الصف الثالث
    painter.drawRect(12, 42, 10, 10)
    painter.drawLine(26, 47, 52, 47)
    
    painter.end()
    return pixmap

def create_categories_icon():
    """إنشاء أيقونة التصنيفات"""
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم إطار
    painter.setPen(QPen(QColor("#3f51b5"), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
    painter.drawRoundedRect(5, 5, 54, 54, 10, 10)
    
    # رسم رمز المجلد
    painter.setPen(QPen(QColor("#3f51b5"), 2))
    painter.setBrush(QBrush(QColor("#8c9eff")))
    
    # رسم المجلد الأول
    painter.drawRect(10, 12, 30, 5)
    painter.drawRoundedRect(10, 12, 44, 25, 3, 3)
    
    # رسم المجلد الثاني
    painter.drawRect(15, 42, 25, 5)
    painter.drawRoundedRect(15, 42, 39, 17, 3, 3)
    
    painter.end()
    return pixmap

def save_icons():
    """حفظ جميع الأيقونات"""
    app = QApplication([])
    
    resources_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources")
    
    # إنشاء وحفظ الأيقونات
    create_view_icon().save(os.path.join(resources_dir, "view.png"))
    create_view_large_icon().save(os.path.join(resources_dir, "view_large.png"))
    create_view_medium_icon().save(os.path.join(resources_dir, "view_medium.png"))
    create_view_small_icon().save(os.path.join(resources_dir, "view_small.png"))
    create_view_list_icon().save(os.path.join(resources_dir, "view_list.png"))
    create_view_details_icon().save(os.path.join(resources_dir, "view_details.png"))
    create_categories_icon().save(os.path.join(resources_dir, "categories.png"))
    
    print("تم إنشاء الأيقونات بنجاح!")

if __name__ == "__main__":
    save_icons()
