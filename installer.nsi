; تعريف اسم البرنامج والإصدار
!define APPNAME "أرشفة"
!define COMPANYNAME "أرشفة"
!define DESCRIPTION "برنامج أرشفة المستندات"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define ABOUTURL ""

; تضمين الملفات الأساسية
!include "MUI2.nsh"
!include "FileFunc.nsh"

; تعريف اسم ملف التثبيت
Name "${APPNAME}"
OutFile "Output\أرشفة_تثبيت.exe"
InstallDir "$PROGRAMFILES\${APPNAME}"
InstallDirRegKey HKCU "Software\${APPNAME}" ""

; طلب صلاحيات المسؤول
RequestExecutionLevel admin

; تعريف واجهة المستخدم
!define MUI_ICON "resources\logo.png.ico"
!define MUI_UNICON "resources\logo.png.ico"
!define MUI_WELCOMEFINISHPAGE_BITMAP "resources\logo.png.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "resources\logo.png.ico"
!define MUI_ABORTWARNING

; صفحات التثبيت
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; صفحات إلغاء التثبيت
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; اللغات
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

; قسم التثبيت
; تعريف اسم الملف التنفيذي من خلال المتغير الخارجي
!ifndef EXE_FILE
  !define EXE_FILE "أرشفة.exe"
!endif

Section "Install"
    SetOutPath "$INSTDIR"
    
    ; نسخ الملف التنفيذي
    File "dist\${EXE_FILE}"
    
    ; إنشاء مجلد للبيانات
    CreateDirectory "$LOCALAPPDATA\${APPNAME}"
    
    ; إنشاء اختصار في قائمة البرامج
    CreateDirectory "$SMPROGRAMS\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\${EXE_FILE}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\إلغاء التثبيت.lnk" "$INSTDIR\uninstall.exe"
    
    ; إنشاء اختصار على سطح المكتب
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\${EXE_FILE}"
    
    ; إنشاء ملف إلغاء التثبيت
    WriteUninstaller "$INSTDIR\uninstall.exe"
    
    ; تسجيل البرنامج في نظام التشغيل
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$\"$INSTDIR$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayIcon" "$\"$INSTDIR\${EXE_FILE}$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMinor" ${VERSIONMINOR}
    
    ; تعيين صلاحيات الوصول لمجلد البيانات
    AccessControl::GrantOnFile "$LOCALAPPDATA\${APPNAME}" "(BU)" "FullAccess"
SectionEnd

; قسم إلغاء التثبيت
Section "Uninstall"
    ; حذف الملفات
    Delete "$INSTDIR\${EXE_FILE}"
    Delete "$INSTDIR\uninstall.exe"
    
    ; حذف الاختصارات
    Delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\إلغاء التثبيت.lnk"
    Delete "$DESKTOP\${APPNAME}.lnk"
    
    ; حذف المجلدات
    RMDir "$SMPROGRAMS\${APPNAME}"
    RMDir "$INSTDIR"
    
    ; حذف معلومات التسجيل
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
SectionEnd
