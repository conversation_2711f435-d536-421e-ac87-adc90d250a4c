@echo off
setlocal enabledelayedexpansion

echo ===== بدء عملية إنشاء ملف التثبيت البسيط =====

echo.
echo 1. إنشاء مجلد الإخراج إذا لم يكن موجوداً
if not exist "Output" mkdir "Output"

echo.
echo 2. التحقق من وجود ملف EXE
dir "dist" /b > temp_files.txt
findstr /i ".exe" temp_files.txt > exe_files.txt
set /p EXE_FILE=<exe_files.txt
del temp_files.txt
del exe_files.txt

if "%EXE_FILE%"=="" (
    echo ملف EXE غير موجود. يرجى تشغيل build_onefile.bat أولاً لإنشاء الملف التنفيذي.
    pause
    exit /b 1
) else (
    echo تم العثور على الملف التنفيذي: %EXE_FILE%
)

echo.
echo 3. إنشاء مجلد التثبيت المؤقت
if exist "Temp_Install" rmdir /s /q "Temp_Install"
mkdir "Temp_Install"

echo.
echo 4. نسخ الملفات اللازمة
copy "dist\%EXE_FILE%" "Temp_Install\%EXE_FILE%" > nul
copy "resources\logo.png.ico" "Temp_Install\logo.ico" > nul

echo.
echo 5. إنشاء ملف التثبيت
echo @echo off > "Temp_Install\install.bat"
echo echo ===== تثبيت برنامج أرشفة ===== >> "Temp_Install\install.bat"
echo echo. >> "Temp_Install\install.bat"
echo echo 1. إنشاء مجلد البرنامج >> "Temp_Install\install.bat"
echo if not exist "%%ProgramFiles%%\أرشفة" mkdir "%%ProgramFiles%%\أرشفة" >> "Temp_Install\install.bat"
echo. >> "Temp_Install\install.bat"
echo echo 2. نسخ ملفات البرنامج >> "Temp_Install\install.bat"
echo copy "%%~dp0%EXE_FILE%" "%%ProgramFiles%%\أرشفة\%EXE_FILE%" >> "Temp_Install\install.bat"
echo copy "%%~dp0logo.ico" "%%ProgramFiles%%\أرشفة\logo.ico" >> "Temp_Install\install.bat"
echo. >> "Temp_Install\install.bat"
echo echo 3. إنشاء مجلد البيانات >> "Temp_Install\install.bat"
echo if not exist "%%LOCALAPPDATA%%\أرشفة" mkdir "%%LOCALAPPDATA%%\أرشفة" >> "Temp_Install\install.bat"
echo. >> "Temp_Install\install.bat"
echo echo 4. إنشاء اختصار على سطح المكتب >> "Temp_Install\install.bat"
echo echo Set oWS = WScript.CreateObject^("WScript.Shell"^) ^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo echo sLinkFile = oWS.SpecialFolders^("Desktop"^) ^& "\أرشفة.lnk" ^>^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo echo Set oLink = oWS.CreateShortcut^(sLinkFile^) ^>^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.TargetPath = "%%ProgramFiles%%\أرشفة\%EXE_FILE%" ^>^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.WorkingDirectory = "%%ProgramFiles%%\أرشفة" ^>^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.Description = "برنامج أرشفة المستندات" ^>^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.IconLocation = "%%ProgramFiles%%\أرشفة\logo.ico" ^>^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.Save ^>^> "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo cscript //nologo "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo del "%%TEMP%%\shortcut.vbs" >> "Temp_Install\install.bat"
echo. >> "Temp_Install\install.bat"
echo echo 5. إنشاء اختصار في قائمة البرامج >> "Temp_Install\install.bat"
echo if not exist "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة" mkdir "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة" >> "Temp_Install\install.bat"
echo echo Set oWS = WScript.CreateObject^("WScript.Shell"^) ^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo echo sLinkFile = "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة\أرشفة.lnk" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo echo Set oLink = oWS.CreateShortcut^(sLinkFile^) ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.TargetPath = "%%ProgramFiles%%\أرشفة\%EXE_FILE%" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.WorkingDirectory = "%%ProgramFiles%%\أرشفة" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.Description = "برنامج أرشفة المستندات" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.IconLocation = "%%ProgramFiles%%\أرشفة\logo.ico" ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo echo oLink.Save ^>^> "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo cscript //nologo "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo del "%%TEMP%%\menu_shortcut.vbs" >> "Temp_Install\install.bat"
echo. >> "Temp_Install\install.bat"
echo echo ===== تم تثبيت البرنامج بنجاح ===== >> "Temp_Install\install.bat"
echo echo يمكنك الآن تشغيل البرنامج من سطح المكتب أو من قائمة البرامج. >> "Temp_Install\install.bat"
echo echo. >> "Temp_Install\install.bat"
echo pause >> "Temp_Install\install.bat"

echo.
echo 6. إنشاء ملف إلغاء التثبيت
echo @echo off > "Temp_Install\uninstall.bat"
echo echo ===== إلغاء تثبيت برنامج أرشفة ===== >> "Temp_Install\uninstall.bat"
echo echo. >> "Temp_Install\uninstall.bat"
echo echo 1. حذف اختصار سطح المكتب >> "Temp_Install\uninstall.bat"
echo del "%%USERPROFILE%%\Desktop\أرشفة.lnk" >> "Temp_Install\uninstall.bat"
echo. >> "Temp_Install\uninstall.bat"
echo echo 2. حذف اختصار قائمة البرامج >> "Temp_Install\uninstall.bat"
echo rmdir /s /q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\أرشفة" >> "Temp_Install\uninstall.bat"
echo. >> "Temp_Install\uninstall.bat"
echo echo 3. حذف ملفات البرنامج >> "Temp_Install\uninstall.bat"
echo rmdir /s /q "%%ProgramFiles%%\أرشفة" >> "Temp_Install\uninstall.bat"
echo. >> "Temp_Install\uninstall.bat"
echo echo ===== تم إلغاء تثبيت البرنامج بنجاح ===== >> "Temp_Install\uninstall.bat"
echo echo. >> "Temp_Install\uninstall.bat"
echo pause >> "Temp_Install\uninstall.bat"

echo.
echo 7. إنشاء ملف تشغيل تلقائي
echo @echo off > "Temp_Install\autorun.inf"
echo [autorun] >> "Temp_Install\autorun.inf"
echo open=install.bat >> "Temp_Install\autorun.inf"
echo icon=logo.ico >> "Temp_Install\autorun.inf"
echo label=تثبيت برنامج أرشفة >> "Temp_Install\autorun.inf"

echo.
echo 8. إنشاء ملف readme
echo # برنامج أرشفة > "Temp_Install\readme.txt"
echo. >> "Temp_Install\readme.txt"
echo ## تعليمات التثبيت >> "Temp_Install\readme.txt"
echo. >> "Temp_Install\readme.txt"
echo 1. قم بتشغيل ملف install.bat لتثبيت البرنامج >> "Temp_Install\readme.txt"
echo 2. سيتم إنشاء اختصار على سطح المكتب وفي قائمة البرامج >> "Temp_Install\readme.txt"
echo 3. لإلغاء التثبيت، قم بتشغيل ملف uninstall.bat >> "Temp_Install\readme.txt"

echo.
echo 9. إنشاء حزمة التثبيت النهائية
powershell -Command "& {Add-Type -A 'System.IO.Compression.FileSystem'; [IO.Compression.ZipFile]::CreateFromDirectory('Temp_Install', 'Output\أرشفة_تثبيت.zip');}"

echo.
echo 10. إعادة تسمية الملف
ren "Output\أرشفة_تثبيت.zip" "أرشفة_تثبيت.exe"

echo.
echo 11. تنظيف الملفات المؤقتة
rmdir /s /q "Temp_Install"

echo.
echo ===== تم إنشاء ملف التثبيت بنجاح =====
echo يمكنك العثور على ملف التثبيت في: %CD%\Output\أرشفة_تثبيت.exe
echo.

pause
