import os
import hashlib
import json
import time
from datetime import datetime, timedelta


class LicenseManager:
    """مدير التراخيص للتحقق من صحة المفتاح التسلسلي والترخيص"""

    def __init__(self):
        # استخدام مجلد AppData لتخزين ملفات الترخيص
        app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'أرشفة')
        if not os.path.exists(app_data_dir):
            os.makedirs(app_data_dir)
        self.license_file = os.path.join(app_data_dir, "license.dat")
        self.trial_file = os.path.join(app_data_dir, "trial.dat")
        self.hwid = self._get_hardware_id()
        self.trial_days = 10  # مدة الفترة التجريبية بالأيام

    def _get_hardware_id(self):
        """إنشاء معرّف فريد للجهاز"""
        try:
            # استخدام معلومات النظام لإنشاء معرّف فريد
            import platform
            system_info = platform.node() + platform.processor() + platform.machine()
            # إنشاء هاش للتأكد من أن المعرّف لا يمكن تزويره بسهولة
            return hashlib.md5(system_info.encode()).hexdigest()
        except:
            # في حالة حدوث خطأ، استخدم اسم المستخدم الحالي
            return hashlib.md5(os.path.expanduser("~").encode()).hexdigest()

    def validate_key(self, license_key):
        """التحقق من صحة المفتاح التسلسلي

        المفتاح يجب أن يكون بتنسيق XXX-XXX-XXX-XXX حيث XXX هي حروف أو أرقام
        """
        # التحقق من تنسيق المفتاح
        if not self._check_key_format(license_key):
            return False

        # الخوارزمية البسيطة للتحقق من صحة المفتاح
        # في بيئة الإنتاج، يمكنك استخدام خوارزمية أكثر تعقيدًا وآمنة
        parts = license_key.split('-')

        # التحقق من أن الجزء الأول هو "ARC"
        if parts[0] != "ARC":
            return False

        # التحقق من الرقم التسلسلي (الجزأين الثاني والثالث)
        checksum = sum(ord(c) for c in parts[1] + parts[2])
        expected_checksum = int(parts[3], 16)

        # التحقق من أن الجزء الرابع يمثل المجموع الصحيح
        if checksum % 256 != expected_checksum:
            return False

        return True

    def _check_key_format(self, key):
        """التحقق من تنسيق المفتاح"""
        if not key or not isinstance(key, str):
            return False

        # المفتاح يجب أن يكون في تنسيق XXX-XXX-XXX-XXX
        parts = key.split('-')
        if len(parts) != 4:
            return False

        for part in parts:
            if len(part) != 3:
                return False
            if not all(c.isalnum() for c in part):
                return False

        return True

    def save_license(self, license_key):
        """حفظ المفتاح التسلسلي في ملف الترخيص"""
        license_data = {
            "key": license_key,
            "hwid": self.hwid,
            "activation_date": datetime.now().strftime("%Y-%m-%d"),
            "status": "active"
        }

        try:
            with open(self.license_file, 'w') as f:
                json.dump(license_data, f)
            return True
        except:
            return False

    def is_licensed_permanent(self):
        """التحقق من وجود ترخيص دائم فقط"""
        if os.path.exists(self.license_file):
            try:
                with open(self.license_file, 'r') as f:
                    license_data = json.load(f)

                # التحقق من أن المفتاح مرتبط بهذا الجهاز
                if license_data.get("hwid") != self.hwid:
                    return False

                # التحقق من صحة المفتاح
                if not self.validate_key(license_data.get("key")):
                    return False

                return True
            except:
                pass
        return False

    def is_licensed(self):
        """التحقق مما إذا كان البرنامج مرخصًا بالفعل"""
        # أولاً، التحقق من وجود ترخيص دائم
        if self.is_licensed_permanent():
            return True

        # ثانياً، التحقق من وجود فترة تجريبية سارية
        return self.is_trial_valid()

    def has_trial_been_used(self):
        """التحقق مما إذا كانت الفترة التجريبية قد تم استخدامها من قبل"""
        if not os.path.exists(self.trial_file):
            return False

        try:
            with open(self.trial_file, 'r') as f:
                trial_data = json.load(f)

            # التحقق من أن الفترة التجريبية مرتبطة بهذا الجهاز
            if trial_data.get("hwid") != self.hwid:
                return False

            # إذا كان الملف موجود ولكن لا يحتوي على حقل "used"،
            # فهذا يعني أنه ملف قديم وقد تم استخدام الفترة التجريبية
            if "used" not in trial_data:
                # تحديث الملف لإضافة حقل "used"
                trial_data["used"] = True
                try:
                    with open(self.trial_file, 'w') as f:
                        json.dump(trial_data, f)
                except:
                    pass
                return True

            # التحقق من وجود علامة الاستخدام
            return trial_data.get("used", False)
        except:
            return False

    def get_license_info(self):
        """الحصول على معلومات الترخيص"""
        # التحقق من وجود ترخيص دائم
        if os.path.exists(self.license_file):
            try:
                with open(self.license_file, 'r') as f:
                    license_data = json.load(f)
                license_data["type"] = "full"
                return license_data
            except:
                pass

        # التحقق من وجود فترة تجريبية
        if self.is_trial_valid():
            trial_data = self.get_trial_info()
            trial_data["type"] = "trial"
            return trial_data

        return None

    def generate_key(self, seed=None):
        """إنشاء مفتاح تسلسلي للاختبار (في بيئة الإنتاج، يجب إنشاء المفاتيح بشكل أكثر أمانًا)"""
        import random
        seed = seed or random.randint(1000, 9999)
        random.seed(seed)

        # إنشاء مفتاح بتنسيق ARC-XXX-XXX-XXX
        part1 = "ARC"
        part2 = ''.join(random.choices("0123456789ABCDEF", k=3))
        part3 = ''.join(random.choices("0123456789ABCDEF", k=3))

        # حساب المجموع للتحقق
        checksum = sum(ord(c) for c in part2 + part3)
        part4 = format(checksum % 256, '03X')

        return f"{part1}-{part2}-{part3}-{part4}"

    def start_trial(self):
        """بدء الفترة التجريبية"""
        # التحقق من وجود ترخيص دائم
        if self.is_licensed_permanent():
            return False

        # التحقق من وجود فترة تجريبية سارية
        if self.is_trial_valid():
            return False

        # التحقق من استخدام الفترة التجريبية سابقاً
        if self.has_trial_been_used():
            return False

        trial_data = {
            "hwid": self.hwid,
            "start_date": datetime.now().strftime("%Y-%m-%d"),
            "end_date": (datetime.now() + timedelta(days=self.trial_days)).strftime("%Y-%m-%d"),
            "status": "active",
            "used": True  # إضافة علامة لتتبع الاستخدام
        }

        try:
            with open(self.trial_file, 'w') as f:
                json.dump(trial_data, f)
            return True
        except:
            return False

    def is_trial_valid(self):
        """التحقق مما إذا كانت الفترة التجريبية سارية المفعول"""
        if not os.path.exists(self.trial_file):
            return False

        try:
            with open(self.trial_file, 'r') as f:
                trial_data = json.load(f)

            # التحقق من أن الفترة التجريبية مرتبطة بهذا الجهاز
            if trial_data.get("hwid") != self.hwid:
                return False

            # التحقق من تاريخ انتهاء الفترة التجريبية
            end_date = datetime.strptime(trial_data.get("end_date"), "%Y-%m-%d")
            if datetime.now() > end_date:
                return False

            return True
        except:
            return False

    def get_trial_info(self):
        """الحصول على معلومات الفترة التجريبية"""
        if not os.path.exists(self.trial_file):
            return None

        try:
            with open(self.trial_file, 'r') as f:
                trial_data = json.load(f)

            # حساب الأيام المتبقية
            end_date = datetime.strptime(trial_data.get("end_date"), "%Y-%m-%d")
            days_left = (end_date - datetime.now()).days + 1
            if days_left < 0:
                days_left = 0

            trial_data["days_left"] = days_left
            return trial_data
        except:
            return None
